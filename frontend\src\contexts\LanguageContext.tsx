import React, { createContext, useContext, ReactNode } from 'react';

// Arabic translations for the entire application
export const translations = {
  // Navigation
  nav: {
    title: 'نظام التوقيع الإلكتروني',
    dashboard: 'لوحة التحكم',
    signatureUpload: 'رفع التوقيع',
    documentSigning: 'توقيع المستندات',
    history: 'السجل',
    logout: 'تسجيل الخروج'
  },

  // Authentication
  auth: {
    login: 'تسجيل الدخول',
    register: 'إنشاء حساب جديد',
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    confirmPassword: 'تأكيد كلمة المرور',
    loginButton: 'دخول',
    registerButton: 'إنشاء حساب',
    loggingIn: 'جاري تسجيل الدخول...',
    creatingAccount: 'جاري إنشاء الحساب...',
    noAccount: 'ليس لديك حساب؟',
    hasAccount: 'لديك حساب بالفعل؟',
    registerHere: 'سجل هنا',
    loginHere: 'سجل دخولك هنا',
    passwordRequirement: 'يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل مع أرقام وحروف'
  },

  // Biometric Authentication
  biometric: {
    // General
    title: 'المصادقة البيومترية',
    subtitle: 'تسجيل الدخول بالبصمة أو الوجه',
    notSupported: 'المصادقة البيومترية غير مدعومة',
    notAvailable: 'المصادقة البيومترية غير متاحة',
    enabled: 'مفعلة',
    disabled: 'غير مفعلة',

    // Login
    loginWith: 'تسجيل الدخول بـ',
    faceId: 'Face ID',
    touchId: 'Touch ID',
    fingerprint: 'بصمة الإصبع',
    windowsHello: 'Windows Hello',
    authenticating: 'جاري المصادقة...',
    authSuccess: 'تم تسجيل الدخول بنجاح',
    authFailed: 'فشل في المصادقة البيومترية',

    // Enrollment
    setup: 'إعداد المصادقة البيومترية',
    setupSubtitle: 'قم بإعداد المصادقة البيومترية للدخول السريع والآمن',
    benefits: 'المزايا',
    benefit1: 'دخول سريع في ثانية واحدة',
    benefit2: 'أمان عالي ومشفر',
    benefit3: 'لا حاجة لتذكر كلمة المرور',
    benefit4: 'بياناتك محفوظة على جهازك فقط',

    // Privacy
    privacy: 'الخصوصية والأمان',
    privacySubtitle: 'معلومات مهمة حول حماية بياناتك',
    guarantee1: 'بياناتك البيومترية لا تغادر جهازك أبداً',
    guarantee2: 'نحفظ فقط مفتاح التشفير العام',
    guarantee3: 'يمكنك إلغاء التفعيل في أي وقت',
    guarantee4: 'كلمة المرور تبقى متاحة كبديل',

    // Instructions
    instructions: 'التعليمات',
    faceIdInstruction: 'انظر إلى الكاميرا الأمامية لجهازك',
    touchIdInstruction: 'ضع إصبعك على زر الهوم',
    fingerprintInstruction: 'ضع إصبعك على مستشعر البصمة',
    followDevice: 'اتبع التعليمات التي تظهر على جهازك',

    // Management
    management: 'إدارة المصادقة البيومترية',
    devices: 'الأجهزة المسجلة',
    noDevices: 'لا توجد أجهزة مسجلة',
    addDevice: 'إضافة جهاز جديد',
    removeDevice: 'حذف الجهاز',
    deviceInfo: 'معلومات الجهاز',
    lastUsed: 'آخر استخدام',
    registeredOn: 'تاريخ التسجيل',
    trustLevel: 'مستوى الثقة',

    // Trust Levels
    trusted: 'موثوق',
    verified: 'محقق',
    unverified: 'غير محقق',
    suspicious: 'مشبوه',

    // Preferences
    preferences: 'التفضيلات',
    preferredMethod: 'طريقة المصادقة المفضلة',
    passwordOnly: 'كلمة المرور فقط',
    biometricOnly: 'المصادقة البيومترية فقط',
    both: 'كلاهما (مرونة أكثر)',

    // Status
    status: 'الحالة',
    currentStatus: 'الحالة الحالية',
    enrolledOn: 'تاريخ التسجيل',

    // Actions
    continue: 'المتابعة',
    cancel: 'إلغاء',
    agree: 'أوافق وأريد المتابعة',
    startEnrollment: 'بدء التسجيل',
    retry: 'إعادة المحاولة',
    save: 'حفظ',
    remove: 'حذف',

    // Messages
    enrollmentSuccess: 'تم تسجيل المصادقة البيومترية بنجاح',
    enrollmentFailed: 'فشل في تسجيل المصادقة البيومترية',
    removalSuccess: 'تم حذف المصادقة البيومترية بنجاح',
    removalFailed: 'فشل في حذف المصادقة البيومترية',
    preferencesUpdated: 'تم تحديث التفضيلات بنجاح',

    // Errors
    userCancelled: 'تم إلغاء العملية من قبل المستخدم',
    notAllowed: 'تم رفض الإذن للمصادقة البيومترية',
    securityError: 'خطأ أمني في المصادقة البيومترية',
    invalidState: 'بيانات الاعتماد موجودة بالفعل',
    networkError: 'خطأ في الشبكة',
    serverError: 'خطأ في الخادم',

    // Help
    help: 'المساعدة',
    helpInfo1: 'بياناتك البيومترية محفوظة بأمان على جهازك فقط',
    helpInfo2: 'يمكنك استخدام أجهزة متعددة للمصادقة البيومترية',
    helpInfo3: 'كلمة المرور ستبقى متاحة دائماً كبديل',
    helpInfo4: 'يمكنك إلغاء المصادقة البيومترية في أي وقت'
  },

  // Dashboard
  dashboard: {
    welcome: 'مرحباً بك مرة أخرى',
    subtitle: 'إدارة التوقيعات والمستندات من لوحة التحكم',
    totalSignatures: 'إجمالي التوقيعات',
    signedDocuments: 'المستندات الموقعة',
    status: 'الحالة',
    active: 'نشط',
    quickActions: 'الإجراءات السريعة',
    uploadSignature: 'رفع توقيع جديد',
    signDocument: 'توقيع مستند',
    viewHistory: 'عرض السجل',
    recentDocuments: 'المستندات الأخيرة',
    noDocuments: 'لا توجد مستندات موقعة بعد',
    startSigning: 'ابدأ بتوقيع مستندك الأول!'
  },

  // Signature Upload
  signatureUpload: {
    title: 'رفع التوقيع',
    subtitle: 'قم برفع صورة توقيعك لاستخدامها في توقيع المستندات',
    dropZone: 'اسحب ملف التوقيع هنا، أو',
    browse: 'تصفح',
    supportedFormats: 'يدعم ملفات PNG و JPG و JPEG و SVG حتى 5 ميجابايت',
    uploading: 'جاري رفع التوقيع...',
    yourSignatures: 'توقيعاتك',
    noSignatures: 'لم يتم رفع أي توقيعات بعد',
    uploadFirst: 'قم برفع توقيعك الأول لبدء توقيع المستندات',
    uploadedOn: 'تم الرفع في',
    fileSize: 'حجم الملف',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التوقيع؟'
  },

  // Document Signing
  documentSigning: {
    title: 'توقيع المستندات',
    subtitle: '',
    selectDocument: 'اختر مستند PDF',
    selectSignature: 'اختر التوقيع',
    chooseSignature: 'اختر توقيعاً',
    signaturePosition: 'موضع التوقيع (اختياري)',
    xCoordinate: 'الإحداثي السيني (X)',
    yCoordinate: 'الإحداثي الصادي (Y)',
    signDocument: 'توقيع المستند',
    signing: 'جاري التوقيع...',
    instructions: 'كيفية توقيع المستند',
    step1: 'قم برفع مستند PDF عن طريق السحب والإفلات أو النقر على تصفح',
    step2: 'اضبط موضع التوقيع إذا لزم الأمر (اختياري)',
    step3: 'انقر على "توقيع المستند" لتضمين توقيعك',
    step4: 'سيتم حفظ المستند الموقع برقم تسلسلي فريد',
    step5: 'سيتم استخدام أول توقيع متاح تلقائياً',
    note: 'ملاحظة',
    noteText: 'يحصل كل مستند موقع على رقم تسلسلي فريد للتحقق وأغراض التدقيق.'
  },

  // History
  history: {
    title: 'سجل المستندات',
    subtitle: 'عرض جميع المستندات التي قمت بتوقيعها',
    document: 'المستند',
    serialNumber: 'الرقم التسلسلي',
    signedDate: 'تاريخ التوقيع',
    fileSize: 'حجم الملف',
    actions: 'الإجراءات',
    download: 'تحميل',
    view: 'عرض',
    noDocuments: 'لا توجد مستندات موقعة',
    startSigning: 'ابدأ بتوقيع مستندك الأول',
    loading: 'جاري التحميل...',
    page: 'الصفحة',
    of: 'من',
    previous: 'السابق',
    next: 'التالي'
  },

  // Common
  common: {
    loading: 'جاري التحميل...',
    error: 'خطأ',
    success: 'نجح',
    cancel: 'إلغاء',
    confirm: 'تأكيد',
    save: 'حفظ',
    edit: 'تعديل',
    delete: 'حذف',
    close: 'إغلاق',
    back: 'رجوع',
    next: 'التالي',
    previous: 'السابق',
    submit: 'إرسال',
    reset: 'إعادة تعيين',
    search: 'بحث',
    filter: 'تصفية',
    sort: 'ترتيب',
    refresh: 'تحديث'
  },

  // Error Messages
  errors: {
    required: 'هذا الحقل مطلوب',
    invalidEmail: 'البريد الإلكتروني غير صحيح',
    passwordMismatch: 'كلمات المرور غير متطابقة',
    passwordTooShort: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
    fileTooLarge: 'حجم الملف كبير جداً',
    invalidFileType: 'نوع الملف غير مدعوم',
    uploadFailed: 'فشل في رفع الملف',
    networkError: 'خطأ في الشبكة',
    serverError: 'خطأ في الخادم',
    unauthorized: 'غير مخول للوصول',
    notFound: 'غير موجود',
    signatureMissing: 'يرجى اختيار توقيع',
    documentMissing: 'يرجى اختيار مستند للتوقيع',
    signingFailed: 'فشل في توقيع المستند',
    deleteFailed: 'فشل في الحذف',
    loadFailed: 'فشل في التحميل'
  },

  // Success Messages
  success: {
    loginSuccess: 'تم تسجيل الدخول بنجاح',
    registerSuccess: 'تم إنشاء الحساب بنجاح',
    signatureUploaded: 'تم رفع التوقيع بنجاح',
    documentSigned: 'تم توقيع المستند بنجاح',
    signatureDeleted: 'تم حذف التوقيع بنجاح',
    documentDownloaded: 'تم تحميل المستند بنجاح'
  },

  // File validation
  fileValidation: {
    pdfOnly: 'يرجى اختيار ملف PDF',
    imageOnly: 'يرجى اختيار ملف صورة (PNG, JPG, JPEG, SVG)',
    maxSize: 'يجب أن يكون حجم الملف أقل من',
    mb: 'ميجابايت'
  }
};

interface LanguageContextType {
  t: typeof translations;
  isRTL: boolean;
  language: string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const value = {
    t: translations,
    isRTL: true,
    language: 'ar'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
