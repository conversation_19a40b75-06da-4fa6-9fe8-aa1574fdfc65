const twilio = require('twilio');
const { query } = require('../models/database');

// Initialize Twilio client
let twilioClient = null;

const initializeTwilio = () => {
  if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
    console.warn('⚠ Twilio credentials not configured - WhatsApp notifications disabled');
    return null;
  }

  try {
    twilioClient = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    console.log('✓ Twilio WhatsApp client initialized');
    return twilioClient;
  } catch (error) {
    console.error('Failed to initialize Twilio client:', error);
    return null;
  }
};

// Configuration
const WHATSAPP_CONFIG = {
  enabled: process.env.WHATSAPP_NOTIFICATIONS_ENABLED === 'true',
  fromNumber: process.env.TWILIO_WHATSAPP_FROM || 'whatsapp:+***********',
  adminNumbers: process.env.WHATSAPP_ADMIN_NUMBERS ? 
    process.env.WHATSAPP_ADMIN_NUMBERS.split(',').map(num => num.trim()) : [],
  retryAttempts: parseInt(process.env.WHATSAPP_RETRY_ATTEMPTS) || 3,
  retryDelay: parseInt(process.env.WHATSAPP_RETRY_DELAY) || 5000
};

/**
 * Validate phone number format (international format with country code)
 */
const validatePhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return false;
  
  // Remove any whitespace and special characters except +
  const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
  
  // Check if it starts with + and has 10-15 digits
  const phoneRegex = /^\+[1-9]\d{9,14}$/;
  return phoneRegex.test(cleanNumber);
};

/**
 * Format phone number for WhatsApp (add whatsapp: prefix if not present)
 */
const formatWhatsAppNumber = (phoneNumber) => {
  if (!phoneNumber) return null;
  
  const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
  
  if (!validatePhoneNumber(cleanNumber)) {
    throw new Error(`Invalid phone number format: ${phoneNumber}`);
  }
  
  return cleanNumber.startsWith('whatsapp:') ? cleanNumber : `whatsapp:${cleanNumber}`;
};

/**
 * Send WhatsApp message with retry logic
 */
const sendWhatsAppMessage = async (to, message, retryCount = 0) => {
  if (!WHATSAPP_CONFIG.enabled) {
    console.log('WhatsApp notifications disabled');
    return { success: false, reason: 'disabled' };
  }

  if (!twilioClient) {
    twilioClient = initializeTwilio();
    if (!twilioClient) {
      return { success: false, reason: 'twilio_not_configured' };
    }
  }

  try {
    const formattedTo = formatWhatsAppNumber(to);
    
    const messageResult = await twilioClient.messages.create({
      from: WHATSAPP_CONFIG.fromNumber,
      to: formattedTo,
      body: message
    });

    console.log(`✓ WhatsApp message sent successfully to ${formattedTo}:`, messageResult.sid);
    
    return {
      success: true,
      messageSid: messageResult.sid,
      status: messageResult.status,
      to: formattedTo
    };

  } catch (error) {
    console.error(`Failed to send WhatsApp message to ${to} (attempt ${retryCount + 1}):`, error);

    // Retry logic
    if (retryCount < WHATSAPP_CONFIG.retryAttempts - 1) {
      console.log(`Retrying WhatsApp message in ${WHATSAPP_CONFIG.retryDelay}ms...`);
      
      await new Promise(resolve => setTimeout(resolve, WHATSAPP_CONFIG.retryDelay));
      return sendWhatsAppMessage(to, message, retryCount + 1);
    }

    return {
      success: false,
      error: error.message,
      code: error.code,
      retryCount: retryCount + 1
    };
  }
};

/**
 * Send notification to multiple recipients
 */
const sendBulkWhatsAppNotification = async (recipients, message) => {
  if (!recipients || recipients.length === 0) {
    return { success: false, reason: 'no_recipients' };
  }

  const results = [];
  
  for (const recipient of recipients) {
    try {
      const result = await sendWhatsAppMessage(recipient, message);
      results.push({
        recipient,
        ...result
      });
      
      // Small delay between messages to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      results.push({
        recipient,
        success: false,
        error: error.message
      });
    }
  }

  const successCount = results.filter(r => r.success).length;
  
  return {
    success: successCount > 0,
    totalSent: successCount,
    totalFailed: results.length - successCount,
    results
  };
};

/**
 * Log notification attempt to database
 */
const logNotificationAttempt = async (userId, documentId, recipients, message, result) => {
  try {
    await query(
      `INSERT INTO notification_logs (
        user_id, document_id, notification_type, recipients, message_content, 
        success, result_data, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)`,
      [
        userId,
        documentId,
        'whatsapp',
        JSON.stringify(recipients),
        message,
        result.success,
        JSON.stringify(result)
      ]
    );
  } catch (error) {
    console.error('Failed to log notification attempt:', error);
  }
};

/**
 * Get user's phone number from database
 */
const getUserPhoneNumber = async (userId) => {
  try {
    const result = await query(
      'SELECT phone_number FROM users WHERE id = $1 AND phone_number IS NOT NULL',
      [userId]
    );
    
    return result.rows.length > 0 ? result.rows[0].phone_number : null;
  } catch (error) {
    console.error('Failed to get user phone number:', error);
    return null;
  }
};

/**
 * Get all configured notification recipients (user + admins)
 */
const getNotificationRecipients = async (userId) => {
  const recipients = [];
  
  // Add user's phone number if available
  const userPhone = await getUserPhoneNumber(userId);
  if (userPhone && validatePhoneNumber(userPhone)) {
    recipients.push(userPhone);
  }
  
  // Add admin numbers from configuration
  for (const adminNumber of WHATSAPP_CONFIG.adminNumbers) {
    if (validatePhoneNumber(adminNumber)) {
      recipients.push(adminNumber);
    }
  }
  
  return [...new Set(recipients)]; // Remove duplicates
};

module.exports = {
  initializeTwilio,
  sendWhatsAppMessage,
  sendBulkWhatsAppNotification,
  logNotificationAttempt,
  getUserPhoneNumber,
  getNotificationRecipients,
  validatePhoneNumber,
  formatWhatsAppNumber,
  WHATSAPP_CONFIG
};
