const express = require('express');
const { 
  upload, 
  uploadSignature, 
  getSignatures, 
  getSignature, 
  deleteSignature 
} = require('../controllers/signatureController');
const { authenticateToken } = require('../middleware/auth');
const { requirePermission } = require('../middleware/roleAuth');

const router = express.Router();

// All signature routes require authentication
router.use(authenticateToken);

// Upload signature (admin only)
router.post('/upload', requirePermission('upload_signatures'), upload, uploadSignature);

// Get all signatures (admin only)
router.get('/', requirePermission('upload_signatures'), getSignatures);

// Get specific signature (admin only)
router.get('/:signatureId', requirePermission('upload_signatures'), getSignature);

// Delete signature (admin only)
router.delete('/:signatureId', requirePermission('upload_signatures'), deleteSignature);

module.exports = router;
