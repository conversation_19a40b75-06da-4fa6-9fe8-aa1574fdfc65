-- Arabic-Only Conversion Migration
-- This migration converts the existing multilingual system to Arabic-only

-- Add Arabic language support columns to existing tables
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS full_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS language VARCHAR(3) DEFAULT 'ar',
ADD COLUMN IF NOT EXISTS text_direction VARCHAR(3) DEFAULT 'rtl';

ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS language VARCHAR(3) DEFAULT 'ar',
ADD COLUMN IF NOT EXISTS text_direction VARCHAR(3) DEFAULT 'rtl';

ALTER TABLE signatures 
ADD COLUMN IF NOT EXISTS text_direction VARCHAR(3) DEFAULT 'rtl';

-- Create Arabic templates table
CREATE TABLE IF NOT EXISTS arabic_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_type VARCHAR(50) NOT NULL,
    arabic_content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert Arabic templates
INSERT INTO arabic_templates (template_type, arabic_content) VALUES
('email_verification', 'مرحباً، يرجى تأكيد عنوان بريدك الإلكتروني'),
('document_signed', 'تم توقيع المستند بنجاح'),
('signature_uploaded', 'تم رفع التوقيع بنجاح'),
('error_invalid_file', 'نوع الملف غير صالح'),
('error_upload_failed', 'فشل في رفع الملف'),
('login_success', 'تم تسجيل الدخول بنجاح'),
('login_failed', 'البريد الإلكتروني أو كلمة المرور غير صحيحة'),
('register_success', 'تم إنشاء الحساب بنجاح'),
('register_failed', 'فشل في إنشاء الحساب'),
('document_upload_success', 'تم رفع المستند بنجاح'),
('document_upload_failed', 'فشل في رفع المستند'),
('signature_verification', 'موقع رقمياً'),
('document_integrity', 'تم التحقق من سلامة الوثيقة'),
('serial_number_label', 'الرقم التسلسلي للوثيقة'),
('signed_date_label', 'تم التوقيع'),
('server_running', 'الخادم يعمل بشكل طبيعي'),
('rate_limit_exceeded', 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً')
ON CONFLICT (template_type) DO UPDATE SET 
    arabic_content = EXCLUDED.arabic_content,
    updated_at = CURRENT_TIMESTAMP;

-- Create trigger for arabic_templates table
CREATE TRIGGER update_arabic_templates_updated_at 
    BEFORE UPDATE ON arabic_templates 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for Arabic templates
CREATE INDEX IF NOT EXISTS idx_arabic_templates_type ON arabic_templates(template_type);

-- Migration function to convert existing data to Arabic-only
CREATE OR REPLACE FUNCTION migrate_to_arabic_only()
RETURNS void AS $$
BEGIN
    -- Update existing users to Arabic language and RTL direction
    UPDATE users SET 
        language = 'ar',
        text_direction = 'rtl'
    WHERE language IS NULL OR language != 'ar' OR text_direction IS NULL OR text_direction != 'rtl';
    
    -- Update existing documents to Arabic language and RTL direction
    UPDATE documents SET 
        language = 'ar',
        text_direction = 'rtl'
    WHERE language IS NULL OR language != 'ar' OR text_direction IS NULL OR text_direction != 'rtl';
    
    -- Update existing signatures to RTL direction
    UPDATE signatures SET 
        text_direction = 'rtl'
    WHERE text_direction IS NULL OR text_direction != 'rtl';
    
    -- Update existing serial numbers to Arabic format if they follow old pattern
    UPDATE documents SET 
        serial_number = CONCAT('وثيقة-', SUBSTRING(serial_number, 5))
    WHERE serial_number LIKE 'ESG-%' OR serial_number LIKE 'DOC-%';
    
    RAISE NOTICE 'Migration to Arabic-only completed successfully';
END;
$$ LANGUAGE plpgsql;

-- Execute the migration
SELECT migrate_to_arabic_only();

-- Drop the migration function after use
DROP FUNCTION IF EXISTS migrate_to_arabic_only();

-- Add comments to document the Arabic-only schema
COMMENT ON TABLE users IS 'Users table with Arabic language support (ar) and RTL text direction';
COMMENT ON TABLE documents IS 'Documents table with Arabic language support and RTL text direction';
COMMENT ON TABLE signatures IS 'Signatures table with RTL text direction support';
COMMENT ON TABLE arabic_templates IS 'Arabic text templates for the application';

COMMENT ON COLUMN users.language IS 'User language preference (always ar for Arabic)';
COMMENT ON COLUMN users.text_direction IS 'Text direction preference (always rtl for Arabic)';
COMMENT ON COLUMN documents.language IS 'Document language (always ar for Arabic)';
COMMENT ON COLUMN documents.text_direction IS 'Document text direction (always rtl for Arabic)';
COMMENT ON COLUMN signatures.text_direction IS 'Signature text direction (always rtl for Arabic)';
