#!/usr/bin/env node

/**
 * Add full_name column to users table
 */

const { query } = require('./backend/src/models/database');

async function addFullNameColumn() {
  try {
    console.log('🔧 Adding full_name column to users table...\n');

    // Check if full_name column already exists
    const columnCheck = await query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'full_name'
    `);

    if (columnCheck.rows.length > 0) {
      console.log('✅ full_name column already exists');
      return;
    }

    // Add full_name column
    await query(`
      ALTER TABLE users 
      ADD COLUMN full_name VARCHAR(255)
    `);

    console.log('✅ Successfully added full_name column to users table');

    // Verify the column was added
    const verifyCheck = await query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `);

    console.log('\n📋 Updated users table columns:');
    verifyCheck.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // Add some sample full names for existing users (optional)
    console.log('\n🔄 Updating existing users with sample names...');
    
    const existingUsers = await query('SELECT id, email FROM users');
    
    for (const user of existingUsers.rows) {
      // Generate a sample name based on email
      const emailPart = user.email.split('@')[0];
      const sampleName = emailPart.charAt(0).toUpperCase() + emailPart.slice(1);
      
      await query(
        'UPDATE users SET full_name = $1 WHERE id = $2',
        [sampleName, user.id]
      );
      
      console.log(`   ✅ Updated ${user.email} with name: ${sampleName}`);
    }

    console.log('\n🎯 Migration completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the migration
addFullNameColumn();
