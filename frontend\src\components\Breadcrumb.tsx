import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

interface BreadcrumbItem {
  label: string;
  path: string;
  isActive?: boolean;
}

const Breadcrumb: React.FC = () => {
  const location = useLocation();
  const { t } = useLanguage();

  const getBreadcrumbItems = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(segment => segment);
    const items: BreadcrumbItem[] = [];

    // Always start with dashboard
    items.push({
      label: t.navigation.dashboard,
      path: '/dashboard',
      isActive: location.pathname === '/dashboard'
    });

    // Map path segments to breadcrumb items
    const pathMap: { [key: string]: string } = {
      'signature-upload': 'رفع التوقيع',
      'document-signing': 'توقيع المستندات',
      'sign': 'توقيع المستندات',
      'history': 'السجل',
      'users': 'إدارة المستخدمين',
      'settings': 'الإعدادات',
      'verify': 'التحقق من الرقم التسلسلي',
      'mail': 'البريد',
      'admin': 'الإدارة',
      'records': 'السجلات',
      'signing-confirmation': 'تأكيد التوقيع',
      'signing-error': 'خطأ في التوقيع',
      'pdf-test': 'اختبار PDF'
    };

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      if (pathMap[segment]) {
        const isLast = index === pathSegments.length - 1;
        items.push({
          label: pathMap[segment],
          path: currentPath,
          isActive: isLast
        });
      }
    });

    return items;
  };

  const breadcrumbItems = getBreadcrumbItems();

  // Don't show breadcrumb on login/register pages
  if (location.pathname === '/login' || location.pathname === '/register') {
    return null;
  }

  return (
    <nav className="bg-white border-b border-gray-200 px-4 py-3 mb-6">
      <div className="container mx-auto">
        <ol className="flex items-center space-x-2 space-x-reverse text-sm">
          <li>
            <Link 
              to="/" 
              className="text-gray-500 hover:text-gray-700 flex items-center"
            >
              <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
              الرئيسية
            </Link>
          </li>
          
          {breadcrumbItems.map((item, index) => (
            <li key={item.path} className="flex items-center">
              <svg className="w-4 h-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              
              {item.isActive ? (
                <span className="text-gray-900 font-medium">
                  {item.label}
                </span>
              ) : (
                <Link 
                  to={item.path}
                  className="text-blue-600 hover:text-blue-800"
                >
                  {item.label}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  );
};

export default Breadcrumb;
