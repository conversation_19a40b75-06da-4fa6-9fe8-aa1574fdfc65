#!/usr/bin/env node

/**
 * Specific test for the PDF viewer loading issue
 * Tests the exact flow that DocumentViewer follows
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIwZjFlYmIzZS03NWY2LTQ0ZGQtOGFiYS1iZjJlM2U4NTdhMDQiLCJpYXQiOjE3NTI2ODQ4MjcsImV4cCI6MTc1Mjc3MTIyN30.fHhH70Nf1L11XiXq6EvHzlOg71x2B-qFkmQL9PpZVS4';

console.log('🔍 Testing PDF Viewer Specific Issue');
console.log('====================================\n');

async function testDocumentViewerFlow() {
  try {
    console.log('1. Getting signed documents...');
    const documentsResponse = await axios.get(`${API_BASE_URL}/documents`, {
      headers: { Authorization: `Bearer ${TEST_TOKEN}` }
    });

    const signedDocs = documentsResponse.data.documents.filter(doc => doc.status === 'signed');
    if (signedDocs.length === 0) {
      console.log('❌ No signed documents found');
      return;
    }

    const testDoc = signedDocs[0];
    console.log(`✅ Using document: ${testDoc.original_filename} (ID: ${testDoc.id})`);

    console.log('\n2. Testing DocumentViewer fetch flow...');
    
    // Step 1: Simulate DocumentViewer fetchPdfData
    console.log('   Step 1: Fetching PDF data (like fetchPdfData does)...');
    const pdfUrl = `${API_BASE_URL}/documents/${testDoc.id}/view?token=${encodeURIComponent(TEST_TOKEN)}`;
    
    const response = await fetch(pdfUrl);
    console.log(`   ✅ Response: ${response.status} ${response.statusText}`);
    console.log(`   ✅ Content-Type: ${response.headers.get('content-type')}`);
    console.log(`   ✅ Content-Length: ${response.headers.get('content-length')} bytes`);

    // Step 2: Convert to ArrayBuffer (like DocumentViewer does)
    console.log('   Step 2: Converting to ArrayBuffer...');
    const arrayBuffer = await response.arrayBuffer();
    console.log(`   ✅ ArrayBuffer size: ${arrayBuffer.byteLength} bytes`);

    // Step 3: Verify PDF format (like DocumentViewer does)
    console.log('   Step 3: Verifying PDF format...');
    const uint8Array = new Uint8Array(arrayBuffer);
    const headerBytes = Array.from(uint8Array.slice(0, 4));
    const pdfHeader = String.fromCharCode.apply(null, headerBytes);
    console.log(`   ✅ PDF Header: "${pdfHeader}" ${pdfHeader === '%PDF' ? '(Valid)' : '(Invalid)'}`);

    // Step 4: Create Blob URL (like DocumentViewer does)
    console.log('   Step 4: Creating Blob URL...');
    const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
    console.log(`   ✅ Blob created: ${blob.size} bytes, type: ${blob.type}`);
    
    // In browser, this would be: URL.createObjectURL(blob)
    console.log('   ✅ Blob URL would be created here (browser-only operation)');

    console.log('\n3. Simulating react-pdf Document component...');
    
    // Test if we can load this with PDF.js directly
    const pdfjsLib = require('pdfjs-dist/legacy/build/pdf.js');
    
    // Set worker (in Node.js environment)
    pdfjsLib.GlobalWorkerOptions.workerSrc = require.resolve('pdfjs-dist/legacy/build/pdf.worker.js');
    
    console.log('   Testing PDF.js loading...');
    try {
      const loadingTask = pdfjsLib.getDocument({ data: uint8Array });
      const pdf = await loadingTask.promise;
      console.log(`   ✅ PDF.js loaded successfully: ${pdf.numPages} pages`);
      
      // Test page rendering
      const page = await pdf.getPage(1);
      const viewport = page.getViewport({ scale: 1.0 });
      console.log(`   ✅ Page 1 viewport: ${viewport.width}x${viewport.height}`);
      
    } catch (pdfError) {
      console.log(`   ❌ PDF.js loading failed: ${pdfError.message}`);
      console.log(`   Error details:`, pdfError);
    }

    console.log('\n4. Checking for common issues...');
    
    // Check file size
    const fileSizeMB = arrayBuffer.byteLength / (1024 * 1024);
    console.log(`   File size: ${fileSizeMB.toFixed(2)} MB`);
    if (fileSizeMB > 50) {
      console.log('   ⚠️  Large file - might cause memory issues');
    } else {
      console.log('   ✅ File size is reasonable');
    }

    // Check for PDF structure
    const pdfString = new TextDecoder().decode(uint8Array.slice(0, 1000));
    const hasXref = pdfString.includes('xref');
    const hasTrailer = pdfString.includes('trailer');
    console.log(`   PDF structure - xref: ${hasXref ? '✅' : '❌'}, trailer: ${hasTrailer ? '✅' : '❌'}`);

    // Check end of file
    const endBytes = uint8Array.slice(-20);
    const endString = new TextDecoder().decode(endBytes);
    const hasEOF = endString.includes('%%EOF');
    console.log(`   PDF end marker: ${hasEOF ? '✅' : '❌'} (%%EOF)`);

    console.log('\n🎯 ANALYSIS');
    console.log('===========');
    console.log('✅ PDF data fetching works correctly');
    console.log('✅ PDF format is valid');
    console.log('✅ PDF.js can load the document');
    console.log('✅ File size is reasonable');
    console.log('');
    console.log('🔍 LIKELY ISSUES IN DOCUMENTVIEWER:');
    console.log('1. State management - loading/pdfData/error states');
    console.log('2. React component lifecycle - useEffect dependencies');
    console.log('3. Conditional rendering - Document component not mounting');
    console.log('4. PDF.js worker loading in browser environment');
    console.log('5. Blob URL creation or cleanup issues');
    console.log('');
    console.log('📋 DEBUGGING STEPS:');
    console.log('1. Check browser console for DocumentViewer state logs');
    console.log('2. Verify Document component is actually rendering');
    console.log('3. Check if onDocumentLoadSuccess is being called');
    console.log('4. Verify PDF.js worker is loading in browser');
    console.log('5. Test with SimplePDFTest component for comparison');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data:`, error.response.data);
    }
  }
}

// Run the test
testDocumentViewerFlow();
