import React from 'react';
import { Link } from 'react-router-dom';

interface AccessDeniedProps {
  feature?: string;
  message?: string;
}

const AccessDenied: React.FC<AccessDeniedProps> = ({
  feature = 'هذه الميزة',
  message
}) => {

  const defaultMessage = `${feature} متاحة للمديرين فقط. يرجى التواصل مع المدير للحصول على الصلاحيات المطلوبة.`;

  return (
    <div className="max-w-2xl mx-auto text-center py-12" dir="rtl">
      <div className="bg-white p-8 rounded-lg shadow-md">
        {/* Access Denied Icon */}
        <div className="mx-auto w-16 h-16 text-red-500 mb-6">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" 
            />
          </svg>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-800 mb-4 font-['Almarai']">
          غير مخول للوصول
        </h1>

        {/* Message */}
        <p className="text-gray-600 mb-8 font-['Almarai'] leading-relaxed">
          {message || defaultMessage}
        </p>

        {/* Role Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-semibold text-blue-800 mb-2 font-['Almarai']">
            معلومات الصلاحيات
          </h3>
          <div className="text-xs text-blue-700 space-y-1 font-['Almarai']">
            <p><strong>المدير:</strong> جميع الصلاحيات متاحة</p>
            <p><strong>المستخدم:</strong> توقيع المستندات وعرض السجل فقط</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/dashboard"
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-200 font-['Almarai']"
          >
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            العودة إلى لوحة التحكم
          </Link>
          
          <Link
            to="/document-signing"
            className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 font-['Almarai']"
          >
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            توقيع مستند
          </Link>
        </div>

        {/* Contact Information */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500 font-['Almarai']">
            هل تحتاج إلى صلاحيات إضافية؟ 
            <br />
            يرجى التواصل مع مدير النظام لطلب الصلاحيات المطلوبة.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AccessDenied;
