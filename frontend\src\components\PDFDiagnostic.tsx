import React, { useState, useEffect } from 'react';
import { pdfjs } from 'react-pdf';

// Import CSS files for react-pdf
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

interface PDFDiagnosticProps {
  documentId?: string;
}

const PDFDiagnostic: React.FC<PDFDiagnosticProps> = ({ documentId }) => {
  const [diagnostics, setDiagnostics] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addDiagnostic = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setDiagnostics(prev => [...prev, `[${timestamp}] ${icon} ${message}`]);
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    setDiagnostics([]);

    try {
      // Test 1: PDF.js Version and Worker
      addDiagnostic(`PDF.js version: ${pdfjs.version}`);
      addDiagnostic(`Worker URL: ${pdfjs.GlobalWorkerOptions.workerSrc}`);

      // Test 2: Check if worker is accessible
      try {
        const workerResponse = await fetch(pdfjs.GlobalWorkerOptions.workerSrc, { method: 'HEAD' });
        if (workerResponse.ok) {
          addDiagnostic('PDF.js worker is accessible', 'success');
        } else {
          addDiagnostic(`PDF.js worker not accessible: ${workerResponse.status}`, 'error');
        }
      } catch (error) {
        addDiagnostic(`PDF.js worker check failed: ${error}`, 'error');
      }

      // Test 3: Check localStorage token
      const token = localStorage.getItem('token');
      if (token) {
        addDiagnostic('Authentication token found', 'success');
        
        // Decode JWT to check expiration
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const isExpired = Date.now() >= payload.exp * 1000;
          addDiagnostic(`Token expires: ${new Date(payload.exp * 1000).toLocaleString()}`, isExpired ? 'error' : 'success');
        } catch (error) {
          addDiagnostic('Failed to decode token', 'error');
        }
      } else {
        addDiagnostic('No authentication token found', 'error');
      }

      // Test 4: Backend connectivity
      try {
        const healthResponse = await fetch('http://localhost:3001/health');
        if (healthResponse.ok) {
          addDiagnostic('Backend is accessible', 'success');
        } else {
          addDiagnostic(`Backend health check failed: ${healthResponse.status}`, 'error');
        }
      } catch (error) {
        addDiagnostic(`Backend connectivity failed: ${error}`, 'error');
      }

      // Test 5: Documents API
      if (token) {
        try {
          const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
          const documentsResponse = await fetch(`${API_BASE_URL}/documents`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          if (documentsResponse.ok) {
            const data = await documentsResponse.json();
            const signedDocs = data.documents?.filter((doc: any) => doc.status === 'signed') || [];
            addDiagnostic(`Found ${signedDocs.length} signed documents`, 'success');
            
            // Test 6: PDF download if we have a document
            if (signedDocs.length > 0 && documentId) {
              const testDoc = signedDocs.find((doc: any) => doc.id === documentId) || signedDocs[0];
              addDiagnostic(`Testing PDF download for: ${testDoc.original_filename}`);
              
              try {
                const pdfUrl = `${API_BASE_URL}/documents/${testDoc.id}/view?token=${encodeURIComponent(token)}`;
                const pdfResponse = await fetch(pdfUrl);
                
                if (pdfResponse.ok) {
                  const contentType = pdfResponse.headers.get('content-type');
                  const contentLength = pdfResponse.headers.get('content-length');
                  addDiagnostic(`PDF download successful: ${contentType}, ${contentLength} bytes`, 'success');
                  
                  // Test 7: PDF.js loading
                  const arrayBuffer = await pdfResponse.arrayBuffer();
                  const uint8Array = new Uint8Array(arrayBuffer);
                  
                  // Verify PDF header
                  const header = String.fromCharCode.apply(null, Array.from(uint8Array.slice(0, 4)));
                  if (header === '%PDF') {
                    addDiagnostic('PDF format verified', 'success');
                    
                    // Test PDF.js loading
                    try {
                      const loadingTask = pdfjs.getDocument({ data: uint8Array });
                      const pdf = await loadingTask.promise;
                      addDiagnostic(`PDF.js loaded successfully: ${pdf.numPages} pages`, 'success');
                    } catch (pdfError) {
                      addDiagnostic(`PDF.js loading failed: ${pdfError}`, 'error');
                    }
                  } else {
                    addDiagnostic(`Invalid PDF header: ${header}`, 'error');
                  }
                } else {
                  addDiagnostic(`PDF download failed: ${pdfResponse.status}`, 'error');
                }
              } catch (pdfError) {
                addDiagnostic(`PDF test failed: ${pdfError}`, 'error');
              }
            }
          } else {
            addDiagnostic(`Documents API failed: ${documentsResponse.status}`, 'error');
          }
        } catch (error) {
          addDiagnostic(`Documents API error: ${error}`, 'error');
        }
      }

      // Test 8: React-PDF specific checks
      addDiagnostic('Checking react-pdf configuration...');
      
      // Check if CSS is loaded
      const annotationCSS = document.querySelector('link[href*="AnnotationLayer.css"]') || 
                           document.querySelector('style[data-emotion*="annotation"]') ||
                           Array.from(document.styleSheets).some(sheet => {
                             try {
                               return Array.from(sheet.cssRules || []).some(rule => 
                                 rule.cssText?.includes('annotationLayer') || 
                                 rule.cssText?.includes('react-pdf')
                               );
                             } catch { return false; }
                           });
      
      addDiagnostic(`React-PDF CSS loaded: ${annotationCSS ? 'Yes' : 'No'}`, annotationCSS ? 'success' : 'error');

    } catch (error) {
      addDiagnostic(`Diagnostic error: ${error}`, 'error');
    } finally {
      setIsRunning(false);
    }
  };

  useEffect(() => {
    // Auto-run diagnostics on mount
    runDiagnostics();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [documentId]);

  return (
    <div className="p-4 bg-white border border-gray-300 rounded-lg shadow-sm font-['Almarai']" dir="rtl">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold text-gray-900">🔍 تشخيص عارض PDF</h3>
        <button
          onClick={runDiagnostics}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isRunning ? 'جاري التشخيص...' : 'إعادة التشخيص'}
        </button>
      </div>
      
      <div className="bg-gray-50 p-3 rounded border max-h-96 overflow-y-auto">
        {diagnostics.length === 0 && !isRunning && (
          <p className="text-gray-500">اضغط على "إعادة التشخيص" لبدء الفحص</p>
        )}
        
        {isRunning && (
          <p className="text-blue-600">🔄 جاري تشغيل التشخيص...</p>
        )}
        
        {diagnostics.map((diagnostic, index) => (
          <div key={index} className="mb-1 text-sm font-mono">
            {diagnostic}
          </div>
        ))}
      </div>
      
      {diagnostics.length > 0 && (
        <div className="mt-3 text-xs text-gray-600">
          تم إجراء {diagnostics.length} فحص. تحقق من الأخطاء المميزة بـ ❌
        </div>
      )}
    </div>
  );
};

export default PDFDiagnostic;
