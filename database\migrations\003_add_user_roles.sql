-- Add user roles to the system
-- This migration adds role-based access control

-- Add role column to users table
ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT 'user';

-- Create index for role column for better performance
CREATE INDEX idx_users_role ON users(role);

-- Update existing users to have admin role (first user becomes admin)
-- You can modify this based on your needs
UPDATE users SET role = 'admin' WHERE id = (
    SELECT id FROM users ORDER BY created_at ASC LIMIT 1
);

-- Add check constraint to ensure valid roles
ALTER TABLE users ADD CONSTRAINT check_user_role 
    CHECK (role IN ('admin', 'user'));

-- Add comments for documentation
COMMENT ON COLUMN users.role IS 'User role: admin (full access) or user (limited access)';

-- Create a function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_id AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql;

-- Create a function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS VARCHAR(20) AS $$
DECLARE
    user_role VARCHAR(20);
BEGIN
    SELECT role INTO user_role FROM users WHERE id = user_id;
    RETURN COALESCE(user_role, 'user');
END;
$$ LANGUAGE plpgsql;

-- Add trigger to update updated_at when role changes
CREATE OR REPLACE FUNCTION update_user_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_user_updated_at();
