import React, { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

// Import CSS files for react-pdf
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface SimplePDFTestProps {
  isOpen: boolean;
  onClose: () => void;
}

const SimplePDFTest: React.FC<SimplePDFTestProps> = ({ isOpen, onClose }) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [testStep, setTestStep] = useState<number>(0);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`SimplePDFTest: ${message}`);
  };

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    addLog(`✅ PDF loaded successfully with ${numPages} pages`);
    setNumPages(numPages);
    setLoading(false);
    setError('');
  };

  const onDocumentLoadError = (error: Error) => {
    addLog(`❌ PDF load error: ${error.message}`);
    setError(error.message);
    setLoading(false);
  };

  const onDocumentLoadProgress = ({ loaded, total }: { loaded: number; total: number }) => {
    const progress = total > 0 ? Math.round((loaded / total) * 100) : 0;
    addLog(`📊 Loading progress: ${progress}% (${loaded}/${total} bytes)`);
  };

  const testWithSamplePDF = async () => {
    setTestStep(1);
    setLogs([]);
    addLog('🧪 Testing with sample PDF URL...');
    
    // Use a simple PDF from the internet for testing
    const samplePdfUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
    
    try {
      addLog(`Attempting to load: ${samplePdfUrl}`);
      setLoading(true);
      setError('');
      
      // This will trigger the Document component to load
      setTestStep(2);
    } catch (error) {
      addLog(`❌ Error setting up test: ${error}`);
    }
  };

  const testWithLocalAPI = async () => {
    setTestStep(3);
    setLogs([]);
    addLog('🔗 Testing with local API...');
    
    const token = localStorage.getItem('token');
    if (!token) {
      addLog('❌ No authentication token found');
      return;
    }

    try {
      // Get documents first
      const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
      const response = await fetch(`${API_BASE_URL}/documents`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) {
        addLog(`❌ Documents API failed: ${response.status}`);
        return;
      }

      const data = await response.json();
      const signedDocs = data.documents?.filter((doc: any) => doc.status === 'signed') || [];
      
      if (signedDocs.length === 0) {
        addLog('❌ No signed documents found');
        return;
      }

      const testDoc = signedDocs[0];
      addLog(`📄 Found test document: ${testDoc.original_filename}`);
      
      const pdfUrl = `${API_BASE_URL}/documents/${testDoc.id}/view?token=${encodeURIComponent(token)}`;
      addLog(`🔗 PDF URL: ${pdfUrl}`);
      
      setLoading(true);
      setError('');
      setTestStep(4);
      
    } catch (error) {
      addLog(`❌ API test error: ${error}`);
    }
  };

  const resetTest = () => {
    setTestStep(0);
    setLogs([]);
    setNumPages(0);
    setPageNumber(1);
    setLoading(true);
    setError('');
  };

  if (!isOpen) return null;

  const getSamplePdfUrl = () => 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center" dir="rtl">
      <div className="bg-white w-11/12 h-5/6 max-w-4xl mx-4 rounded-lg shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-bold text-gray-900 font-['Almarai']">
            🧪 اختبار PDF بسيط
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-md"
            aria-label="إغلاق"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Controls */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex gap-2 mb-4">
            <button
              onClick={testWithSamplePDF}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 font-['Almarai']"
            >
              اختبار PDF عينة
            </button>
            <button
              onClick={testWithLocalAPI}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 font-['Almarai']"
            >
              اختبار API محلي
            </button>
            <button
              onClick={resetTest}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 font-['Almarai']"
            >
              إعادة تعيين
            </button>
          </div>
          
          <div className="text-sm text-gray-600 font-['Almarai']">
            الخطوة الحالية: {testStep === 0 ? 'لم يبدأ' : 
                           testStep === 1 ? 'إعداد PDF عينة' :
                           testStep === 2 ? 'تحميل PDF عينة' :
                           testStep === 3 ? 'إعداد API محلي' :
                           testStep === 4 ? 'تحميل PDF محلي' : 'غير معروف'}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex">
          {/* PDF Viewer */}
          <div className="flex-1 overflow-auto bg-gray-100 flex items-center justify-center p-4">
            {testStep === 0 && (
              <div className="text-center">
                <p className="text-gray-600 font-['Almarai']">اختر نوع الاختبار أعلاه</p>
              </div>
            )}

            {loading && testStep > 0 && (
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-600 font-['Almarai']">جاري تحميل PDF...</p>
              </div>
            )}

            {error && (
              <div className="text-center max-w-md">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2 font-['Almarai']">خطأ في تحميل PDF</h3>
                <p className="text-gray-600 font-['Almarai'] text-sm">{error}</p>
              </div>
            )}

            {testStep === 2 && !loading && !error && (
              <Document
                file={getSamplePdfUrl()}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={onDocumentLoadError}
                onLoadProgress={onDocumentLoadProgress}
                loading={
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                    <p className="text-gray-600 font-['Almarai']">جاري معالجة PDF...</p>
                  </div>
                }
                error={
                  <div className="text-center">
                    <p className="text-red-600 font-['Almarai']">فشل في معالجة PDF</p>
                  </div>
                }
                options={{
                  cMapUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`,
                  cMapPacked: true,
                  standardFontDataUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/standard_fonts/`,
                }}
              >
                <Page
                  pageNumber={pageNumber}
                  scale={1.0}
                  renderTextLayer={true}
                  renderAnnotationLayer={true}
                  className="shadow-lg"
                />
              </Document>
            )}
          </div>

          {/* Logs */}
          <div className="w-80 border-l border-gray-200 bg-gray-50 p-4">
            <h3 className="font-bold mb-2 font-['Almarai']">سجل الأحداث</h3>
            <div className="bg-black text-green-400 p-2 rounded text-xs font-mono h-64 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
              {logs.length === 0 && (
                <div className="text-gray-500">لا توجد أحداث بعد...</div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        {numPages > 0 && (
          <div className="p-2 border-t border-gray-200 text-center">
            <span className="text-sm font-['Almarai']">
              صفحة {pageNumber} من {numPages}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimplePDFTest;
