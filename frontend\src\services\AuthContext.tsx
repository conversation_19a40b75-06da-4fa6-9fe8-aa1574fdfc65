import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI } from './api';

interface User {
  id: string;
  email: string;
  role: string;
  createdAt: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  hasPermission: (permission: string) => boolean;
  isAdmin: () => boolean;
  sessionTimeLeft: number | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [refreshToken, setRefreshToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [sessionTimeLeft, setSessionTimeLeft] = useState<number | null>(null);

  useEffect(() => {
    const storedToken = localStorage.getItem('token');
    const storedRefreshToken = localStorage.getItem('refreshToken');
    const storedUser = localStorage.getItem('user');

    if (storedToken && storedUser) {
      setToken(storedToken);
      setRefreshToken(storedRefreshToken);
      setUser(JSON.parse(storedUser));

      // Calculate session time left
      try {
        const payload = JSON.parse(atob(storedToken.split('.')[1]));
        const expirationTime = payload.exp * 1000;
        const timeLeft = expirationTime - Date.now();
        setSessionTimeLeft(timeLeft > 0 ? timeLeft : 0);
      } catch (error) {
        console.error('Error parsing token:', error);
      }
    }
    setLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const response = await authAPI.login(email, password);
      const { token: newToken, refreshToken: newRefreshToken, user: newUser } = response.data;

      setToken(newToken);
      setRefreshToken(newRefreshToken);
      setUser(newUser);
      localStorage.setItem('token', newToken);
      localStorage.setItem('refreshToken', newRefreshToken);
      localStorage.setItem('user', JSON.stringify(newUser));

      // Calculate session time left
      try {
        const payload = JSON.parse(atob(newToken.split('.')[1]));
        const expirationTime = payload.exp * 1000;
        const timeLeft = expirationTime - Date.now();
        setSessionTimeLeft(timeLeft > 0 ? timeLeft : 0);
      } catch (error) {
        console.error('Error parsing token:', error);
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.response?.data?.error || 'Login failed');
    }
  };

  const register = async (email: string, password: string) => {
    try {
      const response = await authAPI.register(email, password);
      const { token: newToken, refreshToken: newRefreshToken, user: newUser } = response.data;

      setToken(newToken);
      setRefreshToken(newRefreshToken);
      setUser(newUser);
      localStorage.setItem('token', newToken);
      localStorage.setItem('refreshToken', newRefreshToken);
      localStorage.setItem('user', JSON.stringify(newUser));

      // Calculate session time left
      try {
        const payload = JSON.parse(atob(newToken.split('.')[1]));
        const expirationTime = payload.exp * 1000;
        const timeLeft = expirationTime - Date.now();
        setSessionTimeLeft(timeLeft > 0 ? timeLeft : 0);
      } catch (error) {
        console.error('Error parsing token:', error);
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.response?.data?.error || 'Registration failed');
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    setRefreshToken(null);
    setSessionTimeLeft(null);
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;

    const permissions = {
      admin: [
        'upload_signatures',
        'verify_documents',
        'sign_documents',
        'view_history',
        'manage_users',
        'view_dashboard',
        'change_password'
      ],
      user: [
        'view_dashboard',
        'change_password'
      ]
    };

    return permissions[user.role as keyof typeof permissions]?.includes(permission) || false;
  };

  const isAdmin = (): boolean => {
    return user?.role === 'admin';
  };

  // Session timeout warning effect
  useEffect(() => {
    if (!token || !sessionTimeLeft) return;

    const warningTime = 5 * 60 * 1000; // 5 minutes in milliseconds
    const timeUntilWarning = sessionTimeLeft - warningTime;

    if (timeUntilWarning > 0) {
      const warningTimer = setTimeout(() => {
        // Show session timeout warning
        if (window.confirm('جلستك ستنتهي خلال 5 دقائق. هل تريد تجديد الجلسة؟')) {
          // User wants to refresh - the interceptor will handle this automatically on next request
          // We can make a simple request to trigger the refresh
          authAPI.getProfile().catch(() => {
            // If refresh fails, user will be logged out automatically
          });
        }
      }, timeUntilWarning);

      return () => clearTimeout(warningTimer);
    }
  }, [token, sessionTimeLeft]);

  const value = {
    user,
    token,
    refreshToken,
    login,
    register,
    logout,
    loading,
    hasPermission,
    isAdmin,
    sessionTimeLeft,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
