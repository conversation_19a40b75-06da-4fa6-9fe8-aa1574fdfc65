const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { requirePermission } = require('../middleware/roleAuth');
const { healthCheck, getPerformanceMetrics, resetMetrics } = require('../models/database');
const DatabaseOptimizationService = require('../services/databaseOptimization');
const { performanceMonitor } = require('../middleware/performanceMonitor');
const { getResourceHealth } = require('../middleware/resourceManager');

const router = express.Router();

// All performance routes require admin authentication
router.use(authenticateToken);
router.use(requirePermission('manage_users')); // Only admins can access performance data

/**
 * Get system health status
 */
router.get('/health', async (req, res) => {
  try {
    const dbHealth = await healthCheck();
    const systemMetrics = getPerformanceMetrics();
    
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    const healthStatus = {
      status: dbHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: dbHealth,
      system: {
        memory: {
          used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024),
          rss: Math.round(memoryUsage.rss / 1024 / 1024)
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        uptime: Math.round(process.uptime()),
        nodeVersion: process.version
      },
      performance: systemMetrics
    };

    res.json({
      success: true,
      data: healthStatus
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في فحص حالة النظام',
      error: error.message
    });
  }
});

/**
 * Get detailed performance metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    const metrics = getPerformanceMetrics();
    
    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    console.error('Metrics retrieval failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب مقاييس الأداء',
      error: error.message
    });
  }
});

/**
 * Reset performance metrics
 */
router.post('/metrics/reset', async (req, res) => {
  try {
    resetMetrics();
    
    res.json({
      success: true,
      message: 'تم إعادة تعيين مقاييس الأداء بنجاح'
    });
  } catch (error) {
    console.error('Metrics reset failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في إعادة تعيين مقاييس الأداء',
      error: error.message
    });
  }
});

/**
 * Get database optimization report
 */
router.get('/database/optimization', async (req, res) => {
  try {
    const report = await DatabaseOptimizationService.getPerformanceReport();
    
    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    console.error('Database optimization report failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في إنشاء تقرير تحسين قاعدة البيانات',
      error: error.message
    });
  }
});

/**
 * Run database optimization
 */
router.post('/database/optimize', async (req, res) => {
  try {
    const indexResults = await DatabaseOptimizationService.optimizeIndexes();
    const maintenanceResults = await DatabaseOptimizationService.runMaintenance();
    
    res.json({
      success: true,
      message: 'تم تشغيل تحسين قاعدة البيانات بنجاح',
      data: {
        indexOptimization: indexResults,
        maintenance: maintenanceResults
      }
    });
  } catch (error) {
    console.error('Database optimization failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحسين قاعدة البيانات',
      error: error.message
    });
  }
});

/**
 * Get query performance analysis
 */
router.get('/database/queries', async (req, res) => {
  try {
    const analysis = await DatabaseOptimizationService.analyzeQueryPerformance();
    
    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    console.error('Query analysis failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحليل أداء الاستعلامات',
      error: error.message
    });
  }
});

/**
 * Get table statistics
 */
router.get('/database/tables', async (req, res) => {
  try {
    const analysis = await DatabaseOptimizationService.analyzeTableStatistics();
    
    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    console.error('Table analysis failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحليل إحصائيات الجداول',
      error: error.message
    });
  }
});

/**
 * Get system resource usage
 */
router.get('/resources', async (req, res) => {
  try {
    const resourceHealth = getResourceHealth();

    res.json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        ...resourceHealth,
        recommendations: generateResourceRecommendations(resourceHealth)
      }
    });
  } catch (error) {
    console.error('Resource monitoring failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في مراقبة الموارد',
      error: error.message
    });
  }
});

/**
 * Trigger manual resource cleanup
 */
router.post('/cleanup', async (req, res) => {
  try {
    const { memoryManager } = require('../middleware/resourceManager');
    await memoryManager.triggerCleanup();

    res.json({
      success: true,
      message: 'تم تشغيل تنظيف الموارد بنجاح'
    });
  } catch (error) {
    console.error('Resource cleanup failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تنظيف الموارد',
      error: error.message
    });
  }
});

// Helper function to generate resource recommendations
function generateResourceRecommendations(resourceHealth) {
  const recommendations = [];

  if (resourceHealth.memory.usagePercent > 80) {
    recommendations.push({
      type: 'memory',
      severity: 'high',
      message: 'استخدام الذاكرة مرتفع جداً',
      action: 'فكر في إعادة تشغيل الخدمة أو زيادة الذاكرة المتاحة'
    });
  } else if (resourceHealth.memory.usagePercent > 60) {
    recommendations.push({
      type: 'memory',
      severity: 'medium',
      message: 'استخدام الذاكرة مرتفع',
      action: 'راقب استخدام الذاكرة وفكر في تحسين الكود'
    });
  }

  if (resourceHealth.uptime < 3600) { // Less than 1 hour
    recommendations.push({
      type: 'uptime',
      severity: 'info',
      message: 'الخدمة تم إعادة تشغيلها مؤخراً',
      action: 'تأكد من استقرار النظام'
    });
  }

  return recommendations;
}

module.exports = router;
