// Optional bidi-js import for bidirectional text processing
let bidi = null;
try {
  bidi = require('bidi-js');
} catch (error) {
  console.warn('bidi-js not available in RTL service - using fallback processing');
}

// RTL language codes (Arabic-only system)
const RTL_LANGUAGES = ['ar'];

// Unicode ranges for Arabic script
const RTL_UNICODE_RANGES = {
  ARABIC: [0x0600, 0x06FF],
  ARABIC_SUPPLEMENT: [0x0750, 0x077F],
  ARABIC_EXTENDED_A: [0x08A0, 0x08FF],
  ARABIC_PRESENTATION_FORMS_A: [0xFB50, 0xFDFF],
  ARABIC_PRESENTATION_FORMS_B: [0xFE70, 0xFEFF]
};

// Check if character is RTL
const isRTLCharacter = (charCode) => {
  for (const [start, end] of Object.values(RTL_UNICODE_RANGES)) {
    if (charCode >= start && charCode <= end) {
      return true;
    }
  }
  return false;
};

// Always return RTL for Arabic-only system
const detectTextDirection = () => 'rtl';

// Process bidirectional text (simplified for Arabic-only)
const processBidiText = (arabicText) => {
  if (!arabicText) return arabicText;

  // If bidi-js is not available, return original text
  if (!bidi) {
    return arabicText;
  }

  try {
    // Always use RTL for Arabic
    const processedText = bidi(arabicText, {
      dir: 'rtl'
    });

    return processedText;
  } catch (error) {
    console.warn('Bidirectional text processing failed:', error);
    return arabicText;
  }
};

// Calculate text metrics for RTL positioning
const calculateTextMetrics = (text, fontSize = 12, fontFamily = 'Almarai') => {
  // Approximate character width calculations
  const charWidths = {
    'Arial': 0.6,
    'Helvetica': 0.6,
    'Times': 0.5,
    'Courier': 0.6,
    'Almarai': 0.65, // Almarai font character width
  };
  
  const avgCharWidth = (charWidths[fontFamily] || 0.6) * fontSize;
  const textWidth = text.length * avgCharWidth;
  const textHeight = fontSize * 1.2; // Line height
  
  return {
    width: textWidth,
    height: textHeight,
    avgCharWidth,
    characterCount: text.length
  };
};

// Get optimal positioning for Arabic RTL text
const getOptimalTextPosition = (arabicText, x, y, pageWidth, pageHeight, options = {}) => {
  const {
    fontSize = 12,
    fontFamily = 'Almarai',
    alignment = 'start', // 'start', 'center', 'end'
    margin = 10
  } = options;

  const metrics = calculateTextMetrics(arabicText, fontSize, fontFamily);

  let finalX = x;
  let finalY = y;
  
  // Adjust X position based on RTL alignment for Arabic
  switch (alignment) {
    case 'start':
      // For RTL, start means right side
      finalX = Math.min(pageWidth - margin, x);
      break;
    case 'center':
      finalX = (pageWidth - metrics.width) / 2;
      break;
    case 'end':
      // For RTL, end means left side
      finalX = Math.max(margin, x - metrics.width);
      break;
    default:
      // Default RTL positioning
      finalX = Math.min(pageWidth - metrics.width - margin, x);
  }
  
  // Ensure text fits within page bounds
  finalX = Math.max(margin, Math.min(finalX, pageWidth - metrics.width - margin));
  finalY = Math.max(margin, Math.min(finalY, pageHeight - metrics.height - margin));
  
  return {
    x: finalX,
    y: finalY,
    direction: 'rtl',
    metrics,
    processedText: processBidiText(arabicText)
  };
};

// Analyze document layout for optimal signature placement (Arabic RTL)
const analyzeDocumentLayout = (content) => {
  // Always return RTL layout analysis for Arabic documents
  return {
    direction: 'rtl',
    hasArabicContent: true,
    language: 'ar',
    optimalSignaturePosition: 'bottom-right'
  };
};

// Format Arabic text (simplified for Arabic-only system)
const formatMultilingualText = (arabicText) => {
  if (!arabicText) return '';
  return processBidiText(arabicText);
};

module.exports = {
  isRTLCharacter,
  detectTextDirection,
  processBidiText,
  calculateTextMetrics,
  getOptimalTextPosition,
  analyzeDocumentLayout,
  formatMultilingualText,
  RTL_LANGUAGES,
  RTL_UNICODE_RANGES
};
