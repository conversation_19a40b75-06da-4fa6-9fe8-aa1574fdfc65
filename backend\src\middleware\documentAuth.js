const { query } = require('../models/database');
const { logAccessDenied } = require('../services/auditService');

/**
 * Middleware to check if user has access to a specific document
 * @param {string} permission - Required permission: 'VIEW', 'DOWNLOAD', 'SIGN', 'ADMIN'
 */
const checkDocumentAccess = (permission = 'VIEW') => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id || req.user?.userId;
      const documentId = req.params.documentId || req.params.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'المصادقة مطلوبة',
          code: 'AUTHENTICATION_REQUIRED'
        });
      }

      if (!documentId) {
        return res.status(400).json({
          success: false,
          error: 'معرف المستند مطلوب',
          code: 'DOCUMENT_ID_REQUIRED'
        });
      }

      // Check if document exists and get ownership info
      const documentQuery = `
        SELECT
          d.id,
          d.user_id,
          d.original_filename,
          d.status,
          u.email as owner_email
        FROM documents d
        LEFT JOIN users u ON d.user_id = u.id
        WHERE d.id = $1
      `;

      const documentResult = await query(documentQuery, [documentId]);

      if (documentResult.rows.length === 0) {
        await logAccessDenied(userId, documentId, 'Document not found', req);
        return res.status(404).json({
          success: false,
          error: 'المستند غير موجود',
          code: 'DOCUMENT_NOT_FOUND'
        });
      }

      const document = documentResult.rows[0];

      // Check if user is the owner
      const isOwner = document.user_id === userId;

      // Check for explicit permissions if not owner
      let hasPermission = isOwner;

      if (!hasPermission) {
        const permissionQuery = `
          SELECT permission_type 
          FROM user_document_permissions 
          WHERE document_id = $1 AND user_id = $2 AND is_active = true
          AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
        `;

        const permissionResult = await query(permissionQuery, [documentId, userId]);
        const userPermissions = permissionResult.rows.map(row => row.permission_type);

        // Check if user has required permission
        hasPermission = userPermissions.includes(permission) || userPermissions.includes('ADMIN');

        // Special cases for different user roles
        if (!hasPermission) {
          switch (permission) {
            case 'VIEW':
              hasPermission = isUploader || isSigner;
              break;
            case 'DOWNLOAD':
              hasPermission = isUploader || isSigner;
              break;
            case 'SIGN':
              // Only allow signing if user has explicit permission or is the uploader
              hasPermission = isUploader || userPermissions.includes('SIGN');
              break;
            default:
              hasPermission = false;
          }
        }
      }

      if (!hasPermission) {
        await logAccessDenied(userId, documentId, `Insufficient permissions for ${permission}`, req);
        return res.status(403).json({
          success: false,
          error: 'ليس لديك صلاحية للوصول إلى هذا المستند',
          code: 'ACCESS_DENIED',
          details: {
            requiredPermission: permission,
            documentOwner: document.owner_username
          }
        });
      }

      // Add document info to request for use in route handlers
      req.document = document;
      req.userPermissions = {
        isOwner,
        hasAccess: hasPermission
      };

      next();

    } catch (error) {
      console.error('Document authorization error:', error);
      return res.status(500).json({
        success: false,
        error: 'خطأ في التحقق من الصلاحيات',
        code: 'AUTHORIZATION_ERROR'
      });
    }
  };
};

/**
 * Middleware to ensure user can only access their own documents
 */
const requireDocumentOwnership = async (req, res, next) => {
  try {
    const userId = req.user?.id || req.user?.userId;
    const documentId = req.params.documentId || req.params.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'المصادقة مطلوبة',
        code: 'AUTHENTICATION_REQUIRED'
      });
    }

    if (!documentId) {
      return res.status(400).json({
        success: false,
        error: 'معرف المستند مطلوب',
        code: 'DOCUMENT_ID_REQUIRED'
      });
    }

    // Check document ownership
    const ownershipQuery = `
      SELECT id, user_id, original_filename 
      FROM documents 
      WHERE id = $1 AND user_id = $2
    `;

    const result = await query(ownershipQuery, [documentId, userId]);

    if (result.rows.length === 0) {
      await logAccessDenied(userId, documentId, 'Not document owner', req);
      return res.status(403).json({
        success: false,
        error: 'ليس لديك صلاحية للوصول إلى هذا المستند',
        code: 'NOT_OWNER'
      });
    }

    req.document = result.rows[0];
    next();

  } catch (error) {
    console.error('Document ownership check error:', error);
    return res.status(500).json({
      success: false,
      error: 'خطأ في التحقق من ملكية المستند',
      code: 'OWNERSHIP_CHECK_ERROR'
    });
  }
};

/**
 * Middleware to filter documents by user ownership
 */
const filterUserDocuments = (req, res, next) => {
  const userId = req.user?.id || req.user?.userId;

  if (!userId) {
    return res.status(401).json({
      success: false,
      error: 'المصادقة مطلوبة',
      code: 'AUTHENTICATION_REQUIRED'
    });
  }

  // Add user filter to query parameters
  req.userFilter = { userId };
  next();
};

/**
 * Check if user has permission to share documents
 */
const canShareDocument = async (req, res, next) => {
  try {
    const userId = req.user?.id || req.user?.userId;
    const documentId = req.params.documentId;

    // Check if user is owner or has ADMIN permission
    const permissionQuery = `
      SELECT 
        CASE 
          WHEN d.user_id = $2 THEN true
          WHEN udp.permission_type = 'ADMIN' THEN true
          ELSE false
        END as can_share
      FROM documents d
      LEFT JOIN user_document_permissions udp ON d.id = udp.document_id 
        AND udp.user_id = $2 AND udp.is_active = true
      WHERE d.id = $1
    `;

    const result = await query(permissionQuery, [documentId, userId]);

    if (result.rows.length === 0 || !result.rows[0].can_share) {
      await logAccessDenied(userId, documentId, 'Cannot share document', req);
      return res.status(403).json({
        success: false,
        error: 'ليس لديك صلاحية لمشاركة هذا المستند',
        code: 'CANNOT_SHARE'
      });
    }

    next();

  } catch (error) {
    console.error('Share permission check error:', error);
    return res.status(500).json({
      success: false,
      error: 'خطأ في التحقق من صلاحية المشاركة',
      code: 'SHARE_PERMISSION_ERROR'
    });
  }
};

/**
 * Rate limiting for document operations per user
 */
const documentRateLimit = (maxOperations = 100, windowMs = 60 * 60 * 1000) => {
  const userOperations = new Map();

  return (req, res, next) => {
    const userId = req.user?.id || req.user?.userId;
    const now = Date.now();
    const windowStart = now - windowMs;

    if (!userId) {
      return next();
    }

    // Clean old entries
    if (userOperations.has(userId)) {
      const operations = userOperations.get(userId);
      userOperations.set(userId, operations.filter(time => time > windowStart));
    }

    // Check current operations count
    const currentOperations = userOperations.get(userId) || [];
    
    if (currentOperations.length >= maxOperations) {
      return res.status(429).json({
        success: false,
        error: 'تم تجاوز الحد الأقصى للعمليات. يرجى المحاولة لاحقاً.',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    // Add current operation
    currentOperations.push(now);
    userOperations.set(userId, currentOperations);

    next();
  };
};

module.exports = {
  checkDocumentAccess,
  requireDocumentOwnership,
  filterUserDocuments,
  canShareDocument,
  documentRateLimit
};
