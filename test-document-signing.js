const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

async function testDocumentSigning() {
  try {
    // First, login to get a token
    console.log('Logging in...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.token;
    console.log('Login successful, token received');
    
    // Get user's signatures
    console.log('Fetching signatures...');
    const signaturesResponse = await axios.get('http://localhost:3001/api/signatures', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const signatures = signaturesResponse.data.signatures;
    console.log(`Found ${signatures.length} signatures`);
    
    if (signatures.length === 0) {
      console.log('No signatures found. Please upload a signature first.');
      return;
    }
    
    const signatureId = signatures[0].id;
    console.log(`Using signature ID: ${signatureId}`);
    
    // Create a simple PDF for testing
    const testPdfPath = path.join(__dirname, 'test.pdf');
    if (!fs.existsSync(testPdfPath)) {
      console.log('Creating a test PDF...');
      // Create a minimal PDF content
      const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;
      
      fs.writeFileSync(testPdfPath, pdfContent);
      console.log('Test PDF created');
    }
    
    // Test document signing
    console.log('Testing document signing...');
    const form = new FormData();
    form.append('document', fs.createReadStream(testPdfPath));
    form.append('signatureId', signatureId);
    form.append('coordinates', JSON.stringify({ x: 100, y: 100 }));
    
    const signResponse = await axios.post('http://localhost:3001/api/documents/sign', form, {
      headers: {
        ...form.getHeaders(),
        Authorization: `Bearer ${token}`
      },
      timeout: 60000 // 60 seconds timeout
    });
    
    console.log('Document signing successful!');
    console.log('Response:', signResponse.data);
    
  } catch (error) {
    console.error('Error testing document signing:');
    console.error('Status:', error.response?.status);
    console.error('Message:', error.response?.data?.message || error.message);
    console.error('Details:', error.response?.data);
  }
}

testDocumentSigning();
