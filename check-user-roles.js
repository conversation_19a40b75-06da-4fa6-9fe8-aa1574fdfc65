const { query } = require('./backend/src/models/database');

async function checkUserRoles() {
  try {
    console.log('🔍 Checking User Roles in Database...\n');
    
    // Get all users and their roles
    const users = await query('SELECT id, email, role, created_at FROM users ORDER BY created_at');
    
    console.log('📊 All Users in Database:');
    console.log('=' .repeat(80));
    users.rows.forEach((user, index) => {
      console.log(`${index + 1}. Email: ${user.email}`);
      console.log(`   Role: ${user.role || 'NULL'}`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Created: ${user.created_at}`);
      console.log('-'.repeat(40));
    });
    
    // Check specific users
    console.log('\n🎯 Specific User Checks:');
    
    // Check regular user
    const regularUser = await query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    if (regularUser.rows.length > 0) {
      console.log('\n👤 Regular User (<EMAIL>):');
      console.log('   Role:', regularUser.rows[0].role);
      console.log('   ID:', regularUser.rows[0].id);
      console.log('   Full data:', JSON.stringify(regularUser.rows[0], null, 2));
    } else {
      console.log('\n❌ Regular user not found!');
    }
    
    // Check admin user
    const adminUser = await query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    if (adminUser.rows.length > 0) {
      console.log('\n👑 Admin User (<EMAIL>):');
      console.log('   Role:', adminUser.rows[0].role);
      console.log('   ID:', adminUser.rows[0].id);
      console.log('   Full data:', JSON.stringify(adminUser.rows[0], null, 2));
    } else {
      console.log('\n❌ Admin user not found!');
    }
    
    // Check for any users with NULL or unexpected roles
    const usersWithNullRole = await query('SELECT * FROM users WHERE role IS NULL OR role NOT IN ($1, $2)', ['user', 'admin']);
    if (usersWithNullRole.rows.length > 0) {
      console.log('\n⚠️ Users with NULL or unexpected roles:');
      usersWithNullRole.rows.forEach(user => {
        console.log(`   - ${user.email}: role = ${user.role}`);
      });
    } else {
      console.log('\n✅ All users have valid roles (user or admin)');
    }
    
    // Test permission logic
    console.log('\n🔐 Testing Permission Logic:');
    
    const testPermission = (userRole, permission) => {
      const permissions = {
        admin: [
          'upload_signatures',
          'verify_documents', 
          'sign_documents',
          'view_history',
          'manage_users',
          'view_dashboard',
          'change_password'
        ],
        user: [
          'view_history', 
          'view_dashboard',
          'change_password'
        ]
      };
      
      return permissions[userRole]?.includes(permission) || false;
    };
    
    const testUsers = [
      { email: '<EMAIL>', role: 'user' },
      { email: '<EMAIL>', role: 'admin' }
    ];
    
    const testPermissions = ['sign_documents', 'view_history', 'manage_users'];
    
    testUsers.forEach(user => {
      console.log(`\n   ${user.email} (${user.role}):`);
      testPermissions.forEach(permission => {
        const hasPermission = testPermission(user.role, permission);
        console.log(`     - ${permission}: ${hasPermission ? '✅ YES' : '❌ NO'}`);
      });
    });
    
    console.log('\n🎉 User Role Check Completed!');
    
  } catch (error) {
    console.error('❌ Error checking user roles:', error);
  }
  
  process.exit(0);
}

checkUserRoles();
