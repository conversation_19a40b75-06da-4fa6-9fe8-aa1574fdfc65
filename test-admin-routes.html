<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Routes</title>
    <style>
        body {
            font-family: 'Alma<PERSON>', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .route-test {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .user-route {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .admin-route {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 3px;
            cursor: pointer;
            margin: 3px;
            font-size: 12px;
        }
        button:hover {
            background-color: #0056b3;
        }
        a {
            color: #007bff;
            text-decoration: none;
            margin: 5px;
            padding: 5px 10px;
            border: 1px solid #007bff;
            border-radius: 3px;
            display: inline-block;
        }
        a:hover {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Test Admin Route Access</h1>
        
        <div class="section info">
            <h3>Current User Status</h3>
            <div id="currentUser">Loading...</div>
        </div>

        <div class="section">
            <h3>Route Access Test</h3>
            <p><strong>Instructions:</strong> Click the links below to test access. Regular users should be blocked from admin routes.</p>
            
            <div class="route-test user-route">
                <h4>✅ Regular User Routes (Should Work for All Users)</h4>
                <p>These pages are for regular users to manage their own documents:</p>
                <a href="http://localhost:3000/document-signing" target="_blank">📝 Document Signing (User)</a>
                <a href="http://localhost:3000/history" target="_blank">📋 History (User)</a>
                <a href="http://localhost:3000/dashboard" target="_blank">🏠 Dashboard</a>
            </div>
            
            <div class="route-test admin-route">
                <h4>🔒 Admin-Only Routes (Should Block Regular Users)</h4>
                <p>These pages should only be accessible to admin users:</p>
                <a href="http://localhost:3000/admin/document-signing" target="_blank">👑 Admin Document Signing</a>
                <a href="http://localhost:3000/admin/records" target="_blank">👑 Admin Records</a>
                <a href="http://localhost:3000/users" target="_blank">👑 Users Management</a>
            </div>
        </div>

        <div class="section">
            <h3>Expected Behavior</h3>
            <div class="section info">
                <h4>For Regular Users (role: user):</h4>
                <ul>
                    <li>✅ Can access: /document-signing, /history, /dashboard</li>
                    <li>❌ Should be blocked from: /admin/document-signing, /admin/records, /users</li>
                    <li>❌ Should see error page or be redirected when accessing admin routes</li>
                </ul>
            </div>
            
            <div class="section success">
                <h4>For Admin Users (role: admin):</h4>
                <ul>
                    <li>✅ Can access: All routes including admin routes</li>
                    <li>✅ Should see admin navigation links</li>
                    <li>✅ Should have full system access</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h3>Test Results</h3>
            <div id="testResults">
                <p>Click the links above and report back:</p>
                <ol>
                    <li>Which routes can you access as a regular user?</li>
                    <li>Do you see error pages for admin routes?</li>
                    <li>Are you redirected or blocked from admin pages?</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function checkCurrentUser() {
            const userStr = localStorage.getItem('user');
            const currentUserDiv = document.getElementById('currentUser');
            
            if (!userStr) {
                currentUserDiv.innerHTML = '<span style="color: red;">❌ No user logged in</span>';
                return;
            }

            try {
                const user = JSON.parse(userStr);
                currentUserDiv.innerHTML = `
                    <strong>Email:</strong> ${user.email}<br>
                    <strong>Role:</strong> ${user.role}<br>
                    <strong>Expected Access:</strong> ${user.role === 'admin' ? 'All routes' : 'User routes only'}
                `;
            } catch (error) {
                currentUserDiv.innerHTML = '<span style="color: red;">❌ Invalid user data</span>';
            }
        }

        // Check current user on page load
        window.onload = function() {
            checkCurrentUser();
        };
    </script>
</body>
</html>
