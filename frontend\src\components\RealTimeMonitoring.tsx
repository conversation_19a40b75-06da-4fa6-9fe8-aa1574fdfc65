import React, { useState, useEffect, useRef, useCallback } from 'react';

interface SystemMetrics {
  timestamp: string;
  system: {
    memory: {
      used: number;
      total: number;
      usagePercent: number;
    };
    uptime: number;
    status: 'healthy' | 'warning' | 'critical' | 'error';
  };
  database: {
    status: 'healthy' | 'unhealthy';
    responseTime: number;
    performance: {
      totalQueries: number;
      slowQueries: number;
      failedQueries: number;
      averageResponseTime: number;
      slowQueryPercentage: string;
      failureRate: string;
    };
  };
  application: {
    activeConnections: number;
    uptime: number;
    memoryUsage: {
      heapUsed: number;
      heapTotal: number;
    };
  };
  alerts: Array<{
    id: string;
    type: string;
    severity: 'warning' | 'critical';
    title: string;
    message: string;
    timestamp: string;
    acknowledged: boolean;
  }>;
}

interface RealTimeMonitoringProps {
  className?: string;
  autoConnect?: boolean;
  updateInterval?: number;
}

const RealTimeMonitoring: React.FC<RealTimeMonitoringProps> = ({
  className = '',
  autoConnect = true,
  updateInterval = 10000
}) => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [alerts, setAlerts] = useState<SystemMetrics['alerts']>([]);
  const [isExpanded, setIsExpanded] = useState(false);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) return;

    setConnectionStatus('connecting');
    
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const wsUrl = `${protocol}//${host}/ws/monitoring`;

    try {
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('📊 Connected to monitoring WebSocket');
        setConnectionStatus('connected');
        reconnectAttempts.current = 0;
        
        // Request initial metrics
        wsRef.current?.send(JSON.stringify({
          type: 'request_metrics'
        }));
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'metrics_update') {
            setMetrics(data.data);
            setLastUpdate(new Date());
            
            // Update alerts
            if (data.data.alerts) {
              setAlerts(data.data.alerts.filter((alert: any) => !alert.acknowledged));
            }
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('📊 Monitoring WebSocket disconnected');
        setConnectionStatus('disconnected');
        
        // Attempt to reconnect
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          reconnectAttempts.current++;
          
          console.log(`📊 Reconnecting in ${delay}ms (attempt ${reconnectAttempts.current})`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, delay);
        } else {
          setConnectionStatus('error');
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('📊 WebSocket error:', error);
        setConnectionStatus('error');
      };

    } catch (error) {
      console.error('📊 Failed to create WebSocket connection:', error);
      setConnectionStatus('error');
    }
  }, []);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setConnectionStatus('disconnected');
  }, []);

  const acknowledgeAlert = useCallback((alertId: string) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'acknowledge_alert',
        alertId
      }));
    }
    
    // Remove from local state immediately
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  }, []);

  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-600';
      case 'connecting': return 'text-yellow-600';
      case 'disconnected': return 'text-gray-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatBytes = (bytes: number) => {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  if (!isExpanded) {
    return (
      <div className={`fixed bottom-4 left-4 z-50 ${className}`}>
        <div className="bg-white rounded-lg shadow-lg border">
          <button
            onClick={() => setIsExpanded(true)}
            className="flex items-center space-x-2 space-x-reverse p-3 hover:bg-gray-50 rounded-lg"
          >
            <div className={`w-3 h-3 rounded-full ${connectionStatus === 'connected' ? 'bg-green-500' : connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'}`}></div>
            <span className="text-sm font-medium">المراقبة</span>
            {alerts.length > 0 && (
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {alerts.length}
              </span>
            )}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed bottom-4 left-4 z-50 w-96 ${className}`}>
      <div className="bg-white rounded-lg shadow-lg border">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2 space-x-reverse">
            <div className={`w-3 h-3 rounded-full ${connectionStatus === 'connected' ? 'bg-green-500' : connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'}`}></div>
            <h3 className="font-semibold">المراقبة في الوقت الفعلي</h3>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            {connectionStatus !== 'connected' && (
              <button
                onClick={connect}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                اتصال
              </button>
            )}
            <button
              onClick={() => setIsExpanded(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Connection Status */}
        <div className="p-3 bg-gray-50 border-b">
          <div className="flex items-center justify-between text-sm">
            <span className={`font-medium ${getConnectionStatusColor()}`}>
              {connectionStatus === 'connected' && 'متصل'}
              {connectionStatus === 'connecting' && 'جاري الاتصال...'}
              {connectionStatus === 'disconnected' && 'غير متصل'}
              {connectionStatus === 'error' && 'خطأ في الاتصال'}
            </span>
            {lastUpdate && (
              <span className="text-gray-500">
                آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
              </span>
            )}
          </div>
        </div>

        {/* Alerts */}
        {alerts.length > 0 && (
          <div className="p-3 border-b">
            <h4 className="font-medium text-sm mb-2">التنبيهات ({alerts.length})</h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {alerts.slice(0, 3).map((alert) => (
                <div key={alert.id} className={`p-2 rounded text-xs ${alert.severity === 'critical' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{alert.title}</div>
                      <div className="text-xs opacity-75">{alert.message}</div>
                    </div>
                    <button
                      onClick={() => acknowledgeAlert(alert.id)}
                      className="text-xs underline hover:no-underline"
                    >
                      إقرار
                    </button>
                  </div>
                </div>
              ))}
              {alerts.length > 3 && (
                <div className="text-xs text-gray-500 text-center">
                  و {alerts.length - 3} تنبيهات أخرى
                </div>
              )}
            </div>
          </div>
        )}

        {/* Metrics */}
        {metrics && (
          <div className="p-3 space-y-3">
            {/* System Status */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">حالة النظام</span>
              <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(metrics.system.status)}`}>
                {metrics.system.status === 'healthy' && 'سليم'}
                {metrics.system.status === 'warning' && 'تحذير'}
                {metrics.system.status === 'critical' && 'حرج'}
                {metrics.system.status === 'error' && 'خطأ'}
              </span>
            </div>

            {/* Memory Usage */}
            <div>
              <div className="flex items-center justify-between text-sm mb-1">
                <span>الذاكرة</span>
                <span>{metrics.system.memory.usagePercent}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    metrics.system.memory.usagePercent >= 80 ? 'bg-red-500' :
                    metrics.system.memory.usagePercent >= 60 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${metrics.system.memory.usagePercent}%` }}
                ></div>
              </div>
            </div>

            {/* Database */}
            <div className="flex items-center justify-between text-sm">
              <span>قاعدة البيانات</span>
              <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(metrics.database.status)}`}>
                {metrics.database.responseTime}ms
              </span>
            </div>

            {/* Active Connections */}
            <div className="flex items-center justify-between text-sm">
              <span>الاتصالات النشطة</span>
              <span>{metrics.application.activeConnections}</span>
            </div>

            {/* Uptime */}
            <div className="flex items-center justify-between text-sm">
              <span>وقت التشغيل</span>
              <span>{formatUptime(metrics.system.uptime)}</span>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="p-2 bg-gray-50 rounded-b-lg">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>تحديث كل {updateInterval / 1000}s</span>
            {metrics && (
              <span>
                استعلامات: {metrics.database.performance.totalQueries}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeMonitoring;
