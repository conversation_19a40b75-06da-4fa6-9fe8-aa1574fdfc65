const { Pool } = require('pg');

// Enhanced connection pool configuration for optimal performance
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'esign',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,

  // Connection pool optimization
  max: parseInt(process.env.DB_POOL_MAX) || 25, // maximum number of clients in the pool
  min: parseInt(process.env.DB_POOL_MIN) || 5,  // minimum number of clients in the pool
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000, // 30 seconds
  connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 5000, // 5 seconds
  acquireTimeoutMillis: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000, // 60 seconds

  // Performance tuning
  statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT) || 30000, // 30 seconds
  query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 30000, // 30 seconds

  // SSL configuration for production
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,

  // Application name for monitoring
  application_name: 'esign_backend'
});

// Test database connection
pool.on('connect', () => {
  console.log('Connected to PostgreSQL database');
});

pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Performance metrics storage
const queryMetrics = {
  totalQueries: 0,
  slowQueries: 0,
  failedQueries: 0,
  averageResponseTime: 0,
  slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD) || 1000 // 1 second
};

// Enhanced query function with performance monitoring
const query = async (text, params) => {
  const start = Date.now();
  const queryId = Math.random().toString(36).substr(2, 9);

  try {
    queryMetrics.totalQueries++;

    // Log slow queries for optimization
    const res = await pool.query(text, params);
    const duration = Date.now() - start;

    // Update performance metrics
    queryMetrics.averageResponseTime =
      (queryMetrics.averageResponseTime * (queryMetrics.totalQueries - 1) + duration) / queryMetrics.totalQueries;

    if (duration > queryMetrics.slowQueryThreshold) {
      queryMetrics.slowQueries++;
      console.warn(`🐌 Slow query detected [${queryId}]:`, {
        duration: `${duration}ms`,
        query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        params: params ? params.length : 0,
        rows: res.rowCount
      });
    } else if (process.env.NODE_ENV === 'development') {
      console.log(`⚡ Query executed [${queryId}]:`, {
        duration: `${duration}ms`,
        rows: res.rowCount
      });
    }

    return res;
  } catch (error) {
    const duration = Date.now() - start;
    queryMetrics.failedQueries++;

    console.error(`❌ Query failed [${queryId}]:`, {
      duration: `${duration}ms`,
      error: error.message,
      query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      params: params ? params.length : 0
    });

    throw error;
  }
};

// Helper function to get a client from the pool
const getClient = async () => {
  return await pool.connect();
};

// Database health check function
const healthCheck = async () => {
  try {
    const start = Date.now();
    const result = await query('SELECT 1 as health_check');
    const responseTime = Date.now() - start;

    const poolInfo = {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount
    };

    return {
      status: 'healthy',
      responseTime,
      poolInfo,
      metrics: queryMetrics
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      metrics: queryMetrics
    };
  }
};

// Get performance metrics
const getPerformanceMetrics = () => {
  return {
    ...queryMetrics,
    poolStats: {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount
    },
    slowQueryPercentage: queryMetrics.totalQueries > 0
      ? (queryMetrics.slowQueries / queryMetrics.totalQueries * 100).toFixed(2) + '%'
      : '0%',
    failureRate: queryMetrics.totalQueries > 0
      ? (queryMetrics.failedQueries / queryMetrics.totalQueries * 100).toFixed(2) + '%'
      : '0%'
  };
};

// Reset performance metrics
const resetMetrics = () => {
  queryMetrics.totalQueries = 0;
  queryMetrics.slowQueries = 0;
  queryMetrics.failedQueries = 0;
  queryMetrics.averageResponseTime = 0;
};

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down database connection pool...');
  pool.end(() => {
    console.log('Database connection pool has ended');
    process.exit(0);
  });
});

module.exports = {
  pool,
  query,
  getClient,
  healthCheck,
  getPerformanceMetrics,
  resetMetrics
};
