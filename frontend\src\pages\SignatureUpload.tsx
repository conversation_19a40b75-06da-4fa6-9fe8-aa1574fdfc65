import React, { useState, useRef, useEffect } from 'react';
import { signatureAPI } from '../services/api';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../services/AuthContext';
import AccessDenied from '../components/AccessDenied';

interface Signature {
  id: string;
  filename: string;
  fileSize: number;
  uploadDate: string;
}

const SignatureUpload: React.FC = () => {
  const { t } = useLanguage();
  const { hasPermission } = useAuth();
  const [signatures, setSignatures] = useState<Signature[]>([]);
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (hasPermission('upload_signatures')) {
      fetchSignatures();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasPermission]);

  // Check if user has permission to upload signatures
  if (!hasPermission('upload_signatures')) {
    return <AccessDenied feature="رفع التوقيعات" />;
  }

  const fetchSignatures = async () => {
    try {
      const response = await signatureAPI.getAll();
      setSignatures(response.data.signatures);
    } catch (error) {
      console.error('Error fetching signatures:', error);
      setError(t.errors.loadFailed);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    uploadSignature(file);
  };

  const uploadSignature = async (file: File) => {
    setError('');
    setSuccess('');
    
    // Validate file type
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type)) {
      setError(t.fileValidation.imageOnly);
      return;
    }

    // Remove artificial file size limit for signatures

    setUploading(true);

    try {
      await signatureAPI.upload(file);
      setSuccess(t.success.signatureUploaded);
      fetchSignatures(); // Refresh the list

      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      setError(error.response?.data?.error || t.errors.uploadFailed);
    } finally {
      setUploading(false);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const deleteSignature = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this signature?')) {
      return;
    }

    try {
      await signatureAPI.delete(id);
      setSuccess('Signature deleted successfully');
      fetchSignatures();
    } catch (error: any) {
      setError(error.response?.data?.error || 'Delete failed');
    }
  };

  const formatFileSize = (bytes: number | undefined) => {
    // Handle undefined, null, or invalid values
    if (bytes === undefined || bytes === null || isNaN(bytes) || bytes < 0) {
      return 'غير محدد';
    }

    if (bytes === 0) return '0 بايت';

    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    // Ensure we don't exceed the sizes array
    const sizeIndex = Math.min(i, sizes.length - 1);
    const formattedSize = parseFloat((bytes / Math.pow(k, sizeIndex)).toFixed(2));

    return `${formattedSize} ${sizes[sizeIndex]}`;
  };

  const formatUploadDate = (dateString: string | undefined) => {
    // Handle undefined, null, or invalid date strings
    if (!dateString) {
      return 'غير محدد';
    }

    try {
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return 'تاريخ غير صالح';
      }

      // Format date in English locale
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'تاريخ غير صالح';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">{t.signatureUpload.title}</h1>
        <p className="text-gray-600 mb-6">{t.signatureUpload.subtitle}</p>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {success}
          </div>
        )}

        {/* Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive 
              ? 'border-primary-500 bg-primary-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <div className="space-y-4">
            <div className="mx-auto w-12 h-12 text-gray-400">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            <div>
              <p className="text-lg font-medium text-gray-900">
                {t.signatureUpload.dropZone}{' '}
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="text-primary-500 hover:text-primary-600"
                >
                  {t.signatureUpload.browse}
                </button>
              </p>
              <p className="text-sm text-gray-600 mt-2">
                {t.signatureUpload.supportedFormats}
              </p>
            </div>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept=".png,.jpg,.jpeg,.svg"
            onChange={(e) => handleFileSelect(e.target.files)}
            disabled={uploading}
          />
        </div>

        {uploading && (
          <div className="mt-4 text-center">
            <div className="inline-flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500 ml-2"></div>
              {t.signatureUpload.uploading}
            </div>
          </div>
        )}
      </div>

      {/* Signatures List */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-bold text-gray-800 mb-4">{t.signatureUpload.yourSignatures}</h2>

        {signatures.length === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto w-16 h-16 text-gray-400 mb-4">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </div>
            <p className="text-gray-600">{t.signatureUpload.noSignatures}</p>
            <p className="text-sm text-gray-500 mt-1">{t.signatureUpload.uploadFirst}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {signatures.map((signature) => (
              <div key={signature.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">
                      {signature.filename}
                    </p>
                    <p className="text-sm text-gray-600">
                      {t.signatureUpload.fileSize}: {formatFileSize(signature.fileSize)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {t.signatureUpload.uploadedOn}: {formatUploadDate(signature.uploadDate)}
                    </p>
                  </div>
                  <button
                    onClick={() => deleteSignature(signature.id)}
                    className="mr-2 text-red-500 hover:text-red-700 transition-colors"
                    title={t.signatureUpload.delete}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SignatureUpload;
