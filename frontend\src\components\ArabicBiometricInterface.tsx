import React, { useState, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { useBiometric } from '../hooks/useBiometric';
import mobileDetection from '../utils/mobileDetection';

interface ArabicBiometricInterfaceProps {
  mode: 'login' | 'enrollment' | 'management';
  email?: string;
  onSuccess?: (data?: any) => void;
  onError?: (message: string) => void;
  onCancel?: () => void;
  className?: string;
}

const ArabicBiometricInterface: React.FC<ArabicBiometricInterfaceProps> = ({
  mode,
  email,
  onSuccess,
  onError,
  onCancel,
  className = ''
}) => {
  const { t } = useLanguage();
  const {
    capabilities,
    isSupported,
    isAvailable,
    biometricStatus,
    credentials,
    isLoading,
    error,
    authenticateBiometric,
    registerBiometric,
    removeBiometric,
    updatePreferences,
    refreshCredentials,
    clearError
  } = useBiometric();

  const [isProcessing, setIsProcessing] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [selectedPreference, setSelectedPreference] = useState('password');

  const mobileCapabilities = mobileDetection.getCapabilities();

  // Load data on mount
  useEffect(() => {
    if (mode === 'management') {
      refreshCredentials();
    }
  }, [mode, refreshCredentials]);

  // Update preference when status changes
  useEffect(() => {
    if (biometricStatus?.preferred_auth_method) {
      setSelectedPreference(biometricStatus.preferred_auth_method);
    }
  }, [biometricStatus]);

  // Handle errors
  useEffect(() => {
    if (error) {
      onError?.(error);
    }
  }, [error, onError]);

  // Get biometric type information in Arabic
  const getBiometricInfo = () => {
    if (mobileCapabilities.supportsFaceID) {
      return {
        name: t.biometric.faceId,
        icon: '👤',
        instruction: t.biometric.faceIdInstruction
      };
    } else if (mobileCapabilities.supportsTouchID) {
      return {
        name: t.biometric.touchId,
        icon: '👆',
        instruction: t.biometric.touchIdInstruction
      };
    } else if (mobileCapabilities.supportsFingerprint) {
      return {
        name: t.biometric.fingerprint,
        icon: '👆',
        instruction: t.biometric.fingerprintInstruction
      };
    } else {
      return {
        name: t.biometric.title,
        icon: '🔐',
        instruction: t.biometric.followDevice
      };
    }
  };

  const biometricInfo = getBiometricInfo();

  // Handle authentication
  const handleAuthentication = async () => {
    if (!email) {
      onError?.(t.auth.email + ' مطلوب');
      return;
    }

    try {
      setIsProcessing(true);
      clearError();

      mobileDetection.triggerHapticFeedback('warning');
      const result = await authenticateBiometric(email);

      if (result.success) {
        mobileDetection.triggerHapticFeedback('success');
        onSuccess?.(result);
      } else {
        mobileDetection.triggerHapticFeedback('error');
        onError?.(result.message || t.biometric.authFailed);
      }
    } catch (err: any) {
      mobileDetection.triggerHapticFeedback('error');
      onError?.(err.message || t.biometric.authFailed);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle enrollment
  const handleEnrollment = async () => {
    try {
      setIsProcessing(true);
      clearError();

      mobileDetection.triggerHapticFeedback('warning');
      const result = await registerBiometric();

      if (result.success) {
        mobileDetection.triggerHapticFeedback('success');
        setSuccessMessage(t.biometric.enrollmentSuccess);
        setShowSuccess(true);
        setTimeout(() => {
          setShowSuccess(false);
          onSuccess?.();
        }, 2000);
      } else {
        mobileDetection.triggerHapticFeedback('error');
        onError?.(result.message || t.biometric.enrollmentFailed);
      }
    } catch (err: any) {
      mobileDetection.triggerHapticFeedback('error');
      onError?.(err.message || t.biometric.enrollmentFailed);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle credential removal
  const handleRemoveCredential = async (credentialId: string) => {
    if (!confirm(`هل أنت متأكد من ${t.biometric.removeDevice}؟`)) {
      return;
    }

    try {
      setIsProcessing(true);
      const result = await removeBiometric(credentialId);
      
      if (result.success) {
        setSuccessMessage(t.biometric.removalSuccess);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 3000);
      }
    } catch (err) {
      // Error handled by hook
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle preference update
  const handlePreferenceUpdate = async () => {
    try {
      setIsProcessing(true);
      const result = await updatePreferences({
        preferredAuthMethod: selectedPreference
      });
      
      if (result.success) {
        setSuccessMessage(t.biometric.preferencesUpdated);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 3000);
      }
    } catch (err) {
      // Error handled by hook
    } finally {
      setIsProcessing(false);
    }
  };

  // Format date in Arabic
  const formatArabicDate = (dateString: string | null) => {
    if (!dateString) return 'لم يتم الاستخدام';
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get device icon
  const getDeviceIcon = (platform: string) => {
    switch (platform?.toLowerCase()) {
      case 'ios': return '📱';
      case 'android': return '🤖';
      case 'windows': return '💻';
      case 'macos': return '🖥️';
      default: return '🔐';
    }
  };

  // Get trust level in Arabic
  const getTrustLevelText = (level: string) => {
    switch (level) {
      case 'trusted': return t.biometric.trusted;
      case 'verified': return t.biometric.verified;
      case 'suspicious': return t.biometric.suspicious;
      default: return t.biometric.unverified;
    }
  };

  // Get trust level color
  const getTrustLevelColor = (level: string) => {
    switch (level) {
      case 'trusted': return 'text-green-600';
      case 'verified': return 'text-blue-600';
      case 'suspicious': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  // Don't render if not supported
  if (!isSupported) {
    return (
      <div className={`arabic-biometric-interface ${className}`} style={{ direction: 'rtl' }}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
          <div className="text-4xl mb-2">❌</div>
          <p className="text-red-700 font-['Almarai']">{t.biometric.notSupported}</p>
        </div>
      </div>
    );
  }

  if (!isAvailable) {
    return (
      <div className={`arabic-biometric-interface ${className}`} style={{ direction: 'rtl' }}>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
          <div className="text-4xl mb-2">⚠️</div>
          <p className="text-yellow-700 font-['Almarai']">{t.biometric.notAvailable}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`arabic-biometric-interface ${className}`} style={{ direction: 'rtl' }}>
      {/* Success Message */}
      {showSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 font-['Almarai']">
          {successMessage}
        </div>
      )}

      {mode === 'login' && (
        <div className="text-center">
          <button
            type="button"
            onClick={handleAuthentication}
            disabled={isLoading || isProcessing || !email}
            aria-label={`${t.biometric.loginWith} ${biometricInfo.name}`}
            aria-describedby="biometric-login-description"
            className={`
              w-full flex items-center justify-center gap-3 px-6 py-4
              bg-gradient-to-r from-blue-600 to-purple-600
              hover:from-blue-700 hover:to-purple-700
              disabled:from-gray-400 disabled:to-gray-500
              text-white font-medium rounded-xl font-['Almarai']
              transition-all duration-300 ease-in-out
              transform hover:scale-105 disabled:hover:scale-100
              shadow-lg hover:shadow-xl disabled:shadow-md
              focus:outline-none focus:ring-4 focus:ring-blue-300
              ${isProcessing ? 'animate-pulse' : ''}
            `}
          >
            {isProcessing ? (
              <>
                <div
                  className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"
                  aria-hidden="true"
                ></div>
                <span>{t.biometric.authenticating}</span>
              </>
            ) : (
              <>
                <span className="text-2xl" aria-hidden="true">{biometricInfo.icon}</span>
                <span>{t.biometric.loginWith} {biometricInfo.name}</span>
              </>
            )}
          </button>
          <div
            id="biometric-login-description"
            className="sr-only"
            aria-live="polite"
          >
            {biometricInfo.instruction}
          </div>
        </div>
      )}

      {mode === 'enrollment' && (
        <div className="text-center">
          <div className="text-6xl mb-4">{biometricInfo.icon}</div>
          <h3 className="text-xl font-bold mb-4 font-['Almarai']">
            {t.biometric.setup}
          </h3>
          <p className="text-gray-600 mb-6 font-['Almarai'] leading-relaxed">
            {t.biometric.setupSubtitle}
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-right">
            <h4 className="font-semibold text-blue-800 mb-3 font-['Almarai']">{t.biometric.benefits}:</h4>
            <ul className="text-blue-700 space-y-2 font-['Almarai']">
              <li className="flex items-center gap-2">
                <span className="text-green-500">✓</span>
                <span>{t.biometric.benefit1}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="text-green-500">✓</span>
                <span>{t.biometric.benefit2}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="text-green-500">✓</span>
                <span>{t.biometric.benefit3}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="text-green-500">✓</span>
                <span>{t.biometric.benefit4}</span>
              </li>
            </ul>
          </div>

          <div className="flex gap-3 justify-center">
            <button
              onClick={handleEnrollment}
              disabled={isProcessing}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai'] disabled:opacity-50"
            >
              {isProcessing ? t.biometric.authenticating : t.biometric.startEnrollment}
            </button>
            <button
              onClick={onCancel}
              className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
            >
              {t.biometric.cancel}
            </button>
          </div>
        </div>
      )}

      {mode === 'management' && (
        <div>
          <h2 className="text-2xl font-bold mb-6 text-gray-800 font-['Almarai']">
            {t.biometric.management}
          </h2>

          {/* Current Status */}
          {biometricStatus && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 font-['Almarai']">{t.biometric.currentStatus}</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm font-['Almarai']">
                  <div>
                    <span className="font-medium">{t.biometric.title}: </span>
                    <span className={biometricStatus.biometric_enabled ? 'text-green-600' : 'text-red-600'}>
                      {biometricStatus.biometric_enabled ? t.biometric.enabled : t.biometric.disabled}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">{t.biometric.preferredMethod}: </span>
                    <span className="text-gray-700">
                      {biometricStatus.preferred_auth_method === 'biometric' ? t.biometric.biometricOnly :
                       biometricStatus.preferred_auth_method === 'both' ? t.biometric.both : t.biometric.passwordOnly}
                    </span>
                  </div>
                  {biometricStatus.biometric_enrolled_at && (
                    <div className="md:col-span-2">
                      <span className="font-medium">{t.biometric.enrolledOn}: </span>
                      <span className="text-gray-700">{formatArabicDate(biometricStatus.biometric_enrolled_at)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Registered Devices */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 font-['Almarai']">{t.biometric.devices}</h3>
            {credentials.length === 0 ? (
              <div className="text-center py-8 text-gray-500 font-['Almarai']">
                <div className="text-4xl mb-2">🔐</div>
                <p>{t.biometric.noDevices}</p>
              </div>
            ) : (
              <div className="space-y-3">
                {credentials.map((credential) => (
                  <div key={credential.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex justify-between items-start">
                      <div className="flex items-start gap-3">
                        <div className="text-2xl">{getDeviceIcon(credential.platform || '')}</div>
                        <div className="font-['Almarai']">
                          <h4 className="font-medium text-gray-800">
                            {credential.device_name || credential.authenticator_name}
                          </h4>
                          <div className="text-sm text-gray-600 space-y-1">
                            <div>{t.biometric.lastUsed}: {formatArabicDate(credential.last_used_at)}</div>
                            <div>{t.biometric.registeredOn}: {formatArabicDate(credential.created_at)}</div>
                            {credential.trust_level && (
                              <div>
                                {t.biometric.trustLevel}: 
                                <span className={`mr-1 ${getTrustLevelColor(credential.trust_level)}`}>
                                  {getTrustLevelText(credential.trust_level)}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => handleRemoveCredential(credential.id)}
                        disabled={isProcessing}
                        className="text-red-600 hover:text-red-800 p-2 rounded transition-colors disabled:opacity-50"
                        title={t.biometric.remove}
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Preferences */}
          {biometricStatus?.biometric_enabled && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 font-['Almarai']">{t.biometric.preferences}</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                    {t.biometric.preferredMethod}
                  </label>
                  <select
                    value={selectedPreference}
                    onChange={(e) => setSelectedPreference(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Almarai']"
                  >
                    <option value="password">{t.biometric.passwordOnly}</option>
                    <option value="biometric">{t.biometric.biometricOnly}</option>
                    <option value="both">{t.biometric.both}</option>
                  </select>
                </div>
                <button
                  onClick={handlePreferenceUpdate}
                  disabled={isLoading || selectedPreference === biometricStatus.preferred_auth_method}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-['Almarai']"
                >
                  {t.biometric.save}
                </button>
              </div>
            </div>
          )}

          {/* Help Section */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-800 mb-2 font-['Almarai']">{t.biometric.help}</h4>
            <ul className="text-sm text-blue-700 space-y-1 font-['Almarai']">
              <li>• {t.biometric.helpInfo1}</li>
              <li>• {t.biometric.helpInfo2}</li>
              <li>• {t.biometric.helpInfo3}</li>
              <li>• {t.biometric.helpInfo4}</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default ArabicBiometricInterface;
