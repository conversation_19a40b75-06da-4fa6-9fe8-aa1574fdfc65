#!/usr/bin/env node

/**
 * Test script for the new signature block format with "DIGITALLY AUTHORIZED" text
 */

const { formatSignatureBlock } = require('./src/services/multilingualTextService');

console.log('🧪 Testing New Signature Block Format');
console.log('=====================================\n');

// Test data
const testSerialNumber = 'DOC123456789';
const testDate = new Date();
const testUserId = 'f1ebb3e-75f6-44dd-8aba-bf2e3e857a04';

console.log('Test Parameters:');
console.log(`Serial Number: ${testSerialNumber}`);
console.log(`Date: ${testDate.toISOString()}`);
console.log(`User ID: ${testUserId}`);
console.log('');

// Test 1: Basic signature block with new features
console.log('📋 Test 1: Complete Signature Block');
console.log('-----------------------------------');

const signatureBlock = formatSignatureBlock(testSerialNumber, testDate, {
  includeVerification: true,
  includeIntegrity: false,
  userId: testUserId,
  userEmail: '<EMAIL>'
});

console.log('Generated signature block:');
signatureBlock.texts.forEach((text, index) => {
  console.log(`${index + 1}. ${text}`);
});

console.log('\nSpacing information:');
signatureBlock.spacing.forEach((item, index) => {
  console.log(`Line ${index + 1}: "${item.text}" at Y=${item.y}, fontSize=${item.fontSize}`);
});

console.log(`\nDirection: ${signatureBlock.direction}`);
console.log(`Language: ${signatureBlock.language}`);

// Test 2: Without user ID
console.log('\n📋 Test 2: Signature Block Without User ID');
console.log('------------------------------------------');

const signatureBlockNoUser = formatSignatureBlock(testSerialNumber, testDate, {
  includeVerification: true,
  includeIntegrity: false,
  userId: null,
  userEmail: null
});

console.log('Generated signature block (no user):');
signatureBlockNoUser.texts.forEach((text, index) => {
  console.log(`${index + 1}. ${text}`);
});

// Test 3: With integrity check
console.log('\n📋 Test 3: Signature Block With Integrity Check');
console.log('-----------------------------------------------');

const signatureBlockWithIntegrity = formatSignatureBlock(testSerialNumber, testDate, {
  includeVerification: true,
  includeIntegrity: true,
  userId: testUserId,
  userEmail: '<EMAIL>'
});

console.log('Generated signature block (with integrity):');
signatureBlockWithIntegrity.texts.forEach((text, index) => {
  console.log(`${index + 1}. ${text}`);
});

// Test 4: Verify Arabic numeral conversion
console.log('\n📋 Test 4: Arabic Numeral Conversion');
console.log('------------------------------------');

const { convertToArabicNumerals } = require('./src/services/multilingualTextService');

const testNumbers = ['123', '456789', '2024', '0123'];
testNumbers.forEach(num => {
  const arabicNum = convertToArabicNumerals(num);
  console.log(`${num} → ${arabicNum}`);
});

// Test 5: Expected output format
console.log('\n📋 Expected Signature Block Layout');
console.log('==================================');
console.log('Below the signature image, the following text should appear:');
console.log('');
console.log('1. موقع رقمياً (Digitally Signed)');
console.log('2. DIGITALLY AUTHORIZED');
console.log('3. معرف المستخدم: [User ID in Arabic numerals]');
console.log('4. الرقم التسلسلي: [Serial Number in Arabic numerals]');
console.log('5. تم التوقيع: [Date and time in Arabic]');
console.log('');
console.log('✅ All text should be right-to-left aligned');
console.log('✅ Arabic numerals should be used for numbers');
console.log('✅ "DIGITALLY AUTHORIZED" should remain in English');

console.log('\n🎯 Test completed successfully!');
