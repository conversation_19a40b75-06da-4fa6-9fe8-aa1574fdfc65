#!/usr/bin/env node

/**
 * Audit System Integration Test
 * Tests the comprehensive user tracking and audit logging system
 */

const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');

const BASE_URL = 'http://localhost:3001/api';
let authToken = '';
let testUserId = '';
let testDocumentId = '';

// Test configuration
const TEST_CONFIG = {
  username: 'audit_test_user',
  email: '<EMAIL>',
  password: 'TestPassword123!',
  testPdfPath: './test-document.pdf'
};

console.log('🧪 Starting Audit System Integration Test...\n');

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null, headers = {}) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        ...headers,
        ...(authToken && { Authorization: `Bearer ${authToken}` })
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Request failed: ${method} ${endpoint}`);
    console.error('Error:', error.response?.data || error.message);
    throw error;
  }
};

// Create a test PDF file
const createTestPDF = () => {
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;

  fs.writeFileSync(TEST_CONFIG.testPdfPath, pdfContent);
  console.log('✅ Test PDF created');
};

// Test 1: User Registration and Authentication
const testAuthentication = async () => {
  console.log('📝 Test 1: User Registration and Authentication');
  
  try {
    // Register test user
    const registerResponse = await makeRequest('POST', '/auth/register', {
      username: TEST_CONFIG.username,
      email: TEST_CONFIG.email,
      password: TEST_CONFIG.password
    });
    
    console.log('✅ User registered successfully');
    
    // Login
    const loginResponse = await makeRequest('POST', '/auth/login', {
      email: TEST_CONFIG.email,
      password: TEST_CONFIG.password
    });
    
    authToken = loginResponse.token;
    testUserId = loginResponse.user.id;
    console.log('✅ User logged in successfully');
    console.log(`   User ID: ${testUserId}`);
    
  } catch (error) {
    console.log('⚠️  User might already exist, trying login...');
    
    const loginResponse = await makeRequest('POST', '/auth/login', {
      email: TEST_CONFIG.email,
      password: TEST_CONFIG.password
    });
    
    authToken = loginResponse.token;
    testUserId = loginResponse.user.id;
    console.log('✅ User logged in successfully');
  }
};

// Test 2: Document Upload and Signing with Audit Logging
const testDocumentOperations = async () => {
  console.log('\n📄 Test 2: Document Upload and Signing');
  
  // Create form data for file upload
  const formData = new FormData();
  formData.append('document', fs.createReadStream(TEST_CONFIG.testPdfPath));
  formData.append('signatureId', '1'); // Assuming signature ID 1 exists
  formData.append('coordinates', JSON.stringify({ x: 100, y: 100 }));
  
  try {
    const signResponse = await axios.post(`${BASE_URL}/documents/sign`, formData, {
      headers: {
        ...formData.getHeaders(),
        Authorization: `Bearer ${authToken}`
      }
    });
    
    testDocumentId = signResponse.data.document.id;
    console.log('✅ Document signed successfully');
    console.log(`   Document ID: ${testDocumentId}`);
    console.log(`   Serial Number: ${signResponse.data.document.serialNumber}`);
    
  } catch (error) {
    console.error('❌ Document signing failed:', error.response?.data || error.message);
    throw error;
  }
};

// Test 3: Document Access and Download
const testDocumentAccess = async () => {
  console.log('\n👁️  Test 3: Document Access and Download');
  
  try {
    // View document (triggers VIEW audit log)
    const viewResponse = await makeRequest('GET', `/documents/${testDocumentId}`);
    console.log('✅ Document viewed successfully');
    
    // Download document (triggers DOWNLOAD audit log)
    const downloadResponse = await axios.get(`${BASE_URL}/documents/${testDocumentId}/download`, {
      headers: { Authorization: `Bearer ${authToken}` },
      responseType: 'arraybuffer'
    });
    
    console.log('✅ Document downloaded successfully');
    console.log(`   Downloaded ${downloadResponse.data.byteLength} bytes`);
    
  } catch (error) {
    console.error('❌ Document access failed:', error.response?.data || error.message);
    throw error;
  }
};

// Test 4: User Statistics and Audit History
const testUserAnalytics = async () => {
  console.log('\n📊 Test 4: User Statistics and Audit History');
  
  try {
    // Get user statistics
    const statsResponse = await makeRequest('GET', '/users/documents/stats');
    console.log('✅ User statistics retrieved:');
    console.log(`   Total Uploaded: ${statsResponse.data.totalUploaded}`);
    console.log(`   Total Signed: ${statsResponse.data.totalSigned}`);
    console.log(`   Total Downloaded: ${statsResponse.data.totalDownloaded}`);
    console.log(`   Total Viewed: ${statsResponse.data.totalViewed}`);
    console.log(`   Signing Rate: ${statsResponse.data.signingRate}%`);
    
    // Get audit history
    const auditResponse = await makeRequest('GET', '/users/audit-history?limit=10');
    console.log('✅ Audit history retrieved:');
    console.log(`   Total audit entries: ${auditResponse.data.pagination.totalCount}`);
    
    auditResponse.data.auditLogs.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.action} at ${log.timestamp}`);
    });
    
    // Get recent activity
    const activityResponse = await makeRequest('GET', '/users/recent-activity?days=1');
    console.log('✅ Recent activity retrieved:');
    
    activityResponse.data.activities.forEach(activity => {
      console.log(`   ${activity.action}: ${activity.count} times`);
    });
    
  } catch (error) {
    console.error('❌ User analytics failed:', error.response?.data || error.message);
    throw error;
  }
};

// Test 5: User Document List
const testUserDocuments = async () => {
  console.log('\n📋 Test 5: User Document List');
  
  try {
    const documentsResponse = await makeRequest('GET', '/users/documents?page=1&limit=10');
    console.log('✅ User documents retrieved:');
    console.log(`   Total documents: ${documentsResponse.data.pagination.totalCount}`);
    
    documentsResponse.data.documents.forEach((doc, index) => {
      console.log(`   ${index + 1}. ${doc.originalFilename} (${doc.status})`);
    });
    
  } catch (error) {
    console.error('❌ User documents retrieval failed:', error.response?.data || error.message);
    throw error;
  }
};

// Test 6: Security - Unauthorized Access
const testSecurity = async () => {
  console.log('\n🔒 Test 6: Security - Unauthorized Access');
  
  try {
    // Try to access without token
    await axios.get(`${BASE_URL}/documents/${testDocumentId}`);
    console.log('❌ Security test failed - unauthorized access allowed');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Security test passed - unauthorized access denied');
    } else {
      console.log('⚠️  Unexpected error in security test:', error.response?.status);
    }
  }
};

// Cleanup
const cleanup = () => {
  try {
    if (fs.existsSync(TEST_CONFIG.testPdfPath)) {
      fs.unlinkSync(TEST_CONFIG.testPdfPath);
      console.log('✅ Test files cleaned up');
    }
  } catch (error) {
    console.log('⚠️  Cleanup warning:', error.message);
  }
};

// Main test execution
const runTests = async () => {
  try {
    createTestPDF();
    await testAuthentication();
    await testDocumentOperations();
    await testDocumentAccess();
    await testUserAnalytics();
    await testUserDocuments();
    await testSecurity();
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('✅ User authentication and registration');
    console.log('✅ Document upload and signing with audit logging');
    console.log('✅ Document access and download tracking');
    console.log('✅ User statistics and audit history');
    console.log('✅ User document management');
    console.log('✅ Security and access control');
    
  } catch (error) {
    console.error('\n❌ Test suite failed:', error.message);
    process.exit(1);
  } finally {
    cleanup();
  }
};

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test execution failed:', error);
    cleanup();
    process.exit(1);
  });
}

module.exports = { runTests };
