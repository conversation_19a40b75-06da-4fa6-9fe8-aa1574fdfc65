-- E-Signature System Database Schema
-- Run this script in your PostgreSQL database

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON>HA<PERSON>(255),
    language VARCHAR(3) DEFAULT 'ar',
    text_direction VARCHAR(3) DEFAULT 'rtl',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Signatures table
CREATE TABLE signatures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    encrypted_data TEXT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    text_direction VARCHAR(3) DEFAULT 'rtl'
);

-- Documents table
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    original_filename VARCHAR(255) NOT NULL,
    signed_filename VARCHAR(255) NOT NULL,
    serial_number VARCHAR(100) UNIQUE NOT NULL,
    signed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    digital_signature TEXT,
    signature_coordinates JSONB,
    status VARCHAR(50) DEFAULT 'signed',
    language VARCHAR(3) DEFAULT 'ar',
    text_direction VARCHAR(3) DEFAULT 'rtl'
);

-- Audit logs table
CREATE TABLE logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    details JSONB
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_signatures_user_id ON signatures(user_id);
CREATE INDEX idx_signatures_upload_date ON signatures(upload_date);
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_serial_number ON documents(serial_number);
CREATE INDEX idx_documents_signed_date ON documents(signed_date);
CREATE INDEX idx_logs_user_id ON logs(user_id);
CREATE INDEX idx_logs_timestamp ON logs(timestamp);
CREATE INDEX idx_logs_action ON logs(action);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create Arabic templates table
CREATE TABLE arabic_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_type VARCHAR(50) NOT NULL,
    arabic_content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert Arabic templates
INSERT INTO arabic_templates (template_type, arabic_content) VALUES
('email_verification', 'مرحباً، يرجى تأكيد عنوان بريدك الإلكتروني'),
('document_signed', 'تم توقيع المستند بنجاح'),
('signature_uploaded', 'تم رفع التوقيع بنجاح'),
('error_invalid_file', 'نوع الملف غير صالح'),
('error_upload_failed', 'فشل في رفع الملف');

-- Create trigger for arabic_templates table
CREATE TRIGGER update_arabic_templates_updated_at
    BEFORE UPDATE ON arabic_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create migration script for existing data
-- This will update all existing users and documents to use Arabic language and RTL direction
CREATE OR REPLACE FUNCTION migrate_to_arabic_only()
RETURNS void AS $$
BEGIN
    -- Update existing users
    UPDATE users SET
        language = 'ar',
        text_direction = 'rtl'
    WHERE language IS NULL OR language != 'ar';

    -- Update existing documents
    UPDATE documents SET
        language = 'ar',
        text_direction = 'rtl'
    WHERE language IS NULL OR language != 'ar';

    -- Update existing serial numbers to Arabic format
    UPDATE documents SET
        serial_number = CONCAT('وثيقة-', SUBSTRING(serial_number, 5))
    WHERE serial_number LIKE 'ESG-%';
END;
$$ LANGUAGE plpgsql;

-- Execute the migration function
SELECT migrate_to_arabic_only();
