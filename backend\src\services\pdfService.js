const { PDFDocument, rgb, StandardFonts } = require('pdf-lib');

// Optional fontkit import for Arabic font support
let fontkit = null;
try {
  fontkit = require('fontkit');
} catch (error) {
  console.warn('Fontkit not available - Arabic font embedding disabled');
}
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs').promises;
const {
  containsArabic,
  detectTextDirection,
  processBidiText,
  getAppropriateFont,
  getTextPosition,
  calculateTextWidth
} = require('./arabicFontService');
const {
  formatSignatureBlock,
  getOptimalFontSize
} = require('./multilingualTextService');

const embedSignatureInPDF = async (pdfBuffer, signatureBuffer, options = {}) => {
  try {
    // Validate PDF buffer
    if (!pdfBuffer || pdfBuffer.length === 0) {
      throw new Error('Invalid PDF buffer: Buffer is empty or null');
    }

    // Check PDF header
    const pdfHeader = pdfBuffer.slice(0, 5).toString('ascii');
    if (!pdfHeader.startsWith('%PDF-')) {
      throw new Error('Invalid PDF file: Missing PDF header. Please ensure the file is a valid PDF document.');
    }

    // Default options for Arabic-only system
    const {
      coordinates = { x: 50, y: 50 },
      signatureSize = { width: 150, height: 75 },
      page = 0, // First page by default
      userId = null,
      userEmail = null
    } = options;

    // Load the PDF document with better error handling
    let pdfDoc;
    try {
      pdfDoc = await PDFDocument.load(pdfBuffer);
    } catch (pdfError) {
      console.error('PDF loading error:', pdfError.message);
      throw new Error(`Failed to load PDF document: ${pdfError.message}. Please ensure the file is not corrupted and is a valid PDF.`);
    }

    // Register fontkit for custom font support (if available)
    if (fontkit) {
      pdfDoc.registerFontkit(fontkit);
    }

    const pages = pdfDoc.getPages();

    if (page >= pages.length) {
      throw new Error('Invalid page number');
    }

    const targetPage = pages[page];
    const { width: pageWidth, height: pageHeight } = targetPage.getSize();

    // Always use Arabic and RTL for Arabic-only system
    const hasArabicContent = true;
    const textDirection = 'rtl';

    // Validate signature buffer
    if (!signatureBuffer || signatureBuffer.length === 0) {
      throw new Error('Invalid signature buffer: Buffer is empty or null');
    }

    // Embed signature image
    let signatureImage;
    try {
      // Try PNG first
      signatureImage = await pdfDoc.embedPng(signatureBuffer);
    } catch (pngError) {
      try {
        // Try JPG if PNG fails
        signatureImage = await pdfDoc.embedJpg(signatureBuffer);
      } catch (jpgError) {
        console.error('Signature embedding errors:', { pngError: pngError.message, jpgError: jpgError.message });
        throw new Error('Unsupported signature image format. Please use PNG or JPG format. Make sure the image file is not corrupted.');
      }
    }

    // Calculate signature dimensions
    const signatureDims = signatureImage.scale(0.5);
    const finalWidth = signatureSize.width || signatureDims.width;
    const finalHeight = signatureSize.height || signatureDims.height;

    // Calculate RTL positioning for Arabic documents
    const defaultX = pageWidth - finalWidth - 50; // 50px margin from right
    const defaultY = 50; // 50px from bottom

    const finalX = coordinates.x !== undefined ?
      (pageWidth - coordinates.x - finalWidth) : defaultX;
    const finalY = coordinates.y || defaultY;

    // Draw signature on the page
    targetPage.drawImage(signatureImage, {
      x: finalX,
      y: finalY,
      width: finalWidth,
      height: finalHeight,
    });

    // Generate serial number
    const serialNumber = generateSerialNumber();
    const timestamp = new Date();

    console.log(`Generated serial number: ${serialNumber}`);
    console.log(`PDF page dimensions: ${pageWidth}x${pageHeight}`);
    console.log(`Signature position: (${finalX}, ${finalY}) with size ${finalWidth}x${finalHeight}`);

    // Create Arabic signature block with user information
    const signatureBlock = formatSignatureBlock(serialNumber, timestamp, {
      includeVerification: true,
      includeIntegrity: false,
      userId: userId,
      userEmail: userEmail
    });

    console.log('Initial signature block created:', signatureBlock);

    // LOAD STANDARD FONT DIRECTLY - Simple and reliable approach
    console.log('🔄 Loading Helvetica font for text rendering...');
    const customFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const useArabicText = false; // Use English text for reliability

    console.log('✅ Helvetica font loaded successfully');
    console.log('   Font type:', typeof customFont);
    console.log('   Font is valid:', customFont !== null && customFont !== undefined);

    // Create fallback signature block for when Arabic fonts are not available
    let finalSignatureBlock = signatureBlock;
    if (!useArabicText) {
      // Create English fallback signature block with better visibility
      const englishSerialNumber = serialNumber.replace('وثيقة-', 'DOC-');
      const englishTimestamp = timestamp.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });

      finalSignatureBlock = {
        texts: [
          'DIGITALLY SIGNED',
          `Serial: ${englishSerialNumber}`,
          `Date: ${englishTimestamp}`
        ],
        spacing: [
          { text: 'DIGITALLY SIGNED', y: 0, fontSize: 14, lineHeight: 18 },
          { text: `Serial: ${englishSerialNumber}`, y: 18, fontSize: 12, lineHeight: 16 },
          { text: `Date: ${englishTimestamp}`, y: 34, fontSize: 10, lineHeight: 14 }
        ],
        direction: 'ltr',
        language: 'en'
      };

      console.log('Using English fallback signature block:', finalSignatureBlock);
    } else {
      console.log('Using Arabic signature block:', finalSignatureBlock);
    }

    // Position text below signature with proper alignment and bounds checking
    let textY = Math.max(finalY - 20, 30); // Ensure text doesn't go below 30px from bottom
    const minFontSize = 12; // Increase minimum font size for better visibility

    console.log(`Embedding signature text at position: signature(${finalX}, ${finalY}), text starts at Y: ${textY}`);
    console.log(`Signature block contains ${finalSignatureBlock.texts.length} text lines:`, finalSignatureBlock.texts);

    // Use for...of loop instead of forEach to support async/await
    for (let index = 0; index < finalSignatureBlock.texts.length; index++) {
      const text = finalSignatureBlock.texts[index];

      // Get optimal font size for each text line with minimum size
      const spacingFontSize = finalSignatureBlock.spacing[index]?.fontSize || 8;
      const fontSize = Math.max(spacingFontSize, minFontSize);
      const textWidth = calculateTextWidth(text, fontSize);

      // Calculate text position based on direction with better bounds checking
      let textX;
      if (finalSignatureBlock.direction === 'rtl' && useArabicText) {
        textX = Math.min(pageWidth - textWidth - 20, pageWidth - 50); // Right-aligned for Arabic with margin
      } else {
        textX = Math.max(finalX, 20); // Left-aligned for English with margin
      }

      // Ensure text stays within page bounds
      textX = Math.max(20, Math.min(textX, pageWidth - textWidth - 20));
      textY = Math.max(20, textY); // Ensure text doesn't go below page

      console.log(`Drawing text "${text}" at (${textX}, ${textY}) with font size ${fontSize}`);

      try {
        targetPage.drawText(text, {
          x: textX,
          y: textY,
          size: fontSize,
          font: customFont,
          color: rgb(0, 0, 0),
        });
        console.log(`✅ Successfully drew text: "${text}"`);
      } catch (textError) {
        console.error(`❌ Failed to draw text "${text}":`, textError.message);
        console.error('   Font type:', typeof customFont);
        console.error('   Font is valid:', customFont !== null && customFont !== undefined);
      }

      textY -= Math.max(fontSize + 8, 20); // Move to next line with adequate spacing
    }

    // Generate digital signature hash
    const digitalSignature = generateDigitalSignature(pdfBuffer, signatureBuffer, serialNumber);

    // Save the modified PDF
    const pdfBytes = await pdfDoc.save();

    return {
      pdfBytes,
      serialNumber,
      digitalSignature,
      signatureCoordinates: {
        x: finalX,
        y: finalY,
        width: finalWidth,
        height: finalHeight,
        page: page,
        textDirection: 'rtl'
      },
      timestamp,
      metadata: {
        textDirection: 'rtl',
        hasArabicContent: true,
        language: 'ar'
      }
    };
  } catch (error) {
    console.error('خطأ في تضمين التوقيع في PDF:', error);
    throw new Error(`فشل في تضمين التوقيع في PDF: ${error.message}`);
  }
};

// Extract text content from PDF page for language detection
const extractPageText = async (page) => {
  try {
    // This is a simplified text extraction
    // In production, you might want to use a more sophisticated method
    const textContent = page.node.Contents;
    if (textContent) {
      // Basic text extraction - in production use pdf-parse or similar
      return textContent.toString();
    }
    return '';
  } catch (error) {
    console.warn('Could not extract page text:', error);
    return '';
  }
};

const generateSerialNumber = () => {
  const timestamp = Date.now().toString();
  const randomBytes = crypto.randomBytes(8).toString('hex');
  const hash = crypto.createHash('sha256').update(timestamp + randomBytes).digest('hex');
  return `وثيقة-${hash.substring(0, 16).toUpperCase()}`;
};

const generateDigitalSignature = (pdfBuffer, signatureBuffer, serialNumber) => {
  const combinedData = Buffer.concat([
    pdfBuffer,
    signatureBuffer,
    Buffer.from(serialNumber, 'utf8'),
    Buffer.from(Date.now().toString(), 'utf8')
  ]);
  
  return crypto.createHash('sha256').update(combinedData).digest('hex');
};

const verifyDocumentIntegrity = (originalHash, currentPdfBuffer) => {
  // This is a simplified verification - in production you'd want more sophisticated verification
  const currentHash = crypto.createHash('sha256').update(currentPdfBuffer).digest('hex');
  return originalHash === currentHash;
};

const extractSignatureInfo = async (pdfBuffer) => {
  try {
    const pdfDoc = await PDFDocument.load(pdfBuffer);
    const pages = pdfDoc.getPages();
    
    // This is a simplified extraction - in a real implementation you'd parse the PDF content
    // to extract embedded signature information
    return {
      hasSignature: true, // Placeholder
      pageCount: pages.length,
      // In a real implementation, you'd extract actual signature metadata
    };
  } catch (error) {
    throw new Error(`Failed to extract signature info: ${error.message}`);
  }
};

module.exports = {
  embedSignatureInPDF,
  generateSerialNumber,
  generateDigitalSignature,
  verifyDocumentIntegrity,
  extractSignatureInfo,
  extractPageText
};
