import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

// Import CSS files for react-pdf
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set up PDF.js worker with explicit version
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

// Log PDF.js version for debugging
console.log('DocumentViewer: Using PDF.js version:', pdfjs.version);
console.log('DocumentViewer: Worker URL:', pdfjs.GlobalWorkerOptions.workerSrc);

interface DocumentViewerProps {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
  documentName: string;
  onError?: (error: string) => void;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  isOpen,
  onClose,
  documentId,
  documentName,
  onError
}) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [pdfData, setPdfData] = useState<string | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  const fetchPdfData = useCallback(async () => {
    try {
      setLoading(true);
      setError('');
      setPdfData(null);

      const token = localStorage.getItem('token');
      console.log('DocumentViewer: Token available:', !!token);
      console.log('DocumentViewer: Document ID:', documentId);

      if (!token) {
        throw new Error('لا يوجد رمز مصادقة. يرجى تسجيل الدخول مرة أخرى.');
      }

      const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
      const url = `${API_BASE_URL}/documents/${documentId}/view?token=${encodeURIComponent(token)}`;

      console.log('DocumentViewer: Setting PDF URL for direct loading:', url);

      // Set URL for direct loading - let react-pdf handle the fetching
      setPdfUrl(url);
      setLoading(false); // Allow Document component to render

    } catch (error) {
      console.error('DocumentViewer: Failed to fetch PDF data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setError(`فشل في تحميل المستند: ${errorMessage}`);
      onError?.(errorMessage);
    }
  }, [documentId, onError]);

  // Fetch PDF data when component opens
  useEffect(() => {
    console.log('DocumentViewer: useEffect triggered', { isOpen, documentId });
    if (isOpen && documentId) {
      console.log('DocumentViewer: Starting PDF fetch...');
      fetchPdfData();
    }
  }, [isOpen, documentId, fetchPdfData]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setPageNumber(1);
      setScale(1.0);
      setNumPages(0);
      setError('');

      // Clean up blob URL if it exists
      if (pdfData && pdfData.startsWith('blob:')) {
        URL.revokeObjectURL(pdfData);
      }
      setPdfData(null);
      setPdfUrl(null);
      setIsFullscreen(false);
    }
  }, [isOpen, pdfData]);

  // Debug state changes
  useEffect(() => {
    console.log('DocumentViewer: State change', {
      loading,
      pdfData: !!pdfData,
      pdfUrl: !!pdfUrl,
      error: !!error,
      numPages,
      isOpen
    });
  }, [loading, pdfData, pdfUrl, error, numPages, isOpen]);

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    console.log('DocumentViewer: PDF loaded successfully with', numPages, 'pages');
    setNumPages(numPages);
    setLoading(false);
    setError('');
  }, []);

  const onDocumentLoadError = useCallback((error: Error) => {
    console.error('DocumentViewer: PDF load error:', error);
    console.error('DocumentViewer: Error details:', {
      message: error.message,
      name: error.name,
      stack: error.stack
    });
    const errorMessage = 'فشل في تحميل المستند. يرجى المحاولة مرة أخرى.';
    setError(errorMessage);
    setLoading(false);
    onError?.(errorMessage);
  }, [onError]);

  // Memoize PDF.js options to prevent unnecessary reloads
  const pdfOptions = useMemo(() => ({
    cMapUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`,
    cMapPacked: true,
    standardFontDataUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/standard_fonts/`,
    verbosity: 1
  }), []);

  const onDocumentLoadProgress = useCallback(({ loaded, total }: { loaded: number; total: number }) => {
    const progress = total > 0 ? Math.round((loaded / total) * 100) : 0;
    console.log(`DocumentViewer: PDF loading progress: ${progress}% (${loaded}/${total} bytes)`);
  }, []);

  const goToPrevPage = useCallback(() => {
    setPageNumber(prev => Math.max(1, prev - 1));
  }, []);

  const goToNextPage = useCallback(() => {
    setPageNumber(prev => Math.min(numPages, prev + 1));
  }, [numPages]);

  const zoomIn = useCallback(() => {
    setScale(prev => Math.min(3.0, prev + 0.25));
  }, []);

  const zoomOut = useCallback(() => {
    setScale(prev => Math.max(0.5, prev - 0.25));
  }, []);

  const resetZoom = useCallback(() => {
    setScale(1.0);
  }, []);

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isOpen) return;

    switch (event.key) {
      case 'Escape':
        if (isFullscreen) {
          setIsFullscreen(false);
        } else {
          onClose();
        }
        break;
      case 'ArrowLeft':
        goToNextPage(); // In RTL, left arrow goes to next page
        break;
      case 'ArrowRight':
        goToPrevPage(); // In RTL, right arrow goes to previous page
        break;
      case '+':
      case '=':
        zoomIn();
        break;
      case '-':
        zoomOut();
        break;
      case '0':
        resetZoom();
        break;
    }
  }, [isOpen, isFullscreen, onClose, goToNextPage, goToPrevPage, zoomIn, zoomOut, resetZoom]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  if (!isOpen) {
    console.log('DocumentViewer: Not open, returning null');
    return null;
  }

  console.log('DocumentViewer: Rendering modal', { documentId, documentName, loading, error, pdfData: !!pdfData });

  return (
    <div 
      className={`fixed inset-0 z-50 ${isFullscreen ? 'bg-black' : 'bg-black bg-opacity-75'} flex items-center justify-center`}
      dir="rtl"
      role="dialog"
      aria-modal="true"
      aria-labelledby="document-viewer-title"
    >
      <div className={`bg-white ${isFullscreen ? 'w-full h-full' : 'w-11/12 h-5/6 max-w-6xl mx-4 rounded-lg shadow-xl'} flex flex-col`}>
        {/* Header */}
        <div className="flex items-center justify-between p-3 sm:p-4 border-b border-gray-200">
          <h2
            id="document-viewer-title"
            className="text-base sm:text-lg font-bold text-gray-900 font-['Almarai'] truncate flex-1 ml-4"
          >
            عرض المستند: {documentName}
          </h2>
          
          <div className="flex items-center space-x-1 sm:space-x-2 space-x-reverse">
            {/* Zoom Controls */}
            <div className="hidden sm:flex items-center space-x-1 space-x-reverse border rounded-md">
              <button
                onClick={zoomOut}
                disabled={scale <= 0.5}
                className="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="تصغير"
                title="تصغير (مفتاح -)"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              
              <span className="px-2 text-sm font-['Almarai'] min-w-[60px] text-center">
                {Math.round(scale * 100)}%
              </span>
              
              <button
                onClick={zoomIn}
                disabled={scale >= 3.0}
                className="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="تكبير"
                title="تكبير (مفتاح +)"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
              
              <button
                onClick={resetZoom}
                className="p-2 hover:bg-gray-100 text-xs font-['Almarai']"
                aria-label="إعادة تعيين التكبير"
                title="إعادة تعيين التكبير (مفتاح 0)"
              >
                100%
              </button>
            </div>

            {/* Fullscreen Toggle */}
            <button
              onClick={toggleFullscreen}
              className="p-2 hover:bg-gray-100 rounded-md"
              aria-label={isFullscreen ? "إنهاء ملء الشاشة" : "ملء الشاشة"}
              title={isFullscreen ? "إنهاء ملء الشاشة" : "ملء الشاشة"}
            >
              {isFullscreen ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0-4.5l5.5 5.5" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                </svg>
              )}
            </button>

            {/* Close Button */}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-md"
              aria-label="إغلاق عارض المستند"
              title="إغلاق (مفتاح Escape)"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* PDF Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Page Navigation */}
          {numPages > 0 && (
            <div className="flex items-center justify-center p-2 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center space-x-4 space-x-reverse">
                <button
                  onClick={goToPrevPage}
                  disabled={pageNumber <= 1}
                  className="p-2 hover:bg-gray-200 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="الصفحة السابقة"
                  title="الصفحة السابقة (سهم يمين)"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
                
                <span className="text-sm font-['Almarai'] px-4">
                  صفحة {pageNumber} من {numPages}
                </span>
                
                <button
                  onClick={goToNextPage}
                  disabled={pageNumber >= numPages}
                  className="p-2 hover:bg-gray-200 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="الصفحة التالية"
                  title="الصفحة التالية (سهم يسار)"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* PDF Viewer */}
          <div className="flex-1 overflow-auto bg-gray-100 flex items-center justify-center p-4">
            {loading && !pdfUrl && !error && (
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-600 font-['Almarai']">جاري تحميل المستند...</p>
              </div>
            )}

            {error && (
              <div className="text-center max-w-md">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2 font-['Almarai']">خطأ في تحميل المستند</h3>
                <p className="text-gray-600 font-['Almarai'] mb-4">{error}</p>
                <div className="space-x-2 space-x-reverse">
                  <button
                    onClick={fetchPdfData}
                    className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 font-['Almarai']"
                  >
                    إعادة المحاولة
                  </button>
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 font-['Almarai']"
                  >
                    إعادة تحميل الصفحة
                  </button>
                </div>
              </div>
            )}

            {pdfUrl && !error && (
              <Document
                file={pdfUrl}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={onDocumentLoadError}
                onLoadProgress={onDocumentLoadProgress}
                loading={
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                    <p className="text-gray-600 font-['Almarai']">جاري معالجة المستند...</p>
                  </div>
                }
                error={
                  <div className="text-center">
                    <p className="text-red-600 font-['Almarai']">فشل في معالجة المستند</p>
                  </div>
                }
                options={pdfOptions}
              >
                <Page
                  pageNumber={pageNumber}
                  scale={scale}
                  renderTextLayer={true}
                  renderAnnotationLayer={true}
                  className="shadow-lg"
                />
              </Document>
            )}
          </div>
        </div>

        {/* Keyboard Shortcuts Help */}
        <div className="p-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-600 font-['Almarai'] text-center">
          اختصارات لوحة المفاتيح: ← → للتنقل بين الصفحات | + - للتكبير والتصغير | 0 لإعادة التعيين | Esc للإغلاق
        </div>
      </div>
    </div>
  );
};

export default DocumentViewer;
