const request = require('supertest');
const crypto = require('crypto');
const { query } = require('../models/database');
const app = require('../app');

/**
 * Biometric Security Testing Suite
 * Tests WebAuthn implementation security, replay attack prevention, and encryption standards
 */

describe('Biometric Security Tests', () => {
  let testUser;
  let authToken;
  let testCredential;

  beforeAll(async () => {
    // Create test user
    const hashedPassword = await require('bcryptjs').hash('testpassword123', 12);
    const userResult = await query(
      'INSERT INTO users (email, password_hash, biometric_enabled) VALUES ($1, $2, $3) RETURNING *',
      ['<EMAIL>', hashedPassword, true]
    );
    testUser = userResult.rows[0];

    // Login to get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'testpassword123'
      });
    
    authToken = loginResponse.body.token;
  });

  afterAll(async () => {
    // Cleanup test data
    await query('DELETE FROM biometric_auth_logs WHERE user_id = $1', [testUser.id]);
    await query('DELETE FROM biometric_credentials WHERE user_id = $1', [testUser.id]);
    await query('DELETE FROM registered_devices WHERE user_id = $1', [testUser.id]);
    await query('DELETE FROM users WHERE id = $1', [testUser.id]);
  });

  describe('Challenge Generation Security', () => {
    test('should generate cryptographically secure challenges', async () => {
      const response = await request(app)
        .post('/api/biometric/register/challenge')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          deviceInfo: {
            platform: 'test',
            browser: 'test'
          }
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.challengeId).toBeDefined();
      
      // Challenge should be base64url encoded and at least 32 bytes
      const challengeBuffer = Buffer.from(response.body.challengeId, 'base64url');
      expect(challengeBuffer.length).toBeGreaterThanOrEqual(32);
    });

    test('should generate unique challenges for each request', async () => {
      const challenges = [];
      
      for (let i = 0; i < 10; i++) {
        const response = await request(app)
          .post('/api/biometric/register/challenge')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            deviceInfo: {
              platform: 'test',
              browser: 'test'
            }
          });
        
        challenges.push(response.body.challengeId);
      }

      // All challenges should be unique
      const uniqueChallenges = new Set(challenges);
      expect(uniqueChallenges.size).toBe(10);
    });

    test('should expire challenges after timeout', async () => {
      const response = await request(app)
        .post('/api/biometric/register/challenge')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          deviceInfo: {
            platform: 'test',
            browser: 'test'
          }
        });

      const challengeId = response.body.challengeId;

      // Simulate expired challenge by updating timestamp
      await query(
        'UPDATE biometric_auth_logs SET timestamp = NOW() - INTERVAL \'10 minutes\' WHERE challenge = $1',
        [challengeId]
      );

      // Try to use expired challenge
      const completionResponse = await request(app)
        .post('/api/biometric/register/complete')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          challengeId,
          credentialId: 'test-credential',
          publicKey: 'test-public-key',
          attestationObject: 'test-attestation',
          clientDataJSON: Buffer.from(JSON.stringify({
            challenge: challengeId,
            origin: 'http://localhost:3000',
            type: 'webauthn.create'
          })).toString('base64url')
        });

      expect(completionResponse.status).toBe(400);
      expect(completionResponse.body.success).toBe(false);
      expect(completionResponse.body.message).toContain('منتهي الصلاحية');
    });
  });

  describe('Origin Validation', () => {
    test('should reject requests from unauthorized origins', async () => {
      const response = await request(app)
        .post('/api/biometric/auth/challenge')
        .set('Origin', 'https://malicious-site.com')
        .send({
          email: '<EMAIL>'
        });

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('غير مصرح به');
    });

    test('should accept requests from authorized origins', async () => {
      const response = await request(app)
        .post('/api/biometric/auth/challenge')
        .set('Origin', 'http://localhost:3000')
        .send({
          email: '<EMAIL>'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce rate limits on biometric operations', async () => {
      const requests = [];
      
      // Make multiple rapid requests
      for (let i = 0; i < 12; i++) {
        requests.push(
          request(app)
            .post('/api/biometric/auth/challenge')
            .set('Origin', 'http://localhost:3000')
            .send({
              email: '<EMAIL>'
            })
        );
      }

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Input Validation', () => {
    test('should validate required fields in registration', async () => {
      const challengeResponse = await request(app)
        .post('/api/biometric/register/challenge')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          deviceInfo: {
            platform: 'test',
            browser: 'test'
          }
        });

      const challengeId = challengeResponse.body.challengeId;

      // Test missing required fields
      const testCases = [
        { challengeId: null },
        { challengeId, credentialId: null },
        { challengeId, credentialId: 'test', publicKey: null },
        { challengeId, credentialId: 'test', publicKey: 'test', attestationObject: null },
        { challengeId, credentialId: 'test', publicKey: 'test', attestationObject: 'test', clientDataJSON: null }
      ];

      for (const testCase of testCases) {
        const response = await request(app)
          .post('/api/biometric/register/complete')
          .set('Authorization', `Bearer ${authToken}`)
          .send(testCase);

        expect(response.status).toBe(400);
        expect(response.body.success).toBe(false);
        expect(response.body.message).toContain('غير مكتملة');
      }
    });

    test('should sanitize device information', async () => {
      const maliciousDeviceInfo = {
        platform: '<script>alert("xss")</script>',
        browser: 'test<>',
        deviceName: 'test'.repeat(200), // Very long string
        authenticatorName: '<img src=x onerror=alert(1)>'
      };

      const response = await request(app)
        .post('/api/biometric/register/challenge')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          deviceInfo: maliciousDeviceInfo
        });

      expect(response.status).toBe(200);
      
      // Check that malicious content was sanitized in logs
      const logResult = await query(
        'SELECT device_info FROM biometric_auth_logs WHERE user_id = $1 ORDER BY timestamp DESC LIMIT 1',
        [testUser.id]
      );

      const deviceInfo = logResult.rows[0].device_info;
      expect(deviceInfo.platform).not.toContain('<script>');
      expect(deviceInfo.browser).not.toContain('<>');
      expect(deviceInfo.deviceName.length).toBeLessThanOrEqual(100);
      expect(deviceInfo.authenticatorName).not.toContain('<img');
    });
  });

  describe('Replay Attack Prevention', () => {
    test('should prevent replay attacks using counter validation', async () => {
      // First, create a test credential
      const credentialId = crypto.randomBytes(32).toString('base64url');
      const publicKey = crypto.randomBytes(64).toString('base64url');
      
      await query(
        'INSERT INTO biometric_credentials (user_id, credential_id, public_key, counter) VALUES ($1, $2, $3, $4)',
        [testUser.id, credentialId, publicKey, 100]
      );

      // Try to authenticate with a lower counter (replay attack)
      const challengeResponse = await request(app)
        .post('/api/biometric/auth/challenge')
        .set('Origin', 'http://localhost:3000')
        .send({
          email: '<EMAIL>'
        });

      const challengeId = challengeResponse.body.challengeId;

      // Create authenticator data with counter = 50 (lower than stored 100)
      const authenticatorData = Buffer.alloc(37);
      authenticatorData.writeUInt32BE(50, 33); // Counter at bytes 33-36

      const response = await request(app)
        .post('/api/biometric/auth/complete')
        .set('Origin', 'http://localhost:3000')
        .send({
          challengeId,
          credentialId,
          authenticatorData: authenticatorData.toString('base64url'),
          signature: 'test-signature',
          clientDataJSON: Buffer.from(JSON.stringify({
            challenge: challengeId,
            origin: 'http://localhost:3000',
            type: 'webauthn.get'
          })).toString('base64url')
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('فشل في التحقق');

      // Check that a security log was created
      const logResult = await query(
        'SELECT * FROM biometric_auth_logs WHERE user_id = $1 AND error_code = $2',
        [testUser.id, 'REPLAY_ATTACK']
      );

      expect(logResult.rows.length).toBeGreaterThan(0);
    });
  });

  describe('Data Encryption and Storage', () => {
    test('should not store biometric data', async () => {
      // Check that no biometric templates are stored in database
      const tables = ['biometric_credentials', 'biometric_auth_logs', 'registered_devices'];
      
      for (const table of tables) {
        const result = await query(`SELECT column_name FROM information_schema.columns WHERE table_name = '${table}'`);
        const columns = result.rows.map(row => row.column_name);
        
        // Ensure no columns that might contain biometric data
        const biometricColumns = columns.filter(col => 
          col.includes('biometric_data') || 
          col.includes('template') || 
          col.includes('fingerprint_data') ||
          col.includes('face_data')
        );
        
        expect(biometricColumns.length).toBe(0);
      }
    });

    test('should store only public keys and metadata', async () => {
      const credentialResult = await query(
        'SELECT * FROM biometric_credentials WHERE user_id = $1',
        [testUser.id]
      );

      if (credentialResult.rows.length > 0) {
        const credential = credentialResult.rows[0];
        
        // Should have public key but no private data
        expect(credential.public_key).toBeDefined();
        expect(credential.credential_id).toBeDefined();
        expect(credential.counter).toBeDefined();
        
        // Should not have any biometric templates
        expect(credential.biometric_template).toBeUndefined();
        expect(credential.fingerprint_data).toBeUndefined();
        expect(credential.face_data).toBeUndefined();
      }
    });
  });

  describe('Session Security', () => {
    test('should generate secure JWT tokens for biometric authentication', async () => {
      // This would be tested with a successful biometric authentication
      // For now, we'll test the token structure
      const jwt = require('jsonwebtoken');
      
      const token = jwt.sign(
        { userId: testUser.id, authMethod: 'biometric' },
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      expect(decoded.userId).toBe(testUser.id);
      expect(decoded.authMethod).toBe('biometric');
      expect(decoded.exp).toBeGreaterThan(Date.now() / 1000);
    });
  });

  describe('Error Handling Security', () => {
    test('should not leak sensitive information in error messages', async () => {
      const response = await request(app)
        .post('/api/biometric/auth/complete')
        .set('Origin', 'http://localhost:3000')
        .send({
          challengeId: 'invalid-challenge',
          credentialId: 'invalid-credential',
          authenticatorData: 'invalid-data',
          signature: 'invalid-signature',
          clientDataJSON: 'invalid-json'
        });

      expect(response.status).toBe(400);
      expect(response.body.message).toBeDefined();
      
      // Error message should be in Arabic and not leak internal details
      expect(response.body.message).not.toContain('SQL');
      expect(response.body.message).not.toContain('database');
      expect(response.body.message).not.toContain('internal');
      expect(response.body.message).not.toContain('stack');
    });
  });

  describe('Audit Logging', () => {
    test('should log all biometric authentication attempts', async () => {
      const initialLogCount = await query(
        'SELECT COUNT(*) FROM biometric_auth_logs WHERE user_id = $1',
        [testUser.id]
      );

      await request(app)
        .post('/api/biometric/auth/challenge')
        .set('Origin', 'http://localhost:3000')
        .send({
          email: '<EMAIL>'
        });

      const finalLogCount = await query(
        'SELECT COUNT(*) FROM biometric_auth_logs WHERE user_id = $1',
        [testUser.id]
      );

      expect(parseInt(finalLogCount.rows[0].count)).toBeGreaterThan(parseInt(initialLogCount.rows[0].count));
    });

    test('should include security-relevant information in logs', async () => {
      await request(app)
        .post('/api/biometric/auth/challenge')
        .set('Origin', 'http://localhost:3000')
        .set('User-Agent', 'Test-Agent/1.0')
        .send({
          email: '<EMAIL>'
        });

      const logResult = await query(
        'SELECT * FROM biometric_auth_logs WHERE user_id = $1 ORDER BY timestamp DESC LIMIT 1',
        [testUser.id]
      );

      const log = logResult.rows[0];
      expect(log.event_type).toBeDefined();
      expect(log.event_status).toBeDefined();
      expect(log.ip_address).toBeDefined();
      expect(log.user_agent).toBe('Test-Agent/1.0');
      expect(log.timestamp).toBeDefined();
    });
  });
});

module.exports = {
  // Export test utilities for use in other test files
  createTestUser: async (email, password) => {
    const hashedPassword = await require('bcryptjs').hash(password, 12);
    const result = await query(
      'INSERT INTO users (email, password_hash, biometric_enabled) VALUES ($1, $2, $3) RETURNING *',
      [email, hashedPassword, true]
    );
    return result.rows[0];
  },
  
  cleanupTestUser: async (userId) => {
    await query('DELETE FROM biometric_auth_logs WHERE user_id = $1', [userId]);
    await query('DELETE FROM biometric_credentials WHERE user_id = $1', [userId]);
    await query('DELETE FROM registered_devices WHERE user_id = $1', [userId]);
    await query('DELETE FROM users WHERE id = $1', [userId]);
  }
};
