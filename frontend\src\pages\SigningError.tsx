import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface ErrorDetails {
  message: string;
  code?: string;
  timestamp?: string;
  retryable?: boolean;
  originalFileName?: string;
}

interface LocationState {
  error?: ErrorDetails;
  fromSigning?: boolean;
}

const SigningError: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const [errorDetails, setErrorDetails] = useState<ErrorDetails | null>(null);
  const [retryCountdown, setRetryCountdown] = useState(0);

  useEffect(() => {
    const state = location.state as LocationState;
    
    if (state?.error) {
      setErrorDetails(state.error);
    } else {
      // Default error if no specific error provided
      setErrorDetails({
        message: 'حدث خطأ غير متوقع أثناء توقيع المستند',
        code: 'UNKNOWN_ERROR',
        timestamp: new Date().toISOString(),
        retryable: true
      });
    }
  }, [location.state]);

  // Auto-retry countdown for retryable errors
  useEffect(() => {
    if (errorDetails?.retryable && retryCountdown === 0) {
      setRetryCountdown(30); // 30 seconds countdown for auto-retry
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [errorDetails]);

  useEffect(() => {
    if (retryCountdown > 0) {
      const timer = setTimeout(() => {
        setRetryCountdown(retryCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (retryCountdown === 0 && errorDetails?.retryable) {
      // Auto retry after countdown
      setTimeout(() => {
        handleRetry();
      }, 1000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [retryCountdown, errorDetails]);

  const getErrorIcon = (code?: string) => {
    switch (code) {
      case 'NETWORK_ERROR':
      case 'CONNECTION_LOST':
        return (
          <svg className="w-12 h-12 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
          </svg>
        );
      case 'FILE_TOO_LARGE':
      case 'INVALID_FILE':
        return (
          <svg className="w-12 h-12 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'UPLOAD_TIMEOUT':
        return (
          <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'SIGNATURE_MISSING':
        return (
          <svg className="w-12 h-12 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
          </svg>
        );
      default:
        return (
          <svg className="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const getErrorTitle = (code?: string) => {
    switch (code) {
      case 'NETWORK_ERROR':
      case 'CONNECTION_LOST':
        return 'مشكلة في الاتصال';
      case 'FILE_TOO_LARGE':
        return 'الملف كبير جداً';
      case 'INVALID_FILE':
        return 'ملف غير صالح';
      case 'UPLOAD_TIMEOUT':
        return 'انتهت مهلة الرفع';
      case 'SIGNATURE_MISSING':
        return 'التوقيع مفقود';
      case 'INSUFFICIENT_STORAGE':
        return 'مساحة التخزين ممتلئة';
      case 'PAYLOAD_TOO_LARGE':
        return 'حجم البيانات كبير جداً';
      default:
        return 'فشل في التوقيع';
    }
  };

  const getErrorSuggestions = (code?: string): string[] => {
    switch (code) {
      case 'NETWORK_ERROR':
      case 'CONNECTION_LOST':
        return [
          'تحقق من اتصال الإنترنت',
          'أعد المحاولة بعد قليل',
          'تأكد من استقرار الشبكة'
        ];
      case 'FILE_TOO_LARGE':
        return [
          'استخدم ملف أصغر حجماً',
          'ضغط ملف PDF قبل الرفع',
          'تقسيم المستند إلى أجزاء أصغر'
        ];
      case 'INVALID_FILE':
        return [
          'تأكد من أن الملف بصيغة PDF صالحة',
          'جرب ملف PDF آخر',
          'تحقق من عدم تلف الملف'
        ];
      case 'UPLOAD_TIMEOUT':
        return [
          'جرب رفع الملف مرة أخرى',
          'تحقق من سرعة الإنترنت',
          'استخدم ملف أصغر حجماً'
        ];
      case 'SIGNATURE_MISSING':
        return [
          'تأكد من رفع توقيع صالح',
          'أنشئ توقيع جديد',
          'تحقق من اختيار التوقيع'
        ];
      default:
        return [
          'أعد المحاولة بعد قليل',
          'تحقق من البيانات المدخلة',
          'اتصل بالدعم الفني إذا استمرت المشكلة'
        ];
    }
  };

  const handleRetry = () => {
    navigate('/sign', { 
      state: { 
        retryAfterError: true,
        originalFileName: errorDetails?.originalFileName 
      } 
    });
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const handleContactSupport = () => {
    // In a real app, this would open a support ticket or contact form
    const subject = encodeURIComponent(`خطأ في التوقيع الإلكتروني - ${errorDetails?.code || 'UNKNOWN'}`);
    const body = encodeURIComponent(`
تفاصيل الخطأ:
- الرسالة: ${errorDetails?.message}
- الكود: ${errorDetails?.code}
- الوقت: ${errorDetails?.timestamp}
- اسم الملف: ${errorDetails?.originalFileName || 'غير محدد'}

يرجى وصف المشكلة بالتفصيل...
    `);
    
    window.location.href = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
  };

  const stopAutoRetry = () => {
    setRetryCountdown(0);
  };

  if (!errorDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 font-['Almarai']">جاري تحميل تفاصيل الخطأ...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-4 sm:py-8" dir="rtl">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden" role="main" aria-labelledby="error-title">
          {/* Error Header */}
          <div className="bg-gradient-to-r from-red-500 to-red-600 px-4 sm:px-6 py-6 sm:py-8 text-center">
            <div
              className="w-16 h-16 sm:w-20 sm:h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4"
              role="img"
              aria-label="أيقونة الخطأ"
            >
              {getErrorIcon(errorDetails.code)}
            </div>
            <h1
              id="error-title"
              className="text-2xl sm:text-3xl font-extrabold text-white mb-2 font-['Almarai']"
            >
              {getErrorTitle(errorDetails.code)}
            </h1>
            <p className="text-red-100 text-base sm:text-lg font-['Almarai']">
              لم يتم توقيع المستند بسبب خطأ تقني
            </p>
          </div>

          {/* Error Details */}
          <div className="px-6 py-8">
            <div className="mb-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4 font-['Almarai']">تفاصيل الخطأ</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <p className="text-gray-800 font-['Almarai'] leading-relaxed">
                  {errorDetails.message}
                </p>
                
                {errorDetails.code && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <p className="text-sm text-gray-600 font-['Almarai']">
                      <span className="font-medium">كود الخطأ:</span> {errorDetails.code}
                    </p>
                  </div>
                )}
                
                {errorDetails.originalFileName && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600 font-['Almarai']">
                      <span className="font-medium">اسم الملف:</span> {errorDetails.originalFileName}
                    </p>
                  </div>
                )}
              </div>

              {/* Suggestions */}
              <div className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 mb-4 font-['Almarai']">اقتراحات للحل</h3>
                <ul className="space-y-2">
                  {getErrorSuggestions(errorDetails.code).map((suggestion, index) => (
                    <li key={index} className="flex items-start">
                      <svg className="w-5 h-5 text-blue-500 mt-0.5 ml-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-gray-700 font-['Almarai']">{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                {errorDetails.retryable && (
                  <button
                    onClick={handleRetry}
                    className="w-full bg-blue-500 text-white py-3 px-6 rounded-lg hover:bg-blue-600 transition-colors font-['Almarai'] font-medium"
                    aria-label="إعادة المحاولة"
                  >
                    إعادة المحاولة
                  </button>
                )}
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <button
                    onClick={handleGoHome}
                    className="bg-gray-500 text-white py-3 px-6 rounded-lg hover:bg-gray-600 transition-colors font-['Almarai'] font-medium"
                    aria-label="العودة إلى الصفحة الرئيسية"
                  >
                    الصفحة الرئيسية
                  </button>
                  
                  <button
                    onClick={handleContactSupport}
                    className="bg-green-500 text-white py-3 px-6 rounded-lg hover:bg-green-600 transition-colors font-['Almarai'] font-medium"
                    aria-label="الاتصال بالدعم الفني"
                  >
                    الدعم الفني
                  </button>
                </div>
              </div>

              {/* Auto-retry notification */}
              {errorDetails.retryable && retryCountdown > 0 && (
                <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                  <p className="text-blue-800 font-['Almarai']">
                    سيتم إعادة المحاولة تلقائياً خلال {retryCountdown} ثانية
                  </p>
                  <button
                    onClick={stopAutoRetry}
                    className="mt-2 text-blue-600 hover:text-blue-800 underline font-['Almarai']"
                  >
                    إلغاء إعادة المحاولة التلقائية
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SigningError;
