-- Migration: Add refresh token support
-- Description: Add refresh token columns to users table for enhanced authentication

-- Add refresh token columns to users table
ALTER TABLE users 
ADD COLUMN refresh_token TEXT,
ADD COLUMN refresh_token_expires TIMESTAMP;

-- Create index for refresh token lookups
CREATE INDEX IF NOT EXISTS idx_users_refresh_token ON users(refresh_token) WHERE refresh_token IS NOT NULL;

-- Add comment to document the refresh token functionality
COMMENT ON COLUMN users.refresh_token IS 'JWT refresh token for automatic token renewal';
COMMENT ON COLUMN users.refresh_token_expires IS 'Expiration timestamp for the refresh token';

-- Update existing users to have null refresh tokens
UPDATE users SET 
    refresh_token = NULL,
    refresh_token_expires = NULL;
