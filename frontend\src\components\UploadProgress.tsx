import React from 'react';

interface UploadProgressProps {
  progress: {
    loaded: number;
    total: number;
    percentage: number;
    speed: number;
    timeRemaining: number;
    isUploading: boolean;
    error: string | null;
  };
  fileName?: string;
  onCancel?: () => void;
}

const UploadProgress: React.FC<UploadProgressProps> = ({ 
  progress, 
  fileName, 
  onCancel 
}) => {
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 بايت';
    
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${Math.round(seconds)} ثانية`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.round(seconds % 60);
      return `${minutes} دقيقة ${remainingSeconds > 0 ? `و ${remainingSeconds} ثانية` : ''}`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours} ساعة ${minutes > 0 ? `و ${minutes} دقيقة` : ''}`;
    }
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return `${formatBytes(bytesPerSecond)}/ثانية`;
  };

  if (!progress.isUploading && !progress.error && progress.percentage === 0) {
    return null;
  }

  return (
    <div className="w-full max-w-md mx-auto bg-white rounded-lg shadow-lg p-4 sm:p-6 border font-['Almarai']" dir="rtl">
      <div className="flex items-center justify-between mb-3 sm:mb-4">
        <h3 className="text-base sm:text-lg font-semibold text-gray-900 truncate flex-1">
          {progress.isUploading ? 'جاري الرفع...' : progress.error ? 'فشل الرفع' : 'تم الرفع بنجاح'}
        </h3>
        {onCancel && progress.isUploading && (
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1 hover:bg-gray-100 rounded flex-shrink-0 mr-2"
            title="إلغاء الرفع"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {fileName && (
        <div className="mb-3 sm:mb-4">
          <p className="text-xs sm:text-sm text-gray-600 truncate" title={fileName}>
            <strong>الملف:</strong> {fileName}
          </p>
        </div>
      )}

      {progress.error ? (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-4 w-4 sm:h-5 sm:w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-2 sm:mr-3 min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-red-800 break-words">{progress.error}</p>
            </div>
          </div>
        </div>
      ) : (
        <>
          {/* Progress Bar */}
          <div className="mb-3 sm:mb-4">
            <div className="flex justify-between text-xs sm:text-sm text-gray-600 mb-1">
              <span className="font-medium">{progress.percentage}%</span>
              <span className="text-xs sm:text-sm">{formatBytes(progress.loaded)} / {formatBytes(progress.total)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  progress.isUploading
                    ? 'bg-blue-500'
                    : progress.percentage === 100
                      ? 'bg-green-500'
                      : 'bg-gray-400'
                }`}
                style={{ width: `${progress.percentage}%` }}
              />
            </div>
          </div>

          {/* Upload Stats */}
          {progress.isUploading && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-xs sm:text-sm text-gray-600">
              <div className="text-center sm:text-right">
                <span className="font-medium">السرعة:</span>
                <br />
                <span className="text-blue-600">{formatSpeed(progress.speed)}</span>
              </div>
              <div className="text-center sm:text-right">
                <span className="font-medium">الوقت المتبقي:</span>
                <br />
                <span className="text-blue-600">
                  {progress.timeRemaining > 0
                    ? formatTime(progress.timeRemaining)
                    : 'حساب...'
                  }
                </span>
              </div>
            </div>
          )}

          {/* Success Message */}
          {!progress.isUploading && progress.percentage === 100 && !progress.error && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3 mt-3 sm:mt-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-4 w-4 sm:h-5 sm:w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="mr-2 sm:mr-3 min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-green-800 font-medium">تم رفع الملف بنجاح!</p>
                  <p className="text-xs text-green-600 mt-1">سيتم توجيهك تلقائياً...</p>
                </div>
              </div>
            </div>
          )}

          {/* Loading Animation */}
          {progress.isUploading && (
            <div className="mt-4 flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <span className="mr-2 text-sm text-gray-600">معالجة الملف...</span>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default UploadProgress;
