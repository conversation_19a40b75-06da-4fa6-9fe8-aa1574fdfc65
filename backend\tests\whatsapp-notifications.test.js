const {
  validatePhoneNumber,
  formatWhatsAppNumber,
  WHATSAPP_CONFIG
} = require('../src/services/whatsappNotificationService');

const {
  generateDocumentSignedNotification,
  validateNotificationContent,
  formatFileSize,
  getUserDisplayName
} = require('../src/services/notificationContentService');

const {
  getNotificationStats,
  getNotificationHealth
} = require('../src/services/notificationMonitoringService');

describe('WhatsApp Notification System', () => {
  
  describe('Phone Number Validation', () => {
    test('should validate correct international phone numbers', () => {
      const validNumbers = [
        '+966501234567',
        '+14155238886',
        '+971501234567',
        '+201234567890'
      ];

      validNumbers.forEach(number => {
        expect(validatePhoneNumber(number)).toBe(true);
      });
    });

    test('should reject invalid phone numbers', () => {
      const invalidNumbers = [
        '966501234567',  // Missing +
        '+966',          // Too short
        '+96650123456789012345', // Too long
        'invalid',       // Not a number
        '',              // Empty
        null,            // Null
        undefined        // Undefined
      ];

      invalidNumbers.forEach(number => {
        expect(validatePhoneNumber(number)).toBe(false);
      });
    });

    test('should format WhatsApp numbers correctly', () => {
      expect(formatWhatsAppNumber('+966501234567')).toBe('whatsapp:+966501234567');
      expect(formatWhatsAppNumber('whatsapp:+966501234567')).toBe('whatsapp:+966501234567');
      
      expect(() => formatWhatsAppNumber('invalid')).toThrow();
    });
  });

  describe('Notification Content Generation', () => {
    const mockDocumentData = {
      id: 'doc-123',
      originalFilename: 'test-document.pdf',
      serialNumber: 'وثيقة-ABC123',
      signedAt: new Date('2024-01-15T10:30:00Z'),
      fileSize: 1024000
    };

    const mockUserData = {
      id: 'user-123',
      email: '<EMAIL>',
      full_name: 'أحمد محمد'
    };

    test('should generate Arabic document signed notification', () => {
      const notification = generateDocumentSignedNotification(
        mockDocumentData, 
        mockUserData, 
        'ar', 
        false
      );

      expect(notification).toHaveProperty('title');
      expect(notification).toHaveProperty('message');
      expect(notification).toHaveProperty('metadata');
      
      expect(notification.title).toContain('تم توقيع المستند بنجاح');
      expect(notification.message).toContain('test-document.pdf');
      expect(notification.message).toContain('وثيقة-ABC123');
      expect(notification.message).toContain('أحمد محمد');
      
      expect(notification.metadata.type).toBe('document_signed');
      expect(notification.metadata.language).toBe('ar');
      expect(notification.metadata.isAdmin).toBe(false);
    });

    test('should generate admin notification with additional details', () => {
      const notification = generateDocumentSignedNotification(
        mockDocumentData, 
        mockUserData, 
        'ar', 
        true
      );

      expect(notification.message).toContain('إشعار إداري');
      expect(notification.message).toContain('<EMAIL>');
      expect(notification.message).toContain('معرف المستند');
      expect(notification.metadata.isAdmin).toBe(true);
    });

    test('should validate notification content', () => {
      const validContent = {
        message: 'Test notification message',
        metadata: { type: 'test' }
      };

      const validation = validateNotificationContent(validContent);
      expect(validation.valid).toBe(true);
    });

    test('should reject invalid notification content', () => {
      const invalidContent = {
        message: '', // Empty message
        metadata: { type: 'test' }
      };

      const validation = validateNotificationContent(invalidContent);
      expect(validation.valid).toBe(false);
      expect(validation.error).toContain('required');
    });

    test('should reject messages that are too long', () => {
      const longContent = {
        message: 'x'.repeat(1700), // Exceeds WhatsApp limit
        metadata: { type: 'test' }
      };

      const validation = validateNotificationContent(longContent);
      expect(validation.valid).toBe(false);
      expect(validation.error).toContain('too long');
    });
  });

  describe('File Size Formatting', () => {
    test('should format file sizes correctly in Arabic', () => {
      expect(formatFileSize(1024, 'ar')).toContain('كيلوبايت');
      expect(formatFileSize(1024 * 1024, 'ar')).toContain('ميجابايت');
      expect(formatFileSize(1024 * 1024 * 1024, 'ar')).toContain('جيجابايت');
      expect(formatFileSize(0, 'ar')).toBe('غير محدد');
    });

    test('should format file sizes correctly in English', () => {
      expect(formatFileSize(1024, 'en')).toContain('KB');
      expect(formatFileSize(1024 * 1024, 'en')).toContain('MB');
      expect(formatFileSize(1024 * 1024 * 1024, 'en')).toContain('GB');
      expect(formatFileSize(0, 'en')).toBe('Unknown');
    });
  });

  describe('User Display Name', () => {
    test('should use full name when available', () => {
      const user = { full_name: 'أحمد محمد', email: '<EMAIL>' };
      expect(getUserDisplayName(user)).toBe('أحمد محمد');
    });

    test('should extract name from email when full name is not available', () => {
      const user = { email: '<EMAIL>' };
      expect(getUserDisplayName(user)).toBe('Ahmed');
    });

    test('should handle edge cases', () => {
      const userWithEmptyName = { full_name: '   ', email: '<EMAIL>' };
      expect(getUserDisplayName(userWithEmptyName)).toBe('Test');

      const userWithoutEmail = {};
      expect(getUserDisplayName(userWithoutEmail)).toBe('مستخدم غير معروف');
    });
  });

  describe('Configuration', () => {
    test('should have valid configuration structure', () => {
      expect(WHATSAPP_CONFIG).toHaveProperty('enabled');
      expect(WHATSAPP_CONFIG).toHaveProperty('fromNumber');
      expect(WHATSAPP_CONFIG).toHaveProperty('adminNumbers');
      expect(WHATSAPP_CONFIG).toHaveProperty('retryAttempts');
      expect(WHATSAPP_CONFIG).toHaveProperty('retryDelay');

      expect(Array.isArray(WHATSAPP_CONFIG.adminNumbers)).toBe(true);
      expect(typeof WHATSAPP_CONFIG.retryAttempts).toBe('number');
      expect(typeof WHATSAPP_CONFIG.retryDelay).toBe('number');
    });
  });

  describe('Monitoring Service', () => {
    test('should return valid stats structure', async () => {
      const stats = await getNotificationStats('24h');
      
      expect(stats).toHaveProperty('timeframe');
      expect(stats).toHaveProperty('stats');
      expect(stats).toHaveProperty('generatedAt');
      expect(stats.timeframe).toBe('24h');
    });

    test('should return valid health check structure', async () => {
      const health = await getNotificationHealth();
      
      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('isHealthy');
      expect(health).toHaveProperty('checkedAt');
      expect(['healthy', 'warning', 'critical', 'error']).toContain(health.status);
      expect(typeof health.isHealthy).toBe('boolean');
    });
  });
});

// Mock console methods to avoid noise in test output
beforeAll(() => {
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterAll(() => {
  jest.restoreAllMocks();
});
