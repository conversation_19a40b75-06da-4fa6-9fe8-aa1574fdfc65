import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { documentAPI } from '../services/api';

interface DocumentDetails {
  id: string;
  originalName: string;
  serialNumber: string;
  signedAt: string;
  fileSize: number;
  fileType: string;
  signedFilePath?: string;
}

interface LocationState {
  documentDetails?: DocumentDetails;
  successMessage?: string;
}

const SigningConfirmation: React.FC = () => {
  const { documentId } = useParams<{ documentId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [documentDetails, setDocumentDetails] = useState<DocumentDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [downloading, setDownloading] = useState(false);
  const [autoRedirectCountdown, setAutoRedirectCountdown] = useState(0);

  // Get document details from location state or fetch from API
  useEffect(() => {
    const state = location.state as LocationState;
    
    if (state?.documentDetails) {
      setDocumentDetails(state.documentDetails);
      setLoading(false);
    } else if (documentId) {
      fetchDocumentDetails();
    } else {
      setError('معرف المستند مفقود');
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [documentId, location.state]);

  // Auto-redirect countdown
  useEffect(() => {
    if (documentDetails && autoRedirectCountdown === 0) {
      setAutoRedirectCountdown(10); // 10 seconds countdown
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [documentDetails]);

  useEffect(() => {
    if (autoRedirectCountdown > 0) {
      const timer = setTimeout(() => {
        setAutoRedirectCountdown(autoRedirectCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (autoRedirectCountdown === 0 && documentDetails) {
      // Auto redirect after countdown
      setTimeout(() => {
        navigate('/documents');
      }, 1000);
    }
  }, [autoRedirectCountdown, documentDetails, navigate]);

  const fetchDocumentDetails = async () => {
    try {
      setLoading(true);
      const response = await documentAPI.getById(documentId!);
      setDocumentDetails(response.data);
    } catch (error: any) {
      console.error('Error fetching document details:', error);
      setError('فشل في تحميل تفاصيل المستند');
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDateTime = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const handleDownload = async () => {
    if (!documentDetails) return;

    try {
      setDownloading(true);
      const response = await documentAPI.download(documentDetails.id);

      // Create blob and download
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `signed_${documentDetails.originalName}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Download error:', error);
      setError('فشل في تحميل المستند');
    } finally {
      setDownloading(false);
    }
  };

  const handleViewDocument = () => {
    if (documentDetails) {
      // This will trigger audit logging on the backend via the GET endpoint
      const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
      window.open(`${API_BASE_URL}/documents/${documentDetails.id}`, '_blank');
    }
  };

  const handleSignAnother = () => {
    navigate('/sign');
  };

  const handleReturnToDashboard = () => {
    navigate('/documents');
  };

  const stopAutoRedirect = () => {
    setAutoRedirectCountdown(0);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 font-['Almarai']">جاري تحميل تفاصيل المستند...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-4 font-['Almarai']">حدث خطأ</h2>
          <p className="text-gray-600 mb-6 font-['Almarai']">{error}</p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors font-['Almarai']"
            >
              إعادة المحاولة
            </button>
            <button
              onClick={handleReturnToDashboard}
              className="w-full bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors font-['Almarai']"
            >
              العودة إلى لوحة التحكم
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!documentDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <p className="text-gray-600 font-['Almarai']">لم يتم العثور على تفاصيل المستند</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-4 sm:py-8" dir="rtl">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Header */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden" role="main" aria-labelledby="success-title">
          {/* Success Banner */}
          <div className="bg-gradient-to-r from-green-500 to-green-600 px-4 sm:px-6 py-6 sm:py-8 text-center">
            <div
              className="w-16 h-16 sm:w-20 sm:h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4"
              role="img"
              aria-label="أيقونة النجاح"
            >
              <svg
                className="w-10 h-10 sm:w-12 sm:h-12 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h1
              id="success-title"
              className="text-2xl sm:text-3xl font-extrabold text-white mb-2 font-['Almarai']"
            >
              تم توقيع المستند بنجاح!
            </h1>
            <p className="text-green-100 text-base sm:text-lg font-['Almarai']">
              تم حفظ المستند الموقع وهو جاهز للتحميل
            </p>
          </div>

          {/* Document Details */}
          <div className="px-4 sm:px-6 py-6 sm:py-8">
            <h2
              className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 font-['Almarai']"
              id="document-details"
            >
              تفاصيل المستند
            </h2>

            <div
              className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-6 sm:mb-8"
              role="region"
              aria-labelledby="document-details"
            >
              <div className="space-y-4">
                <div>
                  <label
                    className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']"
                    id="document-name-label"
                  >
                    اسم المستند
                  </label>
                  <p
                    className="text-base sm:text-lg text-gray-900 font-['Almarai'] break-words"
                    aria-labelledby="document-name-label"
                  >
                    {documentDetails.originalName}
                  </p>
                </div>

                <div>
                  <label
                    className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']"
                    id="serial-number-label"
                  >
                    الرقم التسلسلي
                  </label>
                  <p
                    className="text-sm sm:text-lg text-gray-900 font-mono bg-gray-100 px-3 py-2 rounded break-all"
                    aria-labelledby="serial-number-label"
                  >
                    {documentDetails.serialNumber}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label
                    className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']"
                    id="signing-time-label"
                  >
                    تاريخ ووقت التوقيع
                  </label>
                  <p
                    className="text-base sm:text-lg text-gray-900 font-['Almarai']"
                    aria-labelledby="signing-time-label"
                  >
                    {formatDateTime(documentDetails.signedAt)}
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label
                      className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']"
                      id="file-size-label"
                    >
                      حجم الملف
                    </label>
                    <p
                      className="text-base sm:text-lg text-gray-900 font-['Almarai']"
                      aria-labelledby="file-size-label"
                    >
                      {formatFileSize(documentDetails.fileSize)}
                    </p>
                  </div>
                  <div>
                    <label
                      className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']"
                      id="file-type-label"
                    >
                      نوع الملف
                    </label>
                    <p
                      className="text-base sm:text-lg text-gray-900 font-['Almarai']"
                      aria-labelledby="file-type-label"
                    >
                      PDF
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="border-t pt-6 sm:pt-8">
              <h3
                className="text-lg sm:text-xl font-bold text-gray-900 mb-4 sm:mb-6 font-['Almarai']"
                id="next-steps"
              >
                الخطوات التالية
              </h3>

              <div
                className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-3 sm:gap-4 mb-6 sm:mb-8"
                role="group"
                aria-labelledby="next-steps"
              >
                <button
                  onClick={handleDownload}
                  disabled={downloading}
                  className="flex flex-col items-center p-4 sm:p-6 bg-blue-50 hover:bg-blue-100 focus:bg-blue-100 rounded-lg transition-colors border-2 border-blue-200 hover:border-blue-300 focus:border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="تحميل المستند الموقع"
                  aria-describedby="download-description"
                >
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-500 rounded-full flex items-center justify-center mb-2 sm:mb-3">
                    {downloading ? (
                      <div
                        className="animate-spin rounded-full h-5 w-5 sm:h-6 sm:w-6 border-b-2 border-white"
                        aria-hidden="true"
                      ></div>
                    ) : (
                      <svg
                        className="w-5 h-5 sm:w-6 sm:h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    )}
                  </div>
                  <span
                    className="text-xs sm:text-sm font-medium text-gray-900 text-center font-['Almarai']"
                    id="download-description"
                  >
                    {downloading ? 'جاري التحميل...' : 'تحميل المستند'}
                  </span>
                </button>

                <button
                  onClick={handleViewDocument}
                  className="flex flex-col items-center p-4 sm:p-6 bg-green-50 hover:bg-green-100 focus:bg-green-100 rounded-lg transition-colors border-2 border-green-200 hover:border-green-300 focus:border-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                  aria-label="عرض المستند في المتصفح"
                  aria-describedby="view-description"
                >
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-500 rounded-full flex items-center justify-center mb-2 sm:mb-3">
                    <svg
                      className="w-5 h-5 sm:w-6 sm:h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                  <span
                    className="text-xs sm:text-sm font-medium text-gray-900 text-center font-['Almarai']"
                    id="view-description"
                  >
                    عرض المستند
                  </span>
                </button>

                <button
                  onClick={handleSignAnother}
                  className="flex flex-col items-center p-4 sm:p-6 bg-purple-50 hover:bg-purple-100 focus:bg-purple-100 rounded-lg transition-colors border-2 border-purple-200 hover:border-purple-300 focus:border-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                  aria-label="توقيع مستند آخر"
                  aria-describedby="sign-another-description"
                >
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-500 rounded-full flex items-center justify-center mb-2 sm:mb-3">
                    <svg
                      className="w-5 h-5 sm:w-6 sm:h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <span
                    className="text-xs sm:text-sm font-medium text-gray-900 text-center font-['Almarai']"
                    id="sign-another-description"
                  >
                    توقيع مستند آخر
                  </span>
                </button>

                <button
                  onClick={handleReturnToDashboard}
                  className="flex flex-col items-center p-4 sm:p-6 bg-gray-50 hover:bg-gray-100 focus:bg-gray-100 rounded-lg transition-colors border-2 border-gray-200 hover:border-gray-300 focus:border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  aria-label="العودة إلى لوحة التحكم"
                  aria-describedby="dashboard-description"
                >
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-500 rounded-full flex items-center justify-center mb-2 sm:mb-3">
                    <svg
                      className="w-5 h-5 sm:w-6 sm:h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                    </svg>
                  </div>
                  <span
                    className="text-xs sm:text-sm font-medium text-gray-900 text-center font-['Almarai']"
                    id="dashboard-description"
                  >
                    لوحة التحكم
                  </span>
                </button>
              </div>

              {/* Auto-redirect notification */}
              {autoRedirectCountdown > 0 && (
                <div
                  className="bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4 text-center"
                  role="status"
                  aria-live="polite"
                  aria-atomic="true"
                >
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-center gap-2 sm:gap-4">
                    <div className="flex items-center justify-center">
                      <div className="animate-pulse w-2 h-2 bg-blue-500 rounded-full ml-2"></div>
                      <p className="text-blue-800 font-['Almarai'] text-xs sm:text-sm">
                        سيتم توجيهك تلقائياً خلال {autoRedirectCountdown} ثانية
                      </p>
                    </div>
                    <button
                      onClick={stopAutoRedirect}
                      className="text-blue-600 hover:text-blue-800 focus:text-blue-800 underline font-['Almarai'] text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-2 py-1 transition-colors duration-200"
                      aria-label="إلغاء التوجيه التلقائي إلى لوحة التحكم"
                    >
                      إلغاء التوجيه
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SigningConfirmation;
