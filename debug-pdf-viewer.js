const axios = require('axios');
const fs = require('fs');

async function debugPDFViewer() {
  try {
    console.log('🔍 Debugging PDF Viewer Issue...\n');
    
    // Step 1: Login to get token
    console.log('1. Logging in...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    if (!loginResponse.data.success) {
      console.error('❌ Login failed:', loginResponse.data);
      return;
    }
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful');
    console.log(`   Token: ${token.substring(0, 20)}...`);
    
    // Step 2: Get documents list
    console.log('\n2. Fetching documents...');
    const documentsResponse = await axios.get('http://localhost:3001/api/documents?page=1&limit=10', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const documents = documentsResponse.data.documents;
    console.log(`✅ Found ${documents.length} documents`);
    
    if (documents.length === 0) {
      console.log('❌ No documents found. Please sign a document first.');
      return;
    }
    
    // Step 3: Test each document
    for (let i = 0; i < Math.min(documents.length, 3); i++) {
      const doc = documents[i];
      console.log(`\n3.${i+1} Testing document: ${doc.original_filename}`);
      console.log(`     Document ID: ${doc.id}`);
      console.log(`     Status: ${doc.status}`);
      console.log(`     File Size: ${doc.file_size} bytes`);
      
      // Test with Authorization header
      console.log(`     Testing with Authorization header...`);
      try {
        const viewResponse = await axios.get(`http://localhost:3001/api/documents/${doc.id}/view`, {
          headers: { 
            Authorization: `Bearer ${token}`,
            'Accept': 'application/pdf'
          },
          responseType: 'arraybuffer',
          timeout: 30000
        });
        
        console.log(`     ✅ Authorization header: Status ${viewResponse.status}`);
        console.log(`     ✅ Content-Type: ${viewResponse.headers['content-type']}`);
        console.log(`     ✅ Content-Length: ${viewResponse.headers['content-length']} bytes`);
        
        // Check PDF validity
        const pdfHeader = viewResponse.data.slice(0, 8).toString();
        console.log(`     ✅ PDF Header: "${pdfHeader}"`);
        console.log(`     ✅ Is valid PDF: ${pdfHeader.startsWith('%PDF')}`);
        
        // Save PDF for inspection
        const filename = `debug-pdf-${i+1}-${doc.id.substring(0, 8)}.pdf`;
        fs.writeFileSync(filename, viewResponse.data);
        console.log(`     ✅ Saved PDF as: ${filename}`);
        
      } catch (authError) {
        console.error(`     ❌ Authorization header failed:`, authError.message);
        if (authError.response) {
          console.error(`     ❌ Status: ${authError.response.status}`);
          console.error(`     ❌ Data: ${authError.response.data?.toString?.() || 'No data'}`);
        }
      }
      
      // Test with query parameter (like frontend does)
      console.log(`     Testing with query parameter...`);
      try {
        const queryUrl = `http://localhost:3001/api/documents/${doc.id}/view?token=${encodeURIComponent(token)}`;
        const queryResponse = await axios.get(queryUrl, {
          responseType: 'arraybuffer',
          timeout: 30000
        });
        
        console.log(`     ✅ Query parameter: Status ${queryResponse.status}`);
        console.log(`     ✅ Content-Type: ${queryResponse.headers['content-type']}`);
        console.log(`     ✅ Content-Length: ${queryResponse.headers['content-length']} bytes`);
        
        // Check if data matches
        const matches = Buffer.compare(viewResponse.data, queryResponse.data) === 0;
        console.log(`     ✅ Data matches auth header: ${matches}`);
        
      } catch (queryError) {
        console.error(`     ❌ Query parameter failed:`, queryError.message);
        if (queryError.response) {
          console.error(`     ❌ Status: ${queryError.response.status}`);
          console.error(`     ❌ Data: ${queryError.response.data?.toString?.() || 'No data'}`);
        }
      }
    }
    
    // Step 4: Test PDF.js worker and CORS
    console.log('\n4. Testing PDF.js worker and CORS...');
    
    // Test PDF.js worker URL
    try {
      const workerUrl = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
      const workerResponse = await axios.head(workerUrl, { timeout: 10000 });
      console.log(`✅ PDF.js worker accessible: Status ${workerResponse.status}`);
    } catch (workerError) {
      console.error('❌ PDF.js worker not accessible:', workerError.message);
    }
    
    // Test CORS headers
    try {
      const corsResponse = await axios.options(`http://localhost:3001/api/documents/${documents[0].id}/view`, {
        headers: { 
          'Origin': 'http://localhost:3000',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'authorization'
        }
      });
      console.log(`✅ CORS preflight: Status ${corsResponse.status}`);
      console.log(`✅ CORS headers:`, corsResponse.headers);
    } catch (corsError) {
      console.error('❌ CORS test failed:', corsError.message);
    }
    
    // Step 5: Test frontend URL construction
    console.log('\n5. Testing frontend URL construction...');
    const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
    const testDocId = documents[0].id;
    const frontendUrl = `${API_BASE_URL}/documents/${testDocId}/view?token=${encodeURIComponent(token)}`;
    
    console.log(`Frontend would construct URL: ${frontendUrl}`);
    
    try {
      const frontendResponse = await axios.get(frontendUrl, {
        responseType: 'arraybuffer',
        timeout: 30000
      });
      console.log(`✅ Frontend URL works: Status ${frontendResponse.status}`);
      console.log(`✅ Content-Type: ${frontendResponse.headers['content-type']}`);
    } catch (frontendError) {
      console.error('❌ Frontend URL failed:', frontendError.message);
    }
    
    console.log('\n📋 Debug Summary:');
    console.log('- Check the saved PDF files to verify they open correctly');
    console.log('- Compare network requests in browser dev tools with these results');
    console.log('- Look for JavaScript errors in browser console');
    console.log('- Verify react-pdf version compatibility');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the debug
debugPDFViewer().catch(console.error);
