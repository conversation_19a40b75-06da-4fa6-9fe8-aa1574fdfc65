import React, { useState, useEffect, useRef } from 'react';
import { useIntersectionObserver } from './LazyWrapper';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  placeholderColor?: string;
  loadingStrategy?: 'lazy' | 'eager' | 'auto';
  onLoad?: () => void;
  onError?: () => void;
  fallbackSrc?: string;
  blur?: boolean;
  quality?: 'low' | 'medium' | 'high';
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  placeholderColor = '#f3f4f6',
  loadingStrategy = 'lazy',
  onLoad,
  onError,
  fallbackSrc,
  blur = true,
  quality = 'medium'
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const isVisible = useIntersectionObserver(imgRef, { rootMargin: '200px' });
  const [currentSrc, setCurrentSrc] = useState<string | null>(null);

  // Quality settings
  const qualitySettings = {
    low: { quality: 60, format: 'webp' },
    medium: { quality: 75, format: 'webp' },
    high: { quality: 90, format: 'webp' }
  };

  // Generate optimized image URL
  const getOptimizedUrl = (url: string) => {
    // This would typically use an image optimization service
    // For now, we'll just return the original URL
    // In a real implementation, you might use something like:
    // return `https://your-image-optimizer.com/optimize?url=${encodeURIComponent(url)}&quality=${qualitySettings[quality].quality}&format=${qualitySettings[quality].format}`;
    return url;
  };

  useEffect(() => {
    if (isVisible || loadingStrategy === 'eager') {
      setCurrentSrc(getOptimizedUrl(src));
    }
  }, [isVisible, src, loadingStrategy]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    if (fallbackSrc && fallbackSrc !== src) {
      setCurrentSrc(fallbackSrc);
    } else {
      onError?.();
    }
  };

  // Calculate aspect ratio for placeholder
  const aspectRatio = width && height ? (height / width) * 100 : 0;

  return (
    <div
      className={`relative overflow-hidden ${className}`}
      style={{
        backgroundColor: placeholderColor,
        paddingBottom: aspectRatio ? `${aspectRatio}%` : undefined,
        width: width ? `${width}px` : '100%',
        height: height && !aspectRatio ? `${height}px` : undefined
      }}
    >
      {currentSrc && (
        <img
          ref={imgRef}
          src={currentSrc}
          alt={alt}
          width={width}
          height={height}
          loading={loadingStrategy === 'auto' ? undefined : loadingStrategy}
          onLoad={handleLoad}
          onError={handleError}
          className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          } ${blur && !isLoaded ? 'blur-sm' : ''}`}
        />
      )}

      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <svg
            className="w-8 h-8 text-gray-300 animate-pulse"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
      )}

      {hasError && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50">
          <div className="text-center">
            <svg
              className="w-8 h-8 text-red-400 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <p className="text-xs text-red-500 mt-2">فشل تحميل الصورة</p>
          </div>
        </div>
      )}
    </div>
  );
};

// Image gallery with optimized loading
interface OptimizedGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    width?: number;
    height?: number;
  }>;
  className?: string;
  itemClassName?: string;
}

export const OptimizedGallery: React.FC<OptimizedGalleryProps> = ({
  images,
  className = '',
  itemClassName = ''
}) => {
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 ${className}`}>
      {images.map((image, index) => (
        <div key={index} className={itemClassName}>
          <OptimizedImage
            src={image.src}
            alt={image.alt}
            width={image.width}
            height={image.height}
            loadingStrategy={index < 4 ? 'eager' : 'lazy'} // Load first 4 images eagerly
            quality={index < 4 ? 'high' : 'medium'} // Higher quality for first 4 images
          />
        </div>
      ))}
    </div>
  );
};

export default OptimizedImage;
