-- Migration: Add user tracking and audit logging
-- Description: Add user association to documents and create comprehensive audit logging system

-- First, add user tracking columns to documents table
ALTER TABLE documents 
ADD COLUMN user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
ADD COLUMN uploaded_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
ADD COLUMN signed_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Create indexes for efficient querying
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_uploaded_by ON documents(uploaded_by);
CREATE INDEX idx_documents_signed_by ON documents(signed_by);
CREATE INDEX idx_documents_created_at ON documents(created_at);

-- Create audit_logs table for comprehensive activity tracking
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    document_id INTEGER REFERENCES documents(id) ON DELETE CASCADE,
    signature_id INTEGER REFERENCES signatures(id) ON DELETE SET NULL,
    action VARCHAR(50) NOT NULL,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    metadata JSONB
);

-- Create indexes for audit_logs table
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_document_id ON audit_logs(document_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_success ON audit_logs(success);
CREATE INDEX idx_audit_logs_ip_address ON audit_logs(ip_address);

-- Create composite indexes for common queries
CREATE INDEX idx_audit_logs_user_action_timestamp ON audit_logs(user_id, action, timestamp);
CREATE INDEX idx_audit_logs_document_action_timestamp ON audit_logs(document_id, action, timestamp);

-- Create user_document_permissions table for future sharing functionality
CREATE TABLE user_document_permissions (
    id SERIAL PRIMARY KEY,
    document_id INTEGER REFERENCES documents(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    permission_type VARCHAR(20) NOT NULL CHECK (permission_type IN ('VIEW', 'DOWNLOAD', 'SIGN', 'ADMIN')),
    granted_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(document_id, user_id, permission_type)
);

-- Create indexes for permissions table
CREATE INDEX idx_user_document_permissions_document_id ON user_document_permissions(document_id);
CREATE INDEX idx_user_document_permissions_user_id ON user_document_permissions(user_id);
CREATE INDEX idx_user_document_permissions_type ON user_document_permissions(permission_type);
CREATE INDEX idx_user_document_permissions_active ON user_document_permissions(is_active);

-- Create document_statistics table for user statistics
CREATE TABLE document_statistics (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    total_uploaded INTEGER DEFAULT 0,
    total_signed INTEGER DEFAULT 0,
    total_downloaded INTEGER DEFAULT 0,
    total_viewed INTEGER DEFAULT 0,
    last_upload_at TIMESTAMP,
    last_sign_at TIMESTAMP,
    last_download_at TIMESTAMP,
    last_view_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for statistics table
CREATE INDEX idx_document_statistics_user_id ON document_statistics(user_id);

-- Create function to update document statistics
CREATE OR REPLACE FUNCTION update_document_statistics()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert or update statistics based on the action
    INSERT INTO document_statistics (user_id, 
        total_uploaded, total_signed, total_downloaded, total_viewed,
        last_upload_at, last_sign_at, last_download_at, last_view_at,
        updated_at)
    VALUES (
        NEW.user_id,
        CASE WHEN NEW.action = 'UPLOAD' THEN 1 ELSE 0 END,
        CASE WHEN NEW.action = 'SIGN' THEN 1 ELSE 0 END,
        CASE WHEN NEW.action = 'DOWNLOAD' THEN 1 ELSE 0 END,
        CASE WHEN NEW.action = 'VIEW' THEN 1 ELSE 0 END,
        CASE WHEN NEW.action = 'UPLOAD' THEN NEW.timestamp ELSE NULL END,
        CASE WHEN NEW.action = 'SIGN' THEN NEW.timestamp ELSE NULL END,
        CASE WHEN NEW.action = 'DOWNLOAD' THEN NEW.timestamp ELSE NULL END,
        CASE WHEN NEW.action = 'VIEW' THEN NEW.timestamp ELSE NULL END,
        CURRENT_TIMESTAMP
    )
    ON CONFLICT (user_id) DO UPDATE SET
        total_uploaded = document_statistics.total_uploaded + 
            CASE WHEN NEW.action = 'UPLOAD' THEN 1 ELSE 0 END,
        total_signed = document_statistics.total_signed + 
            CASE WHEN NEW.action = 'SIGN' THEN 1 ELSE 0 END,
        total_downloaded = document_statistics.total_downloaded + 
            CASE WHEN NEW.action = 'DOWNLOAD' THEN 1 ELSE 0 END,
        total_viewed = document_statistics.total_viewed + 
            CASE WHEN NEW.action = 'VIEW' THEN 1 ELSE 0 END,
        last_upload_at = CASE WHEN NEW.action = 'UPLOAD' THEN NEW.timestamp 
            ELSE document_statistics.last_upload_at END,
        last_sign_at = CASE WHEN NEW.action = 'SIGN' THEN NEW.timestamp 
            ELSE document_statistics.last_sign_at END,
        last_download_at = CASE WHEN NEW.action = 'DOWNLOAD' THEN NEW.timestamp 
            ELSE document_statistics.last_download_at END,
        last_view_at = CASE WHEN NEW.action = 'VIEW' THEN NEW.timestamp 
            ELSE document_statistics.last_view_at END,
        updated_at = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update statistics
CREATE TRIGGER trigger_update_document_statistics
    AFTER INSERT ON audit_logs
    FOR EACH ROW
    WHEN (NEW.success = true AND NEW.user_id IS NOT NULL AND 
          NEW.action IN ('UPLOAD', 'SIGN', 'DOWNLOAD', 'VIEW'))
    EXECUTE FUNCTION update_document_statistics();

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
CREATE TRIGGER trigger_documents_updated_at
    BEFORE UPDATE ON documents
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_document_statistics_updated_at
    BEFORE UPDATE ON document_statistics
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail for all document-related activities';
COMMENT ON COLUMN audit_logs.action IS 'Action performed: UPLOAD, SIGN, DOWNLOAD, VIEW, DELETE, SHARE, etc.';
COMMENT ON COLUMN audit_logs.details IS 'Additional action-specific details in JSON format';
COMMENT ON COLUMN audit_logs.metadata IS 'Additional metadata like file size, signature type, etc.';

COMMENT ON TABLE user_document_permissions IS 'Document sharing and permission management';
COMMENT ON TABLE document_statistics IS 'Aggregated statistics for user document activities';

-- Create view for user document summary
CREATE VIEW user_document_summary AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    ds.total_uploaded,
    ds.total_signed,
    ds.total_downloaded,
    ds.total_viewed,
    ds.last_upload_at,
    ds.last_sign_at,
    ds.last_download_at,
    ds.last_view_at,
    COUNT(d.id) as total_documents,
    COUNT(CASE WHEN d.signed_at IS NOT NULL THEN 1 END) as signed_documents,
    COUNT(CASE WHEN d.signed_at IS NULL THEN 1 END) as unsigned_documents
FROM users u
LEFT JOIN document_statistics ds ON u.id = ds.user_id
LEFT JOIN documents d ON u.id = d.user_id
GROUP BY u.id, u.username, u.email, ds.total_uploaded, ds.total_signed, 
         ds.total_downloaded, ds.total_viewed, ds.last_upload_at, 
         ds.last_sign_at, ds.last_download_at, ds.last_view_at;

-- Create view for recent document activities
CREATE VIEW recent_document_activities AS
SELECT 
    al.id,
    al.action,
    al.timestamp,
    al.success,
    u.username,
    u.email,
    d.original_name as document_name,
    d.serial_number,
    al.ip_address,
    al.details
FROM audit_logs al
LEFT JOIN users u ON al.user_id = u.id
LEFT JOIN documents d ON al.document_id = d.id
ORDER BY al.timestamp DESC;

-- Grant necessary permissions (adjust based on your user roles)
-- GRANT SELECT, INSERT ON audit_logs TO app_user;
-- GRANT SELECT, INSERT, UPDATE ON document_statistics TO app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON user_document_permissions TO app_user;
