{"name": "esign-backend", "version": "1.0.0", "description": "E-Signature System Backend", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "init-notifications": "node src/scripts/initializeNotifications.js", "check-setup": "node src/scripts/checkSetup.js"}, "keywords": ["esignature", "pdf", "authentication"], "author": "", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "bidi-js": "^1.0.3", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "fontkit": "^2.0.4", "helmet": "^6.0.1", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "multer": "^1.4.4", "pdf-lib": "^1.17.1", "pg": "^8.9.0", "react-scripts": "^5.0.1", "typescript": "^5.8.3", "uuid": "^9.0.0"}, "devDependencies": {"jest": "^29.4.0", "nodemon": "^2.0.20", "supertest": "^6.3.3"}}