const { PDFDocument, StandardFonts } = require('pdf-lib');

async function testFontLoading() {
  try {
    console.log('🧪 Testing Font Loading Logic...\n');

    // Create a test PDF document
    const pdfDoc = await PDFDocument.create();
    console.log('✅ PDF document created');

    // Test 1: Load Helvetica font
    console.log('\n1. Testing Helvetica font loading...');
    try {
      const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
      console.log('✅ Helvetica font loaded successfully');
      console.log('   Font type:', typeof helveticaFont);
      console.log('   Font constructor:', helveticaFont.constructor.name);
      console.log('   Font is valid:', helveticaFont !== null && helveticaFont !== undefined);
    } catch (error) {
      console.error('❌ Failed to load Helvetica font:', error.message);
      return { success: false, error: 'Helvetica font loading failed' };
    }

    // Test 2: Load Times Roman font
    console.log('\n2. Testing Times Roman font loading...');
    try {
      const timesFont = await pdfDoc.embedFont(StandardFonts.TimesRoman);
      console.log('✅ Times Roman font loaded successfully');
      console.log('   Font type:', typeof timesFont);
      console.log('   Font constructor:', timesFont.constructor.name);
      console.log('   Font is valid:', timesFont !== null && timesFont !== undefined);
    } catch (error) {
      console.error('❌ Failed to load Times Roman font:', error.message);
      return { success: false, error: 'Times Roman font loading failed' };
    }

    // Test 3: Load Courier font
    console.log('\n3. Testing Courier font loading...');
    try {
      const courierFont = await pdfDoc.embedFont(StandardFonts.Courier);
      console.log('✅ Courier font loaded successfully');
      console.log('   Font type:', typeof courierFont);
      console.log('   Font constructor:', courierFont.constructor.name);
      console.log('   Font is valid:', courierFont !== null && courierFont !== undefined);
    } catch (error) {
      console.error('❌ Failed to load Courier font:', error.message);
      return { success: false, error: 'Courier font loading failed' };
    }

    // Test 4: Simulate the exact logic from pdfService.js
    console.log('\n4. Testing the exact fallback logic...');
    let customFont = null;
    let useArabicText = false;

    try {
      // Simulate Arabic font loading failure
      throw new Error('No Arabic font path available');
    } catch (error) {
      console.log('🔄 Arabic font failed (simulated), starting fallback...');
      console.log('   Error:', error.message);
      
      useArabicText = false;
      
      try {
        console.log('🔄 Attempting to load Helvetica font...');
        customFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
        console.log('✅ Helvetica font loaded as fallback');
        console.log('   Font type:', typeof customFont);
        console.log('   Font is valid:', customFont !== null && customFont !== undefined);
      } catch (fontError) {
        console.error('❌ Failed to load Helvetica font:', fontError.message);
        try {
          console.log('🔄 Attempting to load Times Roman font...');
          customFont = await pdfDoc.embedFont(StandardFonts.TimesRoman);
          console.log('✅ Times Roman font loaded as last resort');
          console.log('   Font type:', typeof customFont);
          console.log('   Font is valid:', customFont !== null && customFont !== undefined);
        } catch (lastResortError) {
          console.error('❌ Failed to load any font:', lastResortError.message);
          customFont = null;
          console.error('💥 All font loading attempts failed!');
        }
      }
    }

    // Final check
    console.log('\n5. Final font validation...');
    console.log('🔍 Final font check - customFont:', customFont ? 'Available' : 'NULL');
    if (!customFont) {
      console.error('❌ No font could be loaded! This should not happen.');
      return { success: false, error: 'All font loading failed' };
    } else {
      console.log('✅ Font is available for text rendering');
      console.log('   Font type:', typeof customFont);
      console.log('   Font constructor:', customFont.constructor.name);
      console.log('   Font is valid:', customFont !== null && customFont !== undefined);
    }

    console.log('\n🎉 All font loading tests passed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Helvetica: Working');
    console.log('   ✅ Times Roman: Working');
    console.log('   ✅ Courier: Working');
    console.log('   ✅ Fallback logic: Working');
    console.log('   ✅ Final font validation: Working');

    return { 
      success: true, 
      font: customFont,
      fontType: typeof customFont,
      fontConstructor: customFont.constructor.name
    };

  } catch (error) {
    console.error('\n❌ Font loading test failed:', error.message);
    console.error('Stack:', error.stack);
    return { success: false, error: error.message };
  }
}

// Run the test
if (require.main === module) {
  testFontLoading()
    .then((result) => {
      if (result.success) {
        console.log('\n✅ Font loading system is working correctly!');
        console.log('🔧 The issue in pdfService.js must be elsewhere.');
        process.exit(0);
      } else {
        console.log('\n❌ Font loading system has issues:', result.error);
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Unexpected error:', error.message);
      process.exit(1);
    });
}

module.exports = { testFontLoading };
