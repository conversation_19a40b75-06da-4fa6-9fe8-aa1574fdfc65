const WebSocket = require('ws');
const { healthCheck, getPerformanceMetrics } = require('../models/database');
const { getResourceHealth } = require('../middleware/resourceManager');
const DatabaseOptimizationService = require('./databaseOptimization');

/**
 * Real-time monitoring service with WebSocket support
 */
class MonitoringService {
  constructor() {
    this.clients = new Set();
    this.metrics = {
      system: {},
      database: {},
      alerts: [],
      lastUpdate: null
    };
    this.alertThresholds = {
      memoryUsage: 80, // percentage
      responseTime: 5000, // milliseconds
      errorRate: 5, // percentage
      diskSpace: 90, // percentage
      cpuUsage: 85 // percentage
    };
    this.monitoringInterval = null;
    this.isMonitoring = false;
  }

  /**
   * Initialize WebSocket server
   */
  initializeWebSocket(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws/monitoring'
    });

    this.wss.on('connection', (ws, req) => {
      console.log('📊 Monitoring client connected');
      this.clients.add(ws);

      // Send current metrics immediately
      this.sendMetricsToClient(ws);

      // Handle client messages
      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message);
          this.handleClientMessage(ws, data);
        } catch (error) {
          console.error('Invalid WebSocket message:', error);
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        console.log('📊 Monitoring client disconnected');
        this.clients.delete(ws);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.clients.delete(ws);
      });
    });

    console.log('📊 Monitoring WebSocket server initialized');
  }

  /**
   * Handle client messages
   */
  handleClientMessage(ws, data) {
    switch (data.type) {
      case 'subscribe':
        // Client wants to subscribe to specific metrics
        ws.subscriptions = data.metrics || ['all'];
        break;
      
      case 'request_metrics':
        // Client requests immediate metrics update
        this.sendMetricsToClient(ws);
        break;
      
      case 'acknowledge_alert':
        // Client acknowledges an alert
        this.acknowledgeAlert(data.alertId);
        break;
      
      default:
        console.warn('Unknown WebSocket message type:', data.type);
    }
  }

  /**
   * Start monitoring
   */
  startMonitoring(interval = 10000) { // 10 seconds default
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    console.log('📊 Starting real-time monitoring...');

    this.monitoringInterval = setInterval(async () => {
      await this.collectMetrics();
      this.checkAlerts();
      this.broadcastMetrics();
    }, interval);

    // Initial metrics collection
    this.collectMetrics();
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    console.log('📊 Stopping real-time monitoring...');

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * Collect system and database metrics
   */
  async collectMetrics() {
    try {
      const timestamp = new Date().toISOString();

      // System metrics
      const systemHealth = getResourceHealth();
      
      // Database metrics
      const dbHealth = await healthCheck();
      const dbPerformance = getPerformanceMetrics();

      // Application metrics
      const appMetrics = this.getApplicationMetrics();

      this.metrics = {
        timestamp,
        system: {
          ...systemHealth,
          status: this.getSystemStatus(systemHealth)
        },
        database: {
          ...dbHealth,
          performance: dbPerformance,
          status: dbHealth.status
        },
        application: appMetrics,
        alerts: this.metrics.alerts,
        lastUpdate: timestamp
      };

    } catch (error) {
      console.error('Error collecting metrics:', error);
      this.metrics.system.status = 'error';
      this.metrics.database.status = 'error';
    }
  }

  /**
   * Get application-specific metrics
   */
  getApplicationMetrics() {
    return {
      activeConnections: this.clients.size,
      uptime: process.uptime(),
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development',
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    };
  }

  /**
   * Determine overall system status
   */
  getSystemStatus(systemHealth) {
    if (systemHealth.memory.usagePercent > 90) return 'critical';
    if (systemHealth.memory.usagePercent > 80) return 'warning';
    if (systemHealth.uptime < 300) return 'starting'; // Less than 5 minutes
    return 'healthy';
  }

  /**
   * Check for alert conditions
   */
  checkAlerts() {
    const newAlerts = [];

    // Memory usage alert
    if (this.metrics.system.memory?.usagePercent > this.alertThresholds.memoryUsage) {
      newAlerts.push({
        id: `memory_${Date.now()}`,
        type: 'memory',
        severity: this.metrics.system.memory.usagePercent > 95 ? 'critical' : 'warning',
        title: 'استخدام ذاكرة مرتفع',
        message: `استخدام الذاكرة وصل إلى ${this.metrics.system.memory.usagePercent}%`,
        timestamp: new Date().toISOString(),
        acknowledged: false
      });
    }

    // Database response time alert
    if (this.metrics.database.responseTime > this.alertThresholds.responseTime) {
      newAlerts.push({
        id: `db_response_${Date.now()}`,
        type: 'database',
        severity: 'warning',
        title: 'بطء في استجابة قاعدة البيانات',
        message: `زمن الاستجابة: ${this.metrics.database.responseTime}ms`,
        timestamp: new Date().toISOString(),
        acknowledged: false
      });
    }

    // Database error rate alert
    const errorRate = parseFloat(this.metrics.database.performance?.failureRate || '0');
    if (errorRate > this.alertThresholds.errorRate) {
      newAlerts.push({
        id: `db_errors_${Date.now()}`,
        type: 'database',
        severity: 'critical',
        title: 'معدل أخطاء مرتفع في قاعدة البيانات',
        message: `معدل الأخطاء: ${errorRate}%`,
        timestamp: new Date().toISOString(),
        acknowledged: false
      });
    }

    // Add new alerts to the list
    this.metrics.alerts = [...this.metrics.alerts, ...newAlerts];

    // Keep only recent alerts (last 24 hours)
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
    this.metrics.alerts = this.metrics.alerts.filter(alert => 
      new Date(alert.timestamp).getTime() > oneDayAgo
    );

    // Log new alerts
    newAlerts.forEach(alert => {
      console.warn(`🚨 Alert: ${alert.title} - ${alert.message}`);
    });
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId) {
    const alert = this.metrics.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedAt = new Date().toISOString();
      console.log(`✅ Alert acknowledged: ${alert.title}`);
    }
  }

  /**
   * Broadcast metrics to all connected clients
   */
  broadcastMetrics() {
    if (this.clients.size === 0) return;

    const message = JSON.stringify({
      type: 'metrics_update',
      data: this.metrics
    });

    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        try {
          client.send(message);
        } catch (error) {
          console.error('Error sending metrics to client:', error);
          this.clients.delete(client);
        }
      } else {
        this.clients.delete(client);
      }
    });
  }

  /**
   * Send metrics to a specific client
   */
  sendMetricsToClient(client) {
    if (client.readyState === WebSocket.OPEN) {
      try {
        const message = JSON.stringify({
          type: 'metrics_update',
          data: this.metrics
        });
        client.send(message);
      } catch (error) {
        console.error('Error sending metrics to client:', error);
        this.clients.delete(client);
      }
    }
  }

  /**
   * Get current metrics
   */
  getCurrentMetrics() {
    return this.metrics;
  }

  /**
   * Get alert summary
   */
  getAlertSummary() {
    const alerts = this.metrics.alerts;
    return {
      total: alerts.length,
      critical: alerts.filter(a => a.severity === 'critical' && !a.acknowledged).length,
      warning: alerts.filter(a => a.severity === 'warning' && !a.acknowledged).length,
      acknowledged: alerts.filter(a => a.acknowledged).length,
      recent: alerts.filter(a => {
        const alertTime = new Date(a.timestamp).getTime();
        const oneHourAgo = Date.now() - 60 * 60 * 1000;
        return alertTime > oneHourAgo;
      }).length
    };
  }

  /**
   * Update alert thresholds
   */
  updateAlertThresholds(newThresholds) {
    this.alertThresholds = { ...this.alertThresholds, ...newThresholds };
    console.log('📊 Alert thresholds updated:', this.alertThresholds);
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopMonitoring();
    
    if (this.wss) {
      this.wss.close();
    }

    this.clients.clear();
    console.log('📊 Monitoring service cleaned up');
  }
}

// Singleton instance
const monitoringService = new MonitoringService();

module.exports = monitoringService;
