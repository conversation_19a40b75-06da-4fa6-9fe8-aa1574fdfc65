const multer = require('multer');
const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const crypto = require('crypto');

// Chunked upload storage for very large files
class ChunkedUploadStorage {
  constructor(options = {}) {
    this.destination = options.destination || './uploads/chunks';
    this.maxChunkSize = options.maxChunkSize || 10 * 1024 * 1024; // 10MB chunks
    this.maxFileSize = options.maxFileSize || Infinity;
    this.allowedMimeTypes = options.allowedMimeTypes || [];
  }

  async _handleFile(req, file, cb) {
    try {
      const { chunkIndex, totalChunks, fileId, originalName } = req.body;
      
      if (!fileId || !chunkIndex || !totalChunks) {
        return cb(new Error('Missing chunk upload parameters'));
      }

      // Ensure chunks directory exists
      await fs.mkdir(this.destination, { recursive: true });
      
      // Create file-specific directory
      const fileDir = path.join(this.destination, fileId);
      await fs.mkdir(fileDir, { recursive: true });

      // Generate chunk filename
      const chunkFilename = `chunk_${chunkIndex.toString().padStart(4, '0')}`;
      const chunkPath = path.join(fileDir, chunkFilename);

      // Create write stream for chunk
      const writeStream = fsSync.createWriteStream(chunkPath);
      let chunkSize = 0;

      writeStream.on('error', (error) => {
        console.error('Chunk write error:', error);
        cb(error);
      });

      file.stream.on('data', (chunk) => {
        chunkSize += chunk.length;
        
        // Check chunk size limit
        if (chunkSize > this.maxChunkSize) {
          writeStream.destroy();
          fs.unlink(chunkPath).catch(() => {});
          return cb(new Error('Chunk too large'));
        }
      });

      file.stream.on('end', () => {
        writeStream.end();
      });

      writeStream.on('finish', async () => {
        try {
          // Check if all chunks are uploaded
          const chunks = await fs.readdir(fileDir);
          const uploadedChunks = chunks.filter(f => f.startsWith('chunk_')).length;

          if (uploadedChunks === parseInt(totalChunks)) {
            // All chunks uploaded, assemble the file
            const assembledPath = await this.assembleChunks(fileDir, originalName, totalChunks);
            
            // Clean up chunks
            await this.cleanupChunks(fileDir);

            cb(null, {
              destination: path.dirname(assembledPath),
              filename: path.basename(assembledPath),
              path: assembledPath,
              size: (await fs.stat(assembledPath)).size,
              isComplete: true
            });
          } else {
            // Partial upload
            cb(null, {
              destination: fileDir,
              filename: chunkFilename,
              path: chunkPath,
              size: chunkSize,
              isComplete: false,
              uploadedChunks,
              totalChunks: parseInt(totalChunks)
            });
          }
        } catch (error) {
          console.error('Chunk assembly error:', error);
          cb(error);
        }
      });

      // Pipe the file stream to write stream
      file.stream.pipe(writeStream);

    } catch (error) {
      console.error('Chunked upload error:', error);
      cb(error);
    }
  }

  async assembleChunks(fileDir, originalName, totalChunks) {
    const assembledPath = path.join(path.dirname(fileDir), `assembled_${originalName}`);
    const writeStream = fsSync.createWriteStream(assembledPath);

    try {
      for (let i = 0; i < totalChunks; i++) {
        const chunkPath = path.join(fileDir, `chunk_${i.toString().padStart(4, '0')}`);
        const chunkData = await fs.readFile(chunkPath);
        
        await new Promise((resolve, reject) => {
          writeStream.write(chunkData, (error) => {
            if (error) reject(error);
            else resolve();
          });
        });
      }

      await new Promise((resolve, reject) => {
        writeStream.end((error) => {
          if (error) reject(error);
          else resolve();
        });
      });

      return assembledPath;
    } catch (error) {
      // Clean up partial assembled file
      try {
        await fs.unlink(assembledPath);
      } catch (cleanupError) {
        console.error('Failed to cleanup partial assembled file:', cleanupError);
      }
      throw error;
    }
  }

  async cleanupChunks(fileDir) {
    try {
      const chunks = await fs.readdir(fileDir);
      await Promise.all(
        chunks.map(chunk => fs.unlink(path.join(fileDir, chunk)))
      );
      await fs.rmdir(fileDir);
    } catch (error) {
      console.error('Chunk cleanup error:', error);
    }
  }

  _removeFile(req, file, cb) {
    fs.unlink(file.path)
      .then(() => cb())
      .catch(cb);
  }
}

// Create chunked upload multer configuration
const createChunkedUpload = (options = {}) => {
  const storage = new ChunkedUploadStorage(options);
  
  return multer({
    storage: storage,
    limits: {
      files: 1,
      fieldSize: options.fieldSize || 1024, // 1KB for metadata fields
    },
    fileFilter: (req, file, cb) => {
      // Validate file type if specified
      if (options.allowedMimeTypes && options.allowedMimeTypes.length > 0) {
        if (!options.allowedMimeTypes.includes(file.mimetype)) {
          const allowedExtensions = options.allowedExtensions || [];
          const fileExtension = path.extname(file.originalname).toLowerCase();
          
          if (!allowedExtensions.includes(fileExtension)) {
            return cb(new Error(`File type not allowed: ${file.mimetype}`), false);
          }
        }
      }
      
      cb(null, true);
    }
  });
};

// Middleware to handle chunk upload status
const handleChunkUpload = (req, res, next) => {
  const originalSend = res.send;
  const originalJson = res.json;

  res.send = function(data) {
    if (req.file && !req.file.isComplete) {
      // Partial upload response
      return originalJson.call(this, {
        success: true,
        isComplete: false,
        uploadedChunks: req.file.uploadedChunks,
        totalChunks: req.file.totalChunks,
        message: `Chunk ${req.file.uploadedChunks}/${req.file.totalChunks} uploaded successfully`
      });
    }
    originalSend.call(this, data);
  };

  res.json = function(data) {
    if (req.file && !req.file.isComplete) {
      // Partial upload response
      return originalJson.call(this, {
        success: true,
        isComplete: false,
        uploadedChunks: req.file.uploadedChunks,
        totalChunks: req.file.totalChunks,
        message: `Chunk ${req.file.uploadedChunks}/${req.file.totalChunks} uploaded successfully`
      });
    }
    originalJson.call(this, data);
  };

  next();
};

// Generate unique file ID for chunked uploads
const generateFileId = () => {
  return crypto.randomBytes(16).toString('hex');
};

// Check upload status endpoint
const checkUploadStatus = async (req, res) => {
  try {
    const { fileId } = req.params;
    const chunksDir = path.join('./uploads/chunks', fileId);
    
    try {
      const chunks = await fs.readdir(chunksDir);
      const uploadedChunks = chunks.filter(f => f.startsWith('chunk_')).length;
      
      res.json({
        success: true,
        uploadedChunks,
        exists: true
      });
    } catch (error) {
      res.json({
        success: true,
        uploadedChunks: 0,
        exists: false
      });
    }
  } catch (error) {
    console.error('Check upload status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check upload status'
    });
  }
};

module.exports = {
  createChunkedUpload,
  handleChunkUpload,
  generateFileId,
  checkUploadStatus,
  ChunkedUploadStorage
};
