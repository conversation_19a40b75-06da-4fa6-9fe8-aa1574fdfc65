const axios = require('axios');

async function testFrontendAuth() {
  try {
    console.log('🔍 Testing Frontend Authentication and Authorization...\n');

    // 1. Test regular user login and get user data
    console.log('1. Testing regular user authentication...');
    const userLoginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    console.log('✅ Regular user login successful');
    console.log('👤 User data received:');
    console.log('   - Email:', userLoginResponse.data.user.email);
    console.log('   - Role:', userLoginResponse.data.user.role);
    console.log('   - ID:', userLoginResponse.data.user.id);

    // 2. Test admin user login and get user data
    console.log('\n2. Testing admin user authentication...');
    const adminLoginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });

    console.log('✅ Admin user login successful');
    console.log('👑 Admin data received:');
    console.log('   - Email:', adminLoginResponse.data.user.email);
    console.log('   - Role:', adminLoginResponse.data.user.role);
    console.log('   - ID:', adminLoginResponse.data.user.id);

    // 3. Test profile endpoint to verify user data consistency
    console.log('\n3. Testing profile endpoint consistency...');
    
    const userToken = userLoginResponse.data.token;
    const adminToken = adminLoginResponse.data.token;

    try {
      const userProfileResponse = await axios.get(
        'http://localhost:3001/api/auth/profile',
        {
          headers: {
            'Authorization': `Bearer ${userToken}`
          }
        }
      );
      console.log('👤 Regular user profile from /profile endpoint:');
      console.log('   - Email:', userProfileResponse.data.user.email);
      console.log('   - Role:', userProfileResponse.data.user.role);
      console.log('   - ID:', userProfileResponse.data.user.id);
    } catch (error) {
      console.log('❌ Error getting user profile:', error.response?.data);
    }

    try {
      const adminProfileResponse = await axios.get(
        'http://localhost:3001/api/auth/profile',
        {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        }
      );
      console.log('👑 Admin user profile from /profile endpoint:');
      console.log('   - Email:', adminProfileResponse.data.user.email);
      console.log('   - Role:', adminProfileResponse.data.user.role);
      console.log('   - ID:', adminProfileResponse.data.user.id);
    } catch (error) {
      console.log('❌ Error getting admin profile:', error.response?.data);
    }

    // 4. Test permission-based endpoints
    console.log('\n4. Testing permission-based endpoints...');

    // Test regular user access to admin endpoints
    console.log('\n   Testing regular user access to admin endpoints:');
    
    try {
      const pendingDocsResponse = await axios.get(
        'http://localhost:3001/api/documents/pending',
        {
          headers: {
            'Authorization': `Bearer ${userToken}`
          }
        }
      );
      console.log('❌ SECURITY ISSUE: Regular user can access pending documents!');
      console.log('   Response:', pendingDocsResponse.data);
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Regular user correctly blocked from pending documents');
        console.log('   Error:', error.response.data.message);
      } else {
        console.log('⚠️ Unexpected error:', error.response?.status, error.response?.data);
      }
    }

    // Test admin user access to admin endpoints
    console.log('\n   Testing admin user access to admin endpoints:');
    
    try {
      const adminPendingDocsResponse = await axios.get(
        'http://localhost:3001/api/documents/pending',
        {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        }
      );
      console.log('✅ Admin user can access pending documents');
      console.log('   Number of pending documents:', adminPendingDocsResponse.data.documents?.length || 0);
    } catch (error) {
      console.log('❌ Admin user blocked from pending documents (unexpected)');
      console.log('   Error:', error.response?.status, error.response?.data);
    }

    // 5. Test JWT token validation
    console.log('\n5. Testing JWT token validation...');
    
    // Test with invalid token
    try {
      const invalidTokenResponse = await axios.get(
        'http://localhost:3001/api/auth/profile',
        {
          headers: {
            'Authorization': 'Bearer invalid-token-here'
          }
        }
      );
      console.log('❌ SECURITY ISSUE: Invalid token accepted!');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Invalid token correctly rejected');
      } else {
        console.log('⚠️ Unexpected error for invalid token:', error.response?.status);
      }
    }

    // Test with no token
    try {
      const noTokenResponse = await axios.get('http://localhost:3001/api/auth/profile');
      console.log('❌ SECURITY ISSUE: No token required!');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Missing token correctly rejected');
      } else {
        console.log('⚠️ Unexpected error for missing token:', error.response?.status);
      }
    }

    console.log('\n🎉 Frontend Authentication Test Completed!');
    console.log('\n📋 Summary:');
    console.log('✅ User authentication working');
    console.log('✅ Role data being returned correctly');
    console.log('✅ JWT token validation working');
    console.log('✅ Permission-based endpoint protection working');

  } catch (error) {
    console.error('\n❌ Frontend auth test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testFrontendAuth();
