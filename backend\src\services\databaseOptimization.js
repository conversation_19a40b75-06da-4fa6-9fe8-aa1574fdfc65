const { query } = require('../models/database');

/**
 * Database optimization service for performance tuning
 */
class DatabaseOptimizationService {
  
  /**
   * Analyze query performance and suggest optimizations
   */
  static async analyzeQueryPerformance() {
    try {
      // Get slow queries from PostgreSQL stats
      const slowQueries = await query(`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows,
          100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
        FROM pg_stat_statements 
        WHERE mean_time > 100
        ORDER BY mean_time DESC 
        LIMIT 10
      `);

      return {
        slowQueries: slowQueries.rows,
        recommendations: this.generateOptimizationRecommendations(slowQueries.rows)
      };
    } catch (error) {
      console.error('Query performance analysis failed:', error);
      return { slowQueries: [], recommendations: [] };
    }
  }

  /**
   * Check and create missing indexes
   */
  static async optimizeIndexes() {
    const indexRecommendations = [
      {
        table: 'users',
        columns: ['email'],
        type: 'UNIQUE',
        reason: 'Login queries'
      },
      {
        table: 'users',
        columns: ['refresh_token'],
        type: 'INDEX',
        reason: 'Token refresh queries',
        condition: 'WHERE refresh_token IS NOT NULL'
      },
      {
        table: 'documents',
        columns: ['user_id', 'signed_date'],
        type: 'INDEX',
        reason: 'User document history queries'
      },
      {
        table: 'documents',
        columns: ['serial_number'],
        type: 'UNIQUE',
        reason: 'Document verification queries'
      },
      {
        table: 'documents',
        columns: ['status', 'signed_date'],
        type: 'INDEX',
        reason: 'Status-based document filtering'
      },
      {
        table: 'signatures',
        columns: ['user_id', 'upload_date'],
        type: 'INDEX',
        reason: 'User signature history queries'
      },
      {
        table: 'logs',
        columns: ['user_id', 'timestamp'],
        type: 'INDEX',
        reason: 'User activity logging queries'
      },
      {
        table: 'logs',
        columns: ['action', 'timestamp'],
        type: 'INDEX',
        reason: 'Action-based log filtering'
      }
    ];

    const results = [];
    
    for (const recommendation of indexRecommendations) {
      try {
        const indexName = `idx_${recommendation.table}_${recommendation.columns.join('_')}`;
        
        // Check if index already exists
        const existingIndex = await query(`
          SELECT indexname 
          FROM pg_indexes 
          WHERE tablename = $1 AND indexname = $2
        `, [recommendation.table, indexName]);

        if (existingIndex.rows.length === 0) {
          // Create the index
          const createIndexSQL = recommendation.type === 'UNIQUE'
            ? `CREATE UNIQUE INDEX IF NOT EXISTS ${indexName} ON ${recommendation.table} (${recommendation.columns.join(', ')})`
            : `CREATE INDEX IF NOT EXISTS ${indexName} ON ${recommendation.table} (${recommendation.columns.join(', ')}) ${recommendation.condition || ''}`;

          await query(createIndexSQL);
          
          results.push({
            action: 'created',
            indexName,
            table: recommendation.table,
            columns: recommendation.columns,
            reason: recommendation.reason
          });
        } else {
          results.push({
            action: 'exists',
            indexName,
            table: recommendation.table,
            columns: recommendation.columns
          });
        }
      } catch (error) {
        results.push({
          action: 'failed',
          table: recommendation.table,
          columns: recommendation.columns,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Analyze table statistics and suggest maintenance
   */
  static async analyzeTableStatistics() {
    try {
      const tableStats = await query(`
        SELECT 
          schemaname,
          tablename,
          n_tup_ins as inserts,
          n_tup_upd as updates,
          n_tup_del as deletes,
          n_live_tup as live_tuples,
          n_dead_tup as dead_tuples,
          last_vacuum,
          last_autovacuum,
          last_analyze,
          last_autoanalyze
        FROM pg_stat_user_tables
        ORDER BY n_dead_tup DESC
      `);

      const recommendations = [];
      
      tableStats.rows.forEach(table => {
        const deadTupleRatio = table.live_tuples > 0 
          ? (table.dead_tuples / table.live_tuples) * 100 
          : 0;

        if (deadTupleRatio > 20) {
          recommendations.push({
            table: table.tablename,
            issue: 'High dead tuple ratio',
            ratio: `${deadTupleRatio.toFixed(2)}%`,
            recommendation: 'Consider running VACUUM ANALYZE',
            priority: deadTupleRatio > 50 ? 'high' : 'medium'
          });
        }

        const lastAnalyze = table.last_analyze || table.last_autoanalyze;
        if (!lastAnalyze || (Date.now() - new Date(lastAnalyze).getTime()) > 7 * 24 * 60 * 60 * 1000) {
          recommendations.push({
            table: table.tablename,
            issue: 'Outdated statistics',
            lastAnalyze: lastAnalyze || 'Never',
            recommendation: 'Run ANALYZE to update table statistics',
            priority: 'medium'
          });
        }
      });

      return {
        tableStats: tableStats.rows,
        recommendations
      };
    } catch (error) {
      console.error('Table statistics analysis failed:', error);
      return { tableStats: [], recommendations: [] };
    }
  }

  /**
   * Generate optimization recommendations based on query analysis
   */
  static generateOptimizationRecommendations(slowQueries) {
    const recommendations = [];

    slowQueries.forEach(query => {
      if (query.hit_percent < 95) {
        recommendations.push({
          type: 'cache_optimization',
          query: query.query.substring(0, 100) + '...',
          issue: `Low cache hit ratio: ${query.hit_percent}%`,
          recommendation: 'Consider increasing shared_buffers or optimizing query'
        });
      }

      if (query.mean_time > 1000) {
        recommendations.push({
          type: 'query_optimization',
          query: query.query.substring(0, 100) + '...',
          issue: `High average execution time: ${query.mean_time}ms`,
          recommendation: 'Review query plan and consider adding indexes'
        });
      }

      if (query.calls > 1000 && query.mean_time > 100) {
        recommendations.push({
          type: 'frequent_slow_query',
          query: query.query.substring(0, 100) + '...',
          issue: `Frequently executed slow query: ${query.calls} calls, ${query.mean_time}ms avg`,
          recommendation: 'High priority for optimization - consider caching or query rewrite'
        });
      }
    });

    return recommendations;
  }

  /**
   * Run database maintenance tasks
   */
  static async runMaintenance() {
    const results = [];

    try {
      // Update table statistics
      await query('ANALYZE');
      results.push({ task: 'analyze', status: 'completed', message: 'Table statistics updated' });

      // Get table sizes to identify large tables for maintenance
      const tableSizes = await query(`
        SELECT 
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
      `);

      results.push({ 
        task: 'table_sizes', 
        status: 'completed', 
        data: tableSizes.rows 
      });

      // Check for bloated tables (this is a simplified check)
      const bloatedTables = tableSizes.rows.filter(table => table.size_bytes > 100 * 1024 * 1024); // > 100MB
      
      if (bloatedTables.length > 0) {
        results.push({
          task: 'bloat_check',
          status: 'warning',
          message: `Found ${bloatedTables.length} large tables that may benefit from maintenance`,
          data: bloatedTables
        });
      }

    } catch (error) {
      results.push({ 
        task: 'maintenance', 
        status: 'failed', 
        error: error.message 
      });
    }

    return results;
  }

  /**
   * Get comprehensive database performance report
   */
  static async getPerformanceReport() {
    try {
      const [queryAnalysis, indexOptimization, tableAnalysis, maintenance] = await Promise.all([
        this.analyzeQueryPerformance(),
        this.optimizeIndexes(),
        this.analyzeTableStatistics(),
        this.runMaintenance()
      ]);

      return {
        timestamp: new Date().toISOString(),
        queryAnalysis,
        indexOptimization,
        tableAnalysis,
        maintenance,
        summary: {
          slowQueriesFound: queryAnalysis.slowQueries.length,
          indexesOptimized: indexOptimization.filter(r => r.action === 'created').length,
          maintenanceRecommendations: tableAnalysis.recommendations.length,
          overallHealth: this.calculateOverallHealth(queryAnalysis, tableAnalysis)
        }
      };
    } catch (error) {
      console.error('Performance report generation failed:', error);
      return {
        timestamp: new Date().toISOString(),
        error: error.message,
        summary: { overallHealth: 'unknown' }
      };
    }
  }

  /**
   * Calculate overall database health score
   */
  static calculateOverallHealth(queryAnalysis, tableAnalysis) {
    let score = 100;
    
    // Deduct points for slow queries
    score -= Math.min(queryAnalysis.slowQueries.length * 10, 30);
    
    // Deduct points for high priority maintenance issues
    const highPriorityIssues = tableAnalysis.recommendations.filter(r => r.priority === 'high').length;
    score -= highPriorityIssues * 15;
    
    // Deduct points for medium priority issues
    const mediumPriorityIssues = tableAnalysis.recommendations.filter(r => r.priority === 'medium').length;
    score -= mediumPriorityIssues * 5;

    if (score >= 90) return 'excellent';
    if (score >= 75) return 'good';
    if (score >= 60) return 'fair';
    if (score >= 40) return 'poor';
    return 'critical';
  }
}

module.exports = DatabaseOptimizationService;
