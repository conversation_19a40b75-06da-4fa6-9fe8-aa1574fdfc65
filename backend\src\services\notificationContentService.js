const { convertToArabicNumerals, formatEnglishDate, formatEnglishTime } = require('./multilingualTextService');

/**
 * Arabic notification templates
 */
const ARABIC_TEMPLATES = {
  DOCUMENT_SIGNED: {
    title: '✅ تم توقيع المستند بنجاح',
    body: `تم توقيع المستند "{documentName}" بنجاح.

📄 اسم المستند: {documentName}
🔢 الرقم التسلسلي: {serialNumber}
👤 الموقع: {signerName}
📅 تاريخ التوقيع: {signedDate}
⏰ وقت التوقيع: {signedTime}
📊 حجم الملف: {fileSize}

تم حفظ المستند الموقع بأمان في النظام.

🔗 يمكنك تحميل المستند أو عرضه من خلال لوحة التحكم.

نظام التوقيع الإلكتروني العربي 🇸🇦`,
    
    admin: `🔔 إشعار إداري - تم توقيع مستند جديد

📄 المستند: {documentName}
🔢 الرقم التسلسلي: {serialNumber}
👤 المستخدم: {signerEmail}
📅 التاريخ: {signedDate} {signedTime}
📊 الحجم: {fileSize}

تفاصيل إضافية:
- معرف المستند: {documentId}
- معرف المستخدم: {userId}
- نوع الملف: PDF موقع رقمياً

نظام التوقيع الإلكتروني - الإدارة 🇸🇦`
  },
  
  DOCUMENT_UPLOADED: {
    title: '📤 تم رفع مستند جديد',
    body: `تم رفع المستند "{documentName}" بنجاح وهو جاهز للتوقيع.

📄 اسم المستند: {documentName}
📅 تاريخ الرفع: {uploadDate}
⏰ وقت الرفع: {uploadTime}
📊 حجم الملف: {fileSize}

يمكنك الآن توقيع المستند من خلال لوحة التحكم.

نظام التوقيع الإلكتروني العربي 🇸🇦`
  }
};

/**
 * English notification templates (fallback)
 */
const ENGLISH_TEMPLATES = {
  DOCUMENT_SIGNED: {
    title: '✅ Document Signed Successfully',
    body: `Document "{documentName}" has been signed successfully.

📄 Document Name: {documentName}
🔢 Serial Number: {serialNumber}
👤 Signer: {signerName}
📅 Signed Date: {signedDate}
⏰ Signed Time: {signedTime}
📊 File Size: {fileSize}

The signed document has been securely saved in the system.

🔗 You can download or view the document from your dashboard.

Arabic E-Signature System 🇸🇦`,
    
    admin: `🔔 Admin Notification - New Document Signed

📄 Document: {documentName}
🔢 Serial Number: {serialNumber}
👤 User: {signerEmail}
📅 Date: {signedDate} {signedTime}
📊 Size: {fileSize}

Additional Details:
- Document ID: {documentId}
- User ID: {userId}
- File Type: Digitally Signed PDF

E-Signature System - Administration 🇸🇦`
  }
};

/**
 * Format file size in human readable format
 */
const formatFileSize = (bytes, language = 'ar') => {
  if (!bytes || bytes === 0) return language === 'ar' ? 'غير محدد' : 'Unknown';
  
  const sizes = language === 'ar' 
    ? ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
    : ['B', 'KB', 'MB', 'GB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const size = (bytes / Math.pow(1024, i)).toFixed(1);
  
  return language === 'ar' 
    ? `${convertToArabicNumerals(size)} ${sizes[i]}`
    : `${size} ${sizes[i]}`;
};

/**
 * Get user display name (fallback to email if no full name)
 */
const getUserDisplayName = (user) => {
  if (user.full_name && user.full_name.trim()) {
    return user.full_name.trim();
  }
  
  // Extract name from email (before @)
  if (user.email) {
    const emailName = user.email.split('@')[0];
    return emailName.charAt(0).toUpperCase() + emailName.slice(1);
  }
  
  return 'مستخدم غير معروف'; // Unknown user
};

/**
 * Generate document signed notification content
 */
const generateDocumentSignedNotification = (documentData, userData, language = 'ar', isAdmin = false) => {
  const templates = language === 'ar' ? ARABIC_TEMPLATES : ENGLISH_TEMPLATES;
  const template = templates.DOCUMENT_SIGNED;
  
  const signedDate = new Date(documentData.signedAt || documentData.signed_at);
  const signerName = getUserDisplayName(userData);
  
  const placeholders = {
    documentName: documentData.originalFilename || documentData.original_filename,
    serialNumber: documentData.serialNumber || documentData.serial_number,
    signerName: signerName,
    signerEmail: userData.email,
    signedDate: formatEnglishDate(signedDate),
    signedTime: formatEnglishTime(signedDate),
    fileSize: formatFileSize(documentData.fileSize || documentData.file_size, language),
    documentId: documentData.id,
    userId: userData.id
  };
  
  // Use admin template if this is for admin notification
  const messageTemplate = isAdmin ? template.admin : template.body;
  
  // Replace placeholders in the message
  let message = messageTemplate;
  Object.keys(placeholders).forEach(key => {
    const regex = new RegExp(`{${key}}`, 'g');
    message = message.replace(regex, placeholders[key] || '');
  });
  
  return {
    title: template.title,
    message: message,
    metadata: {
      type: 'document_signed',
      documentId: documentData.id,
      userId: userData.id,
      language: language,
      isAdmin: isAdmin,
      timestamp: new Date().toISOString()
    }
  };
};

/**
 * Generate document uploaded notification content
 */
const generateDocumentUploadedNotification = (documentData, userData, language = 'ar') => {
  const templates = language === 'ar' ? ARABIC_TEMPLATES : ENGLISH_TEMPLATES;
  const template = templates.DOCUMENT_UPLOADED;
  
  const uploadDate = new Date(documentData.createdAt || documentData.created_at);
  
  const placeholders = {
    documentName: documentData.originalFilename || documentData.original_filename,
    uploadDate: formatEnglishDate(uploadDate),
    uploadTime: formatEnglishTime(uploadDate),
    fileSize: formatFileSize(documentData.fileSize || documentData.file_size, language)
  };
  
  // Replace placeholders in the message
  let message = template.body;
  Object.keys(placeholders).forEach(key => {
    const regex = new RegExp(`{${key}}`, 'g');
    message = message.replace(regex, placeholders[key] || '');
  });
  
  return {
    title: template.title,
    message: message,
    metadata: {
      type: 'document_uploaded',
      documentId: documentData.id,
      userId: userData.id,
      language: language,
      timestamp: new Date().toISOString()
    }
  };
};

/**
 * Generate custom notification message
 */
const generateCustomNotification = (title, content, metadata = {}) => {
  return {
    title: title,
    message: content,
    metadata: {
      type: 'custom',
      timestamp: new Date().toISOString(),
      ...metadata
    }
  };
};

/**
 * Validate notification content
 */
const validateNotificationContent = (content) => {
  if (!content || typeof content !== 'object') {
    return { valid: false, error: 'Invalid content object' };
  }
  
  if (!content.message || content.message.trim().length === 0) {
    return { valid: false, error: 'Message content is required' };
  }
  
  if (content.message.length > 1600) { // WhatsApp message limit
    return { valid: false, error: 'Message too long (max 1600 characters)' };
  }
  
  return { valid: true };
};

module.exports = {
  generateDocumentSignedNotification,
  generateDocumentUploadedNotification,
  generateCustomNotification,
  validateNotificationContent,
  formatFileSize,
  getUserDisplayName,
  ARABIC_TEMPLATES,
  ENGLISH_TEMPLATES
};
