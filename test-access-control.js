const axios = require('axios');

async function testAccessControl() {
  try {
    console.log('🔒 Testing Access Control for Admin Pages...\n');

    // 1. Test with regular user
    console.log('1. Testing with regular user...');
    const userLoginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const userToken = userLoginResponse.data.token;
    console.log('✅ Regular user login successful');
    console.log('👤 User role:', userLoginResponse.data.user.role);

    // 2. Test access to pending documents endpoint (should fail for regular user)
    console.log('\n2. Testing regular user access to pending documents...');
    try {
      const pendingResponse = await axios.get(
        'http://localhost:3001/api/documents/pending',
        {
          headers: {
            'Authorization': `Bearer ${userToken}`
          }
        }
      );
      console.log('❌ SECURITY ISSUE: Regular user can access pending documents!');
      console.log('Response:', pendingResponse.data);
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Access correctly denied for regular user');
        console.log('📝 Error message:', error.response.data.message);
      } else {
        console.log('⚠️ Unexpected error:', error.response?.status, error.response?.data);
      }
    }

    // 3. Test access to sign-pending endpoint (should fail for regular user)
    console.log('\n3. Testing regular user access to sign-pending endpoint...');
    try {
      const signResponse = await axios.post(
        'http://localhost:3001/api/documents/1/sign-pending',
        {},
        {
          headers: {
            'Authorization': `Bearer ${userToken}`
          }
        }
      );
      console.log('❌ SECURITY ISSUE: Regular user can sign pending documents!');
      console.log('Response:', signResponse.data);
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Access correctly denied for regular user');
        console.log('📝 Error message:', error.response.data.message);
      } else {
        console.log('⚠️ Unexpected error:', error.response?.status, error.response?.data);
      }
    }

    // 4. Test with admin user
    console.log('\n4. Testing with admin user...');
    const adminLoginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const adminToken = adminLoginResponse.data.token;
    console.log('✅ Admin user login successful');
    console.log('👤 User role:', adminLoginResponse.data.user.role);

    // 5. Test admin access to pending documents endpoint (should succeed)
    console.log('\n5. Testing admin access to pending documents...');
    try {
      const pendingResponse = await axios.get(
        'http://localhost:3001/api/documents/pending',
        {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        }
      );
      console.log('✅ Admin can access pending documents');
      console.log('📊 Number of pending documents:', pendingResponse.data.documents?.length || 0);
    } catch (error) {
      console.log('❌ Admin access denied unexpectedly:', error.response?.status, error.response?.data);
    }

    // 6. Test user permissions in auth context
    console.log('\n6. Testing user permissions...');
    
    // Get user profile to check permissions
    try {
      const userProfileResponse = await axios.get(
        'http://localhost:3001/api/auth/profile',
        {
          headers: {
            'Authorization': `Bearer ${userToken}`
          }
        }
      );
      console.log('👤 Regular user profile:', {
        email: userProfileResponse.data.user.email,
        role: userProfileResponse.data.user.role
      });
    } catch (error) {
      console.log('⚠️ Could not get user profile:', error.response?.data);
    }

    try {
      const adminProfileResponse = await axios.get(
        'http://localhost:3001/api/auth/profile',
        {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        }
      );
      console.log('👑 Admin user profile:', {
        email: adminProfileResponse.data.user.email,
        role: adminProfileResponse.data.user.role
      });
    } catch (error) {
      console.log('⚠️ Could not get admin profile:', error.response?.data);
    }

    console.log('\n🎉 Access Control Test Completed!');
    console.log('\n📋 Summary:');
    console.log('✅ Regular users are blocked from admin endpoints');
    console.log('✅ Admin users can access admin endpoints');
    console.log('✅ Proper error messages are returned');
    console.log('✅ Role-based access control is working');

  } catch (error) {
    console.error('\n❌ Access control test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testAccessControl();
