const { query } = require('./backend/src/models/database');
const bcrypt = require('./backend/node_modules/bcryptjs');

async function checkAndCreateAdmin() {
  try {
    console.log('Checking for admin users...');
    
    // Check existing users
    const users = await query('SELECT id, email, role FROM users ORDER BY created_at');
    console.log('\nExisting users:');
    users.rows.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (${user.role || 'no role'}) - ID: ${user.id}`);
    });
    
    // Check if admin user exists
    const adminCheck = await query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    
    if (adminCheck.rows.length === 0) {
      console.log('\nAdmin user not found. Creating admin user...');
      
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const result = await query(
        'INSERT INTO users (email, password_hash, role) VALUES ($1, $2, $3) RETURNING *',
        ['<EMAIL>', hashedPassword, 'admin']
      );
      
      console.log('✅ Admin user created:', result.rows[0]);
    } else {
      console.log('\n✅ Admin user already exists');
      
      // Update role if needed
      await query('UPDATE users SET role = $1 WHERE email = $2', ['admin', '<EMAIL>']);
      console.log('✅ Admin role updated');
    }
    
    // Check user permissions
    console.log('\nChecking user permissions...');
    const permissions = await query(`
      SELECT u.email, u.role, up.permission_name 
      FROM users u 
      LEFT JOIN user_permissions up ON u.id = up.user_id 
      ORDER BY u.email, up.permission_name
    `);
    
    if (permissions.rows.length > 0) {
      console.log('User permissions:');
      permissions.rows.forEach(row => {
        console.log(`- ${row.email}: ${row.permission_name || 'no permissions'}`);
      });
    } else {
      console.log('No user permissions found. Creating admin permissions...');
      
      // Get admin user ID
      const adminUser = await query('SELECT id FROM users WHERE email = $1', ['<EMAIL>']);
      if (adminUser.rows.length > 0) {
        const adminId = adminUser.rows[0].id;
        
        // Add sign_documents permission
        await query(
          'INSERT INTO user_permissions (user_id, permission_name) VALUES ($1, $2) ON CONFLICT DO NOTHING',
          [adminId, 'sign_documents']
        );
        
        console.log('✅ Admin permissions created');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
  
  process.exit(0);
}

checkAndCreateAdmin();
