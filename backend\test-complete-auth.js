const axios = require('axios');

async function testCompleteAuthenticationFlow() {
  try {
    console.log('🔐 Testing Complete Authentication Flow...\n');
    
    // Test 1: Login with existing user
    console.log('1. Testing login with existing user...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>', 
      password: 'password123'
    });
    
    console.log('✅ Login successful:', loginResponse.data.success);
    console.log('   User email:', loginResponse.data.user?.email);
    console.log('   Token length:', loginResponse.data.token?.length);
    
    const token = loginResponse.data.token;
    const authHeaders = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // Test 2: Dashboard data endpoints (the ones causing 403 errors)
    console.log('\n2. Testing dashboard endpoints that were failing...');
    
    // Test signatures endpoint
    try {
      const signaturesResponse = await axios.get('http://localhost:3001/api/signatures', {
        headers: authHeaders
      });
      console.log('✅ /api/signatures: Status', signaturesResponse.status);
      console.log('   Signatures count:', signaturesResponse.data.signatures?.length || 0);
    } catch (error) {
      console.error('❌ /api/signatures failed:', error.response?.status, error.response?.data);
    }
    
    // Test documents endpoint with pagination
    try {
      const documentsResponse = await axios.get('http://localhost:3001/api/documents?page=1&limit=5', {
        headers: authHeaders
      });
      console.log('✅ /api/documents?page=1&limit=5: Status', documentsResponse.status);
      console.log('   Documents count:', documentsResponse.data.documents?.length || 0);
      console.log('   Total documents:', documentsResponse.data.total || 0);
    } catch (error) {
      console.error('❌ /api/documents failed:', error.response?.status, error.response?.data);
    }
    
    // Test 3: Profile endpoint
    try {
      const profileResponse = await axios.get('http://localhost:3001/api/auth/profile', {
        headers: authHeaders
      });
      console.log('✅ /api/auth/profile: Status', profileResponse.status);
      console.log('   Profile email:', profileResponse.data.user?.email);
    } catch (error) {
      console.error('❌ /api/auth/profile failed:', error.response?.status, error.response?.data);
    }
    
    // Test 4: Test with invalid token (should get 403)
    console.log('\n3. Testing with invalid token (should get 403)...');
    try {
      await axios.get('http://localhost:3001/api/signatures', {
        headers: {
          'Authorization': 'Bearer invalid-token',
          'Content-Type': 'application/json'
        }
      });
      console.log('❌ Invalid token was accepted (this is a security issue!)');
    } catch (error) {
      if (error.response?.status === 403 || error.response?.status === 401) {
        console.log('✅ Invalid token correctly rejected with status:', error.response.status);
      } else {
        console.log('⚠️  Unexpected error with invalid token:', error.response?.status);
      }
    }
    
    // Test 5: Test without token (should get 403)
    console.log('\n4. Testing without token (should get 403)...');
    try {
      await axios.get('http://localhost:3001/api/signatures');
      console.log('❌ No token was accepted (this is a security issue!)');
    } catch (error) {
      if (error.response?.status === 403 || error.response?.status === 401) {
        console.log('✅ No token correctly rejected with status:', error.response.status);
      } else {
        console.log('⚠️  Unexpected error without token:', error.response?.status);
      }
    }
    
    console.log('\n🎉 Complete authentication flow test completed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ User login: Working');
    console.log('   ✅ JWT token validation: Working');
    console.log('   ✅ Protected endpoints: Accessible with valid token');
    console.log('   ✅ Security: Invalid/missing tokens properly rejected');
    console.log('\n🔧 Frontend Integration:');
    console.log('   1. Open http://localhost:3000/login');
    console.log('   2. Login with: <EMAIL> / password123');
    console.log('   3. Dashboard should now load without 403 errors');
    
    return { success: true, token, user: loginResponse.data.user };
    
  } catch (error) {
    console.error('\n❌ Complete authentication test failed:');
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    } else {
      console.error('   Error:', error.message);
    }
    return { success: false, error: error.message };
  }
}

// Run the test
if (require.main === module) {
  testCompleteAuthenticationFlow()
    .then((result) => {
      if (result.success) {
        console.log('\n✅ All authentication issues have been resolved!');
        process.exit(0);
      } else {
        console.log('\n❌ Authentication issues still exist.');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Unexpected error:', error.message);
      process.exit(1);
    });
}

module.exports = { testCompleteAuthenticationFlow };
