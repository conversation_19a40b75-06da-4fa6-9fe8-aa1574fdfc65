#!/usr/bin/env node

/**
 * Test script for the serial verification API endpoint
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

console.log('🧪 Testing Serial Verification API Endpoint');
console.log('===========================================\n');

async function testVerificationAPI() {
  try {
    // First, let's get a real serial number from the documents
    console.log('1. Getting a sample document serial number...');
    
    const { query } = require('./backend/src/models/database');
    const documentsResult = await query(
      'SELECT serial_number FROM documents WHERE status = \'signed\' LIMIT 1'
    );
    
    if (documentsResult.rows.length === 0) {
      console.log('❌ No signed documents found in database');
      console.log('   Please sign a document first to test verification');
      return;
    }
    
    const testSerialNumber = documentsResult.rows[0].serial_number;
    console.log(`✅ Found test serial number: ${testSerialNumber}`);

    // Test valid serial number
    console.log('\n2. Testing verification with valid serial number...');
    
    const response = await axios.get(`${API_BASE_URL}/documents/verify/${encodeURIComponent(testSerialNumber)}`);

    console.log('✅ Verification API call successful!');
    console.log(`Status: ${response.status}`);
    console.log(`Valid: ${response.data.isValid}`);
    
    if (response.data.document) {
      const doc = response.data.document;
      console.log('\n📋 Document details:');
      console.log(`   Serial Number: ${doc.serial_number}`);
      console.log(`   Original Filename: ${doc.original_filename}`);
      console.log(`   Signed Date: ${doc.signed_date}`);
      console.log(`   User ID: ${doc.user_id}`);
      console.log(`   User Email: ${doc.user_email}`);
      console.log(`   File Size: ${doc.file_size} bytes`);
    }

    // Test invalid serial number
    console.log('\n3. Testing verification with invalid serial number...');
    
    try {
      const invalidResponse = await axios.get(`${API_BASE_URL}/documents/verify/INVALID123456789`);
      console.log('❌ Expected 404 error but got success');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Invalid serial number correctly returned 404');
        console.log(`   Message: ${error.response.data.error}`);
      } else {
        console.log('❌ Unexpected error for invalid serial:', error.message);
      }
    }

    console.log('\n🎯 Serial verification API test completed successfully!');

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data:`, error.response.data);
    }
  }
}

// Run the test
testVerificationAPI();
