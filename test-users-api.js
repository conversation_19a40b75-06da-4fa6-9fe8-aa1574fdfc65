#!/usr/bin/env node

/**
 * Test script for the users API endpoint
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIwZjFlYmIzZS03NWY2LTQ0ZGQtOGFiYS1iZjJlM2U4NTdhMDQiLCJpYXQiOjE3NTI2ODQ4MjcsImV4cCI6MTc1Mjc3MTIyN30.fHhH70Nf1L11XiXq6EvHzlOg71x2B-qFkmQL9PpZVS4';

console.log('🧪 Testing Users API Endpoint');
console.log('=============================\n');

async function testUsersAPI() {
  try {
    console.log('1. Testing GET /api/auth/users...');
    
    const response = await axios.get(`${API_BASE_URL}/auth/users`, {
      headers: { 
        Authorization: `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ API call successful!');
    console.log(`Status: ${response.status}`);
    console.log(`Users found: ${response.data.users?.length || 0}`);
    
    if (response.data.users && response.data.users.length > 0) {
      console.log('\n📋 Sample users:');
      response.data.users.slice(0, 3).forEach((user, index) => {
        console.log(`${index + 1}. ID: ${user.id}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Name: ${user.full_name || 'Not set'}`);
        console.log(`   Created: ${user.created_at}`);
        console.log(`   Status: ${user.status}`);
        console.log('');
      });
    }

    console.log('🎯 Users API test completed successfully!');

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data:`, error.response.data);
    }
  }
}

// Run the test
testUsersAPI();
