const { query } = require('../src/models/database');

async function createSignedDocumentsTable() {
  try {
    console.log('Creating signed_documents table...');
    
    // Create signed_documents table
    await query(`
      CREATE TABLE IF NOT EXISTS signed_documents (
        id SERIAL PRIMARY KEY,
        original_filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INTEGER NOT NULL,
        signed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        user_email VARCHAR(255) NOT NULL,
        serial_number VARCHAR(100) UNIQUE NOT NULL,
        digital_signature TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ signed_documents table created successfully');
    
    // Create indexes for better performance
    console.log('Creating indexes...');
    
    await query('CREATE INDEX IF NOT EXISTS idx_signed_documents_user_id ON signed_documents(user_id)');
    await query('CREATE INDEX IF NOT EXISTS idx_signed_documents_serial_number ON signed_documents(serial_number)');
    await query('CREATE INDEX IF NOT EXISTS idx_signed_documents_signed_date ON signed_documents(signed_date DESC)');
    
    console.log('✅ Indexes created successfully');
    
    // Add comments for documentation
    console.log('Adding table comments...');
    
    await query(`
      COMMENT ON TABLE signed_documents IS 'Documents that have been signed by administrators from the mail workflow'
    `);
    
    await query(`
      COMMENT ON COLUMN signed_documents.serial_number IS 'Unique serial number for the signed document'
    `);
    
    await query(`
      COMMENT ON COLUMN signed_documents.digital_signature IS 'Digital signature hash for document verification'
    `);
    
    console.log('✅ Table comments added successfully');
    
    // Create trigger for updated_at
    console.log('Creating updated_at trigger...');
    
    await query(`
      CREATE OR REPLACE FUNCTION update_signed_documents_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);
    
    await query(`
      DROP TRIGGER IF EXISTS trigger_signed_documents_updated_at ON signed_documents
    `);
    
    await query(`
      CREATE TRIGGER trigger_signed_documents_updated_at
        BEFORE UPDATE ON signed_documents
        FOR EACH ROW
        EXECUTE FUNCTION update_signed_documents_updated_at()
    `);
    
    console.log('✅ Updated_at trigger created successfully');
    
    // Verify table creation
    console.log('\n📊 Verification:');
    
    const tableInfo = await query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'signed_documents'
      ORDER BY ordinal_position
    `);
    
    console.log('Table structure:');
    tableInfo.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.column_name} (${row.data_type}) - ${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    const indexInfo = await query(`
      SELECT indexname, indexdef
      FROM pg_indexes
      WHERE tablename = 'signed_documents'
    `);
    
    console.log('\nIndexes:');
    indexInfo.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.indexname}`);
    });
    
    console.log('\n🎉 Signed documents table setup completed successfully!');
    console.log('📝 Features:');
    console.log('- ✅ Signed document storage');
    console.log('- ✅ Serial number tracking');
    console.log('- ✅ Digital signature verification');
    console.log('- ✅ User association and email tracking');
    console.log('- ✅ Timestamp tracking');
    console.log('- ✅ Performance indexes');
    console.log('- ✅ Automatic updated_at trigger');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error creating signed_documents table:', error);
    process.exit(1);
  }
}

// Run the migration
createSignedDocumentsTable();
