@tailwind base;
@tailwind components;
@tailwind utilities;

/* Arabic RTL Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  direction: rtl;
  text-align: right;
  font-family: 'Almarai', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: 'Almarai', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* RTL Layout Adjustments */
.sidebar {
  right: 0;
  left: auto;
}

.navbar {
  direction: rtl;
}

.navbar-brand {
  margin-right: 0;
  margin-left: auto;
}

/* Form Elements RTL */
input, select, textarea {
  text-align: right;
  direction: rtl;
}

input[type="email"] {
  direction: ltr;
  text-align: left;
}

/* Button Styles */
.btn {
  font-family: 'Almarai', sans-serif;
  border-radius: 4px;
  padding: 10px 20px;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

/* Table RTL */
.table {
  direction: rtl;
}

.table th:first-child {
  border-top-right-radius: 4px;
  border-top-left-radius: 0;
}

.table th:last-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 0;
}

/* Modal RTL */
.modal-header {
  direction: rtl;
}

.modal-header .close {
  margin-left: 0;
  margin-right: auto;
}

/* Upload Area */
.upload-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  background-color: #fafafa;
}

/* Enhanced RTL Support */
.rtl-flex {
  display: flex;
  flex-direction: row-reverse;
}

.rtl-space-x > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

.rtl-space-x-2 > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

.rtl-space-x-4 > * + * {
  margin-right: 1rem;
  margin-left: 0;
}

/* Arabic Text Improvements */
.arabic-text {
  font-family: 'Almarai', sans-serif;
  line-height: 1.6;
  text-align: right;
  direction: rtl;
}

/* Form Improvements for Arabic */
.form-input-rtl {
  text-align: right;
  direction: rtl;
  padding-right: 12px;
  padding-left: 12px;
}

.form-input-ltr {
  text-align: left;
  direction: ltr;
  padding-left: 12px;
  padding-right: 12px;
}

/* Button Improvements */
.btn-rtl {
  font-family: 'Almarai', sans-serif;
  font-weight: 500;
}

/* Table RTL Improvements */
.table-rtl {
  direction: rtl;
}

.table-rtl th {
  text-align: right;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

.table-rtl td {
  text-align: right;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

/* Card and Container RTL */
.card-rtl {
  direction: rtl;
  text-align: right;
}

/* Navigation RTL */
.nav-rtl {
  direction: rtl;
}

.nav-rtl .nav-item {
  margin-left: 1rem;
  margin-right: 0;
}

/* Loading Spinner RTL */
.spinner-rtl {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* Pagination RTL */
.pagination-rtl {
  direction: rtl;
}

.pagination-rtl .page-item {
  margin-left: 0.25rem;
  margin-right: 0;
}

/* Dropdown RTL */
.dropdown-rtl {
  text-align: right;
  direction: rtl;
}

/* Alert and Message RTL */
.alert-rtl {
  text-align: right;
  direction: rtl;
}

/* File Upload RTL */
.file-upload-rtl {
  text-align: center;
  direction: rtl;
}

/* Responsive RTL Adjustments */
@media (max-width: 768px) {
  .mobile-rtl {
    direction: rtl;
    text-align: right;
  }

  .mobile-rtl .flex {
    flex-direction: column;
  }

  .mobile-rtl .space-x-4 > * + * {
    margin-right: 0;
    margin-top: 1rem;
  }
}

/* Print Styles for Arabic */
@media print {
  body {
    font-family: 'Almarai', sans-serif;
    direction: rtl;
    text-align: right;
  }
}

.upload-label {
  display: inline-block;
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 20px;
}

.upload-input {
  display: none;
}

.error-message {
  color: #dc3545;
  margin-top: 10px;
  padding: 10px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

/* Professional Header Styles */
.navbar-logo {
  transition: transform 0.2s ease-in-out;
}

.navbar-logo:hover {
  transform: scale(1.05);
}

/* Enhanced Mobile Navigation */
.mobile-menu-overlay {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
}

/* Professional Button Styles */
.btn-professional {
  font-family: 'Almarai', sans-serif;
  font-weight: 500;
  border-radius: 8px;
  padding: 10px 20px;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-professional:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Navigation Links */
.nav-link-professional {
  position: relative;
  font-family: 'Almarai', sans-serif;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.nav-link-professional::after {
  content: '';
  position: absolute;
  bottom: -4px;
  right: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3B82F6, #1D4ED8);
  transition: width 0.3s ease-in-out;
}

.nav-link-professional:hover::after {
  width: 100%;
}

/* User Avatar Styles */
.user-avatar {
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Mobile Menu Animation */
.mobile-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.2s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .upload-area {
    padding: 20px;
  }

  /* Mobile Header Adjustments */
  .navbar-brand-mobile {
    font-size: 1rem;
    line-height: 1.2;
  }

  .navbar-logo-mobile {
    height: 2rem;
    width: 2rem;
  }
}

/* Large Screen Optimizations */
@media (min-width: 1280px) {
  .navbar-container-xl {
    max-width: 1280px;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .navbar-logo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
