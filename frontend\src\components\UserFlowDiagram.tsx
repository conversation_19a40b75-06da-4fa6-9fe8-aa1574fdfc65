import React from 'react';

interface FlowNode {
  id: string;
  title: string;
  description: string;
  type: 'start' | 'process' | 'decision' | 'end';
  connections: string[];
  userType?: 'admin' | 'user' | 'both';
}

interface UserFlowDiagramProps {
  userType: 'admin' | 'user';
  className?: string;
}

const UserFlowDiagram: React.FC<UserFlowDiagramProps> = ({ userType, className = '' }) => {
  const adminFlow: FlowNode[] = [
    {
      id: 'login',
      title: 'تسجيل الدخول',
      description: 'دخول المدير إلى النظام',
      type: 'start',
      connections: ['dashboard'],
      userType: 'admin'
    },
    {
      id: 'dashboard',
      title: 'لوحة التحكم',
      description: 'عرض إحصائيات النظام والمهام المعلقة',
      type: 'process',
      connections: ['review_docs', 'sign_docs', 'manage_users', 'view_records'],
      userType: 'admin'
    },
    {
      id: 'review_docs',
      title: 'مراجعة المستندات',
      description: 'مراجعة المستندات المرفوعة من المستخدمين',
      type: 'process',
      connections: ['approve_reject'],
      userType: 'admin'
    },
    {
      id: 'approve_reject',
      title: 'موافقة أو رفض',
      description: 'اتخاذ قرار بشأن المستند',
      type: 'decision',
      connections: ['sign_docs', 'notify_user'],
      userType: 'admin'
    },
    {
      id: 'sign_docs',
      title: 'توقيع المستندات',
      description: 'إضافة التوقيع الرقمي للمستندات المعتمدة',
      type: 'process',
      connections: ['notify_user'],
      userType: 'admin'
    },
    {
      id: 'manage_users',
      title: 'إدارة المستخدمين',
      description: 'إضافة وتعديل وحذف المستخدمين',
      type: 'process',
      connections: ['dashboard'],
      userType: 'admin'
    },
    {
      id: 'view_records',
      title: 'عرض السجلات',
      description: 'مراجعة سجل جميع العمليات',
      type: 'process',
      connections: ['dashboard'],
      userType: 'admin'
    },
    {
      id: 'notify_user',
      title: 'إشعار المستخدم',
      description: 'إرسال إشعار للمستخدم بحالة المستند',
      type: 'end',
      connections: [],
      userType: 'admin'
    }
  ];

  const userFlow: FlowNode[] = [
    {
      id: 'login',
      title: 'تسجيل الدخول',
      description: 'دخول المستخدم إلى النظام',
      type: 'start',
      connections: ['dashboard'],
      userType: 'user'
    },
    {
      id: 'dashboard',
      title: 'لوحة التحكم',
      description: 'عرض حالة المستندات والإشعارات',
      type: 'process',
      connections: ['upload_doc', 'view_history', 'settings'],
      userType: 'user'
    },
    {
      id: 'upload_doc',
      title: 'رفع مستند',
      description: 'اختيار ورفع مستند للمراجعة',
      type: 'process',
      connections: ['wait_review'],
      userType: 'user'
    },
    {
      id: 'wait_review',
      title: 'انتظار المراجعة',
      description: 'انتظار مراجعة المدير للمستند',
      type: 'process',
      connections: ['receive_notification'],
      userType: 'user'
    },
    {
      id: 'receive_notification',
      title: 'استلام الإشعار',
      description: 'استلام إشعار بحالة المستند',
      type: 'decision',
      connections: ['download_signed', 'resubmit'],
      userType: 'user'
    },
    {
      id: 'download_signed',
      title: 'تحميل المستند الموقع',
      description: 'تحميل المستند بعد التوقيع',
      type: 'process',
      connections: ['view_history'],
      userType: 'user'
    },
    {
      id: 'resubmit',
      title: 'إعادة الإرسال',
      description: 'تعديل وإعادة إرسال المستند',
      type: 'process',
      connections: ['upload_doc'],
      userType: 'user'
    },
    {
      id: 'view_history',
      title: 'عرض السجل',
      description: 'مراجعة تاريخ المستندات',
      type: 'process',
      connections: ['dashboard'],
      userType: 'user'
    },
    {
      id: 'settings',
      title: 'الإعدادات',
      description: 'تعديل إعدادات الحساب',
      type: 'end',
      connections: [],
      userType: 'user'
    }
  ];

  const currentFlow = userType === 'admin' ? adminFlow : userFlow;

  const getNodeColor = (type: string) => {
    switch (type) {
      case 'start': return 'bg-green-100 border-green-500 text-green-800';
      case 'process': return 'bg-blue-100 border-blue-500 text-blue-800';
      case 'decision': return 'bg-yellow-100 border-yellow-500 text-yellow-800';
      case 'end': return 'bg-red-100 border-red-500 text-red-800';
      default: return 'bg-gray-100 border-gray-500 text-gray-800';
    }
  };

  const getNodeShape = (type: string) => {
    switch (type) {
      case 'start': return 'rounded-full';
      case 'decision': return 'transform rotate-45';
      case 'end': return 'rounded-full';
      default: return 'rounded-lg';
    }
  };

  return (
    <div className={`p-6 bg-white rounded-lg shadow-lg ${className}`}>
      <h3 className="text-xl font-bold mb-6 text-center">
        مخطط سير العمل - {userType === 'admin' ? 'المدير' : 'المستخدم'}
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {currentFlow.map((node, index) => (
          <div key={node.id} className="relative">
            <div
              className={`p-4 border-2 ${getNodeColor(node.type)} ${getNodeShape(node.type)} 
                ${node.type === 'decision' ? 'w-32 h-32 flex items-center justify-center' : 'min-h-24'}`}
            >
              <div className={node.type === 'decision' ? 'transform -rotate-45 text-center' : ''}>
                <h4 className="font-semibold text-sm mb-1">{node.title}</h4>
                <p className="text-xs">{node.description}</p>
              </div>
            </div>
            
            {/* Connection arrows */}
            {node.connections.length > 0 && (
              <div className="absolute top-1/2 left-full transform -translate-y-1/2 ml-2">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-6 flex justify-center space-x-6 space-x-reverse text-sm">
        <div className="flex items-center">
          <div className="w-4 h-4 bg-green-100 border border-green-500 rounded-full ml-2"></div>
          <span>بداية</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-blue-100 border border-blue-500 rounded ml-2"></div>
          <span>عملية</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-yellow-100 border border-yellow-500 transform rotate-45 ml-2"></div>
          <span>قرار</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-red-100 border border-red-500 rounded-full ml-2"></div>
          <span>نهاية</span>
        </div>
      </div>
    </div>
  );
};

export default UserFlowDiagram;
