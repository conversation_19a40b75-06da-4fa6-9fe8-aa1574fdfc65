const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001/api';

async function testAuthenticationAndSigning() {
  try {
    console.log('🔐 Testing Authentication and Document Signing...\n');

    // Test login with existing user
    console.log('1. Testing login...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123' // Assuming this is the password
    });

    if (loginResponse.data.success) {
      console.log('✅ Login successful!');
      console.log(`User: ${loginResponse.data.user.email}`);
      console.log(`Token: ${loginResponse.data.token.substring(0, 20)}...`);
    } else {
      throw new Error('Login failed');
    }

    const token = loginResponse.data.token;
    const authHeaders = {
      'Authorization': `Bearer ${token}`
    };

    // Test getting signatures
    console.log('\n2. Testing signatures API...');
    try {
      const signaturesResponse = await axios.get(`${API_BASE_URL}/signatures`, {
        headers: authHeaders
      });
      console.log('✅ Signatures API accessible');
      console.log(`Found ${signaturesResponse.data.signatures?.length || 0} signatures`);
    } catch (error) {
      console.log('❌ Signatures API failed:', error.response?.data?.error || error.message);
    }

    // Test getting documents
    console.log('\n3. Testing documents API...');
    try {
      const documentsResponse = await axios.get(`${API_BASE_URL}/documents`, {
        headers: authHeaders
      });
      console.log('✅ Documents API accessible');
      console.log(`Found ${documentsResponse.data.documents?.length || 0} documents`);
    } catch (error) {
      console.log('❌ Documents API failed:', error.response?.data?.error || error.message);
    }

    console.log('\n🎉 Authentication test completed!');
    console.log('\n📝 To test document signing:');
    console.log('1. Open http://localhost:3000/login in your browser');
    console.log('2. Login with: <EMAIL> / password123');
    console.log('3. Navigate to document signing page');
    console.log('4. Upload a signature first, then sign a PDF document');

  } catch (error) {
    if (error.response) {
      console.error('❌ API Error:', error.response.status, error.response.data);
    } else {
      console.error('❌ Error:', error.message);
    }
    
    console.log('\n🔧 Troubleshooting:');
    console.log('- Make sure the backend server is running on port 3001');
    console.log('- Check if the user exists and password is correct');
    console.log('- Verify database connection is working');
  }
}

// Run the test
if (require.main === module) {
  testAuthenticationAndSigning()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = { testAuthenticationAndSigning };
