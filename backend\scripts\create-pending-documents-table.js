const { query } = require('../src/models/database');

async function createPendingDocumentsTable() {
  try {
    console.log('Creating pending_documents table...');
    
    // Create pending_documents table
    await query(`
      CREATE TABLE IF NOT EXISTS pending_documents (
        id SERIAL PRIMARY KEY,
        original_filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INTEGER NOT NULL,
        uploaded_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        uploader_email VARCHAR(255) NOT NULL,
        notes TEXT,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'signed', 'rejected')),
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        signed_at TIMESTAMP,
        signed_by UUID REFERENCES users(id),
        rejected_at TIMESTAMP,
        rejected_by UUID REFERENCES users(id),
        rejection_reason TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ pending_documents table created successfully');
    
    // Create indexes for better performance
    console.log('Creating indexes...');
    
    await query('CREATE INDEX IF NOT EXISTS idx_pending_documents_status ON pending_documents(status)');
    await query('CREATE INDEX IF NOT EXISTS idx_pending_documents_uploaded_by ON pending_documents(uploaded_by)');
    await query('CREATE INDEX IF NOT EXISTS idx_pending_documents_uploaded_at ON pending_documents(uploaded_at DESC)');
    
    console.log('✅ Indexes created successfully');
    
    // Add comments for documentation
    console.log('Adding table comments...');
    
    await query(`
      COMMENT ON TABLE pending_documents IS 'Documents uploaded by users for admin review and signing'
    `);
    
    await query(`
      COMMENT ON COLUMN pending_documents.status IS 'Document status: pending (awaiting review), signed (approved and signed), rejected (declined)'
    `);
    
    await query(`
      COMMENT ON COLUMN pending_documents.notes IS 'Optional notes from uploader about the document'
    `);
    
    await query(`
      COMMENT ON COLUMN pending_documents.rejection_reason IS 'Reason provided by admin when rejecting document'
    `);
    
    console.log('✅ Table comments added successfully');
    
    // Create trigger for updated_at
    console.log('Creating updated_at trigger...');
    
    await query(`
      CREATE OR REPLACE FUNCTION update_pending_documents_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);
    
    await query(`
      DROP TRIGGER IF EXISTS trigger_pending_documents_updated_at ON pending_documents
    `);
    
    await query(`
      CREATE TRIGGER trigger_pending_documents_updated_at
        BEFORE UPDATE ON pending_documents
        FOR EACH ROW
        EXECUTE FUNCTION update_pending_documents_updated_at()
    `);
    
    console.log('✅ Updated_at trigger created successfully');
    
    // Verify table creation
    console.log('\n📊 Verification:');
    
    const tableInfo = await query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'pending_documents'
      ORDER BY ordinal_position
    `);
    
    console.log('Table structure:');
    tableInfo.rows.forEach((col, index) => {
      console.log(`${index + 1}. ${col.column_name} (${col.data_type}) - ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    const indexInfo = await query(`
      SELECT indexname, indexdef
      FROM pg_indexes
      WHERE tablename = 'pending_documents'
    `);
    
    console.log('\nIndexes:');
    indexInfo.rows.forEach((idx, index) => {
      console.log(`${index + 1}. ${idx.indexname}`);
    });
    
    console.log('\n🎉 Pending documents table setup completed successfully!');
    console.log('📝 Features:');
    console.log('- ✅ Document upload for review');
    console.log('- ✅ Status tracking (pending/signed/rejected)');
    console.log('- ✅ User association and email tracking');
    console.log('- ✅ Optional notes from uploaders');
    console.log('- ✅ Rejection reason tracking');
    console.log('- ✅ Timestamp tracking for all actions');
    console.log('- ✅ Performance indexes');
    console.log('- ✅ Automatic updated_at trigger');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error creating pending_documents table:', error);
    process.exit(1);
  }
}

// Run the migration
createPendingDocumentsTable();
