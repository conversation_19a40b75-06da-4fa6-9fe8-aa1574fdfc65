const { query } = require('../models/database');

/**
 * Get notification statistics for monitoring
 */
const getNotificationStats = async (timeframe = '24h') => {
  try {
    let timeCondition = '';
    
    switch (timeframe) {
      case '1h':
        timeCondition = "created_at >= NOW() - INTERVAL '1 hour'";
        break;
      case '24h':
        timeCondition = "created_at >= NOW() - INTERVAL '24 hours'";
        break;
      case '7d':
        timeCondition = "created_at >= NOW() - INTERVAL '7 days'";
        break;
      case '30d':
        timeCondition = "created_at >= NOW() - INTERVAL '30 days'";
        break;
      default:
        timeCondition = "created_at >= NOW() - INTERVAL '24 hours'";
    }

    const statsQuery = `
      SELECT 
        notification_type,
        COUNT(*) as total_attempts,
        COUNT(CASE WHEN success = true THEN 1 END) as successful,
        COUNT(CASE WHEN success = false THEN 1 END) as failed,
        AVG(retry_count) as avg_retry_count,
        MAX(retry_count) as max_retry_count,
        COUNT(CASE WHEN retry_count > 0 THEN 1 END) as retried_attempts
      FROM notification_logs 
      WHERE ${timeCondition}
      GROUP BY notification_type
    `;

    const result = await query(statsQuery);
    
    const stats = {};
    result.rows.forEach(row => {
      stats[row.notification_type] = {
        totalAttempts: parseInt(row.total_attempts),
        successful: parseInt(row.successful),
        failed: parseInt(row.failed),
        successRate: row.total_attempts > 0 ? 
          Math.round((row.successful / row.total_attempts) * 100) : 0,
        avgRetryCount: parseFloat(row.avg_retry_count) || 0,
        maxRetryCount: parseInt(row.max_retry_count) || 0,
        retriedAttempts: parseInt(row.retried_attempts)
      };
    });

    return {
      timeframe,
      stats,
      generatedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error getting notification stats:', error);
    return {
      timeframe,
      stats: {},
      error: error.message,
      generatedAt: new Date().toISOString()
    };
  }
};

/**
 * Get recent notification failures for debugging
 */
const getRecentFailures = async (limit = 50) => {
  try {
    const failuresQuery = `
      SELECT 
        nl.id,
        nl.user_id,
        nl.document_id,
        nl.notification_type,
        nl.recipients,
        nl.message_content,
        nl.result_data,
        nl.retry_count,
        nl.created_at,
        u.email as user_email,
        d.original_filename as document_name
      FROM notification_logs nl
      LEFT JOIN users u ON nl.user_id = u.id
      LEFT JOIN documents d ON nl.document_id = d.id
      WHERE nl.success = false
      ORDER BY nl.created_at DESC
      LIMIT $1
    `;

    const result = await query(failuresQuery, [limit]);
    
    return result.rows.map(row => ({
      id: row.id,
      userId: row.user_id,
      userEmail: row.user_email,
      documentId: row.document_id,
      documentName: row.document_name,
      notificationType: row.notification_type,
      recipients: row.recipients,
      messageContent: row.message_content?.substring(0, 100) + '...', // Truncate for display
      resultData: row.result_data,
      retryCount: row.retry_count,
      createdAt: row.created_at
    }));

  } catch (error) {
    console.error('Error getting recent failures:', error);
    return [];
  }
};

/**
 * Get notification health status
 */
const getNotificationHealth = async () => {
  try {
    // Check last 1 hour stats
    const recentStats = await getNotificationStats('1h');
    const last24hStats = await getNotificationStats('24h');
    
    // Calculate health metrics
    const whatsappRecent = recentStats.stats.whatsapp || { totalAttempts: 0, successRate: 100 };
    const whatsapp24h = last24hStats.stats.whatsapp || { totalAttempts: 0, successRate: 100 };
    
    // Health thresholds
    const isHealthy = whatsappRecent.successRate >= 80 && whatsapp24h.successRate >= 70;
    const isWarning = whatsappRecent.successRate >= 60 && whatsapp24h.successRate >= 50;
    
    let status = 'healthy';
    if (!isHealthy && !isWarning) {
      status = 'critical';
    } else if (!isHealthy) {
      status = 'warning';
    }

    return {
      status,
      isHealthy,
      metrics: {
        last1h: whatsappRecent,
        last24h: whatsapp24h
      },
      checkedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error checking notification health:', error);
    return {
      status: 'error',
      isHealthy: false,
      error: error.message,
      checkedAt: new Date().toISOString()
    };
  }
};

/**
 * Cleanup old notification logs (retention policy)
 */
const cleanupOldLogs = async (retentionDays = 90) => {
  try {
    const cleanupQuery = `
      DELETE FROM notification_logs 
      WHERE created_at < NOW() - INTERVAL '${retentionDays} days'
    `;

    const result = await query(cleanupQuery);
    
    console.log(`🧹 Cleaned up ${result.rowCount} old notification logs (older than ${retentionDays} days)`);
    
    return {
      success: true,
      deletedCount: result.rowCount,
      retentionDays
    };

  } catch (error) {
    console.error('Error cleaning up old notification logs:', error);
    return {
      success: false,
      error: error.message,
      retentionDays
    };
  }
};

/**
 * Get user notification history
 */
const getUserNotificationHistory = async (userId, limit = 20) => {
  try {
    const historyQuery = `
      SELECT 
        nl.id,
        nl.document_id,
        nl.notification_type,
        nl.success,
        nl.retry_count,
        nl.created_at,
        d.original_filename as document_name,
        d.serial_number
      FROM notification_logs nl
      LEFT JOIN documents d ON nl.document_id = d.id
      WHERE nl.user_id = $1
      ORDER BY nl.created_at DESC
      LIMIT $2
    `;

    const result = await query(historyQuery, [userId, limit]);
    
    return result.rows.map(row => ({
      id: row.id,
      documentId: row.document_id,
      documentName: row.document_name,
      serialNumber: row.serial_number,
      notificationType: row.notification_type,
      success: row.success,
      retryCount: row.retry_count,
      createdAt: row.created_at
    }));

  } catch (error) {
    console.error('Error getting user notification history:', error);
    return [];
  }
};

/**
 * Retry failed notifications
 */
const retryFailedNotifications = async (maxAge = '1h', maxRetries = 3) => {
  try {
    // Get failed notifications that haven't exceeded max retries
    const failedQuery = `
      SELECT 
        nl.id,
        nl.user_id,
        nl.document_id,
        nl.recipients,
        nl.message_content,
        nl.retry_count
      FROM notification_logs nl
      WHERE nl.success = false 
        AND nl.retry_count < $1
        AND nl.created_at >= NOW() - INTERVAL '${maxAge}'
      ORDER BY nl.created_at DESC
    `;

    const result = await query(failedQuery, [maxRetries]);
    
    if (result.rows.length === 0) {
      return { success: true, retriedCount: 0, message: 'No failed notifications to retry' };
    }

    console.log(`🔄 Found ${result.rows.length} failed notifications to retry`);
    
    // Import notification service (avoid circular dependency)
    const { sendBulkWhatsAppNotification } = require('./whatsappNotificationService');
    
    let retriedCount = 0;
    let successCount = 0;

    for (const notification of result.rows) {
      try {
        const recipients = notification.recipients;
        const message = notification.message_content;
        
        const retryResult = await sendBulkWhatsAppNotification(recipients, message);
        
        // Update the notification log
        await query(
          `UPDATE notification_logs 
           SET success = $1, retry_count = retry_count + 1, 
               result_data = $2, updated_at = CURRENT_TIMESTAMP
           WHERE id = $3`,
          [retryResult.success, JSON.stringify(retryResult), notification.id]
        );

        retriedCount++;
        if (retryResult.success) {
          successCount++;
        }

        // Small delay between retries
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (retryError) {
        console.error(`Failed to retry notification ${notification.id}:`, retryError);
      }
    }

    console.log(`✅ Retry completed: ${successCount}/${retriedCount} successful`);

    return {
      success: true,
      retriedCount,
      successCount,
      failedCount: retriedCount - successCount
    };

  } catch (error) {
    console.error('Error retrying failed notifications:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = {
  getNotificationStats,
  getRecentFailures,
  getNotificationHealth,
  cleanupOldLogs,
  getUserNotificationHistory,
  retryFailedNotifications
};
