import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../services/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { signatureAPI, documentAPI } from '../services/api';

interface DashboardStats {
  totalSignatures: number;
  totalDocuments: number;
  recentDocuments: any[];
}

const Dashboard: React.FC = () => {
  const { user, hasPermission } = useAuth();
  const { t } = useLanguage();
  const [stats, setStats] = useState<DashboardStats>({
    totalSignatures: 0,
    totalDocuments: 0,
    recentDocuments: []
  });
  const [loading, setLoading] = useState(true);

  const formatSignedDate = (dateString: string | undefined) => {
    // Handle undefined, null, or invalid date strings
    if (!dateString) {
      return 'غير محدد';
    }

    try {
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return 'تاريخ غير صالح';
      }

      // Format date in English locale
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'تاريخ غير صالح';
    }
  };

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const [signaturesResponse, documentsResponse] = await Promise.all([
          signatureAPI.getAll(),
          documentAPI.getAll(1, 5) // Get first 5 documents
        ]);

        setStats({
          totalSignatures: signaturesResponse.data.signatures.length,
          totalDocuments: documentsResponse.data.pagination.total,
          recentDocuments: documentsResponse.data.documents
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 px-4 sm:px-0">
      {/* Welcome Section */}
      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-2 font-['Almarai'] leading-tight">
          {t.dashboard.welcome}، {user?.email}!
        </h1>
        <p className="text-sm sm:text-base text-gray-600 font-['Almarai']">
          {t.dashboard.subtitle}
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Signatures Card */}
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
          <div className="flex items-center">
            <div className="p-2 sm:p-3 rounded-full bg-blue-100 text-blue-600 flex-shrink-0">
              <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </div>
            <div className="mr-3 sm:mr-4 min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-600 font-['Almarai'] truncate">{t.dashboard.totalSignatures}</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900 font-['Almarai']">{stats.totalSignatures}</p>
            </div>
          </div>
        </div>

        {/* Documents Card */}
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
          <div className="flex items-center">
            <div className="p-2 sm:p-3 rounded-full bg-green-100 text-green-600 flex-shrink-0">
              <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="mr-3 sm:mr-4 min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-600 font-['Almarai'] truncate">{t.dashboard.signedDocuments}</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900 font-['Almarai']">{stats.totalDocuments}</p>
            </div>
          </div>
        </div>

        {/* Status Card */}
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 sm:col-span-2 lg:col-span-1">
          <div className="flex items-center">
            <div className="p-2 sm:p-3 rounded-full bg-purple-100 text-purple-600 flex-shrink-0">
              <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="mr-3 sm:mr-4 min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-600 font-['Almarai'] truncate">{t.dashboard.status}</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900 font-['Almarai']">{t.dashboard.active}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
        <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-3 sm:mb-4 font-['Almarai']">{t.dashboard.quickActions}</h2>
        <div className={`grid grid-cols-1 gap-3 sm:gap-4 ${hasPermission('upload_signatures') ? 'sm:grid-cols-2 lg:grid-cols-3' : 'sm:grid-cols-2'}`}>
          {/* Upload Signature Action - Admin Only */}
          {hasPermission('upload_signatures') && (
            <Link
              to="/signature-upload"
              className="flex items-center p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-blue-300 transition-all duration-200 group"
            >
              <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors duration-200 flex-shrink-0">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <div className="mr-2 sm:mr-3 min-w-0 flex-1">
                <p className="font-medium text-gray-900 font-['Almarai'] text-sm sm:text-base truncate">{t.dashboard.uploadSignature}</p>
                <p className="text-xs sm:text-sm text-gray-600 font-['Almarai'] truncate">إضافة توقيع جديد</p>
              </div>
            </Link>
          )}

          {/* Sign Document Action */}
          <Link
            to="/document-signing"
            className="flex items-center p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-green-300 transition-all duration-200 group"
          >
            <div className="p-2 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors duration-200 flex-shrink-0">
              <svg className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <div className="mr-2 sm:mr-3 min-w-0 flex-1">
              <p className="font-medium text-gray-900 font-['Almarai'] text-sm sm:text-base truncate">{t.dashboard.signDocument}</p>
              <p className="text-xs sm:text-sm text-gray-600 font-['Almarai'] truncate">توقيع مستند PDF</p>
            </div>
          </Link>

          {/* View History Action */}
          <Link
            to="/history"
            className={`flex items-center p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-purple-300 transition-all duration-200 group ${!hasPermission('upload_signatures') ? 'sm:col-span-2' : 'sm:col-span-2 lg:col-span-1'}`}
          >
            <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors duration-200 flex-shrink-0">
              <svg className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="mr-2 sm:mr-3 min-w-0 flex-1">
              <p className="font-medium text-gray-900 font-['Almarai'] text-sm sm:text-base truncate">{t.dashboard.viewHistory}</p>
              <p className="text-xs sm:text-sm text-gray-600 font-['Almarai'] truncate">عرض جميع المستندات</p>
            </div>
          </Link>
        </div>
      </div>

      {/* Recent Documents */}
      {stats.recentDocuments.length > 0 ? (
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
          <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-3 sm:mb-4 font-['Almarai']">{t.dashboard.recentDocuments}</h2>
          <div className="space-y-2 sm:space-y-3">
            {stats.recentDocuments.map((doc) => (
              <div key={doc.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                <div className="min-w-0 flex-1 mb-2 sm:mb-0">
                  <p className="font-medium text-gray-900 font-['Almarai'] text-sm sm:text-base truncate">{doc.original_filename}</p>
                  <p className="text-xs sm:text-sm text-gray-600 font-['Almarai'] mt-1">
                    تم التوقيع في {formatSignedDate(doc.signed_date)}
                  </p>
                </div>
                <div className="flex justify-end sm:justify-start">
                  <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full font-['Almarai'] whitespace-nowrap">
                    موقع
                  </span>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-100">
            <Link
              to="/history"
              className="inline-flex items-center text-primary-500 hover:text-primary-600 font-medium font-['Almarai'] text-sm sm:text-base transition-colors duration-200"
            >
              عرض جميع المستندات
              <svg className="w-4 h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
          </div>
        </div>
      ) : (
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md text-center">
          <div className="max-w-sm mx-auto">
            {/* Empty State Icon */}
            <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 text-gray-300">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-2 sm:mb-4 font-['Almarai']">{t.dashboard.recentDocuments}</h2>
            <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 font-['Almarai'] leading-relaxed">{t.dashboard.noDocuments}</p>
            <Link
              to="/document-signing"
              className="inline-flex items-center justify-center bg-primary-500 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-md hover:bg-primary-600 transition-colors duration-200 font-medium font-['Almarai'] text-sm sm:text-base"
            >
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              {t.dashboard.startSigning}
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
