import React, { useState, useEffect } from 'react';
import { useBiometric } from '../hooks/useBiometric';
import MobileBiometricEnrollment from './MobileBiometricEnrollment';
import mobileDetection from '../utils/mobileDetection';

interface BiometricEnrollmentProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  className?: string;
}

type EnrollmentStep = 'intro' | 'capabilities' | 'consent' | 'enrollment' | 'success' | 'error';

const BiometricEnrollment: React.FC<BiometricEnrollmentProps> = ({
  onSuccess,
  onCancel,
  className = ''
}) => {
  const {
    capabilities,
    isSupported,
    isAvailable,
    isLoading,
    error,
    registerBiometric,
    checkCapabilities,
    clearError
  } = useBiometric();

  const [currentStep, setCurrentStep] = useState<EnrollmentStep>('intro');
  const [enrollmentError, setEnrollmentError] = useState<string | null>(null);
  const [isEnrolling, setIsEnrolling] = useState(false);

  // Check capabilities on mount
  useEffect(() => {
    checkCapabilities();
  }, [checkCapabilities]);

  // Update step based on capabilities
  useEffect(() => {
    if (capabilities) {
      if (!capabilities.isSupported) {
        setCurrentStep('error');
        setEnrollmentError('المصادقة البيومترية غير مدعومة في هذا المتصفح');
      } else if (!capabilities.isAvailable) {
        setCurrentStep('error');
        setEnrollmentError('المصادقة البيومترية غير متاحة على هذا الجهاز');
      }
    }
  }, [capabilities]);

  // Handle enrollment process
  const handleEnrollment = async () => {
    try {
      setIsEnrolling(true);
      setEnrollmentError(null);
      clearError();

      const result = await registerBiometric();

      if (result.success) {
        setCurrentStep('success');
        setTimeout(() => {
          onSuccess?.();
        }, 2000);
      } else {
        setCurrentStep('error');
        setEnrollmentError(result.message);
      }
    } catch (err: any) {
      setCurrentStep('error');
      setEnrollmentError(err.message || 'فشل في تسجيل المصادقة البيومترية');
    } finally {
      setIsEnrolling(false);
    }
  };

  // Get platform-specific information
  const getPlatformInfo = () => {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      return {
        name: 'Face ID',
        icon: '👤',
        description: 'استخدم Face ID للدخول السريع والآمن'
      };
    } else if (userAgent.includes('Mac')) {
      return {
        name: 'Touch ID',
        icon: '👆',
        description: 'استخدم Touch ID للدخول السريع والآمن'
      };
    } else if (userAgent.includes('Windows')) {
      return {
        name: 'Windows Hello',
        icon: '🔐',
        description: 'استخدم Windows Hello للدخول السريع والآمن'
      };
    }
    return {
      name: 'المصادقة البيومترية',
      icon: '🔐',
      description: 'استخدم المصادقة البيومترية للدخول السريع والآمن'
    };
  };

  const platformInfo = getPlatformInfo();

  // Use mobile version if on mobile device
  if (mobileDetection.shouldUseMobileUI()) {
    return (
      <MobileBiometricEnrollment
        onSuccess={onSuccess}
        onCancel={onCancel}
        className={className}
      />
    );
  }

  const renderStep = () => {
    switch (currentStep) {
      case 'intro':
        return (
          <div className="text-center" style={{ direction: 'rtl' }}>
            <div className="text-6xl mb-4">{platformInfo.icon}</div>
            <h3 className="text-xl font-bold mb-4">إعداد {platformInfo.name}</h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              {platformInfo.description}
              <br />
              سيتيح لك هذا تسجيل الدخول بسرعة وأمان دون الحاجة لكتابة كلمة المرور
            </p>
            <div className="flex gap-3 justify-center">
              <button
                onClick={() => setCurrentStep('capabilities')}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                المتابعة
              </button>
              <button
                onClick={onCancel}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        );

      case 'capabilities':
        return (
          <div className="text-center" style={{ direction: 'rtl' }}>
            <div className="text-4xl mb-4">🔍</div>
            <h3 className="text-xl font-bold mb-4">فحص إمكانيات الجهاز</h3>
            {isLoading ? (
              <div className="flex flex-col items-center gap-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="text-gray-600">جاري فحص إمكانيات المصادقة البيومترية...</p>
              </div>
            ) : capabilities ? (
              <div className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-center gap-2 text-green-700">
                    <span>✅</span>
                    <span>جهازك يدعم المصادقة البيومترية</span>
                  </div>
                </div>
                <div className="text-sm text-gray-600 space-y-2">
                  <div>المصادقات المدعومة: {capabilities.supportedAuthenticators.join(', ')}</div>
                  <div>مصادقة المنصة: {capabilities.platformAuthenticator ? 'متاحة' : 'غير متاحة'}</div>
                </div>
                <button
                  onClick={() => setCurrentStep('consent')}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  المتابعة
                </button>
              </div>
            ) : null}
          </div>
        );

      case 'consent':
        return (
          <div className="text-center" style={{ direction: 'rtl' }}>
            <div className="text-4xl mb-4">🛡️</div>
            <h3 className="text-xl font-bold mb-4">الخصوصية والأمان</h3>
            <div className="text-right space-y-4 mb-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 mb-2">معلومات مهمة:</h4>
                <ul className="text-sm text-blue-700 space-y-2">
                  <li>• بياناتك البيومترية لا تغادر جهازك أبداً</li>
                  <li>• نحن نحفظ فقط مفتاح التشفير العام</li>
                  <li>• يمكنك إلغاء المصادقة البيومترية في أي وقت</li>
                  <li>• كلمة المرور ستبقى متاحة كبديل</li>
                </ul>
              </div>
            </div>
            <div className="flex gap-3 justify-center">
              <button
                onClick={() => setCurrentStep('enrollment')}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                أوافق وأريد المتابعة
              </button>
              <button
                onClick={onCancel}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        );

      case 'enrollment':
        return (
          <div className="text-center" style={{ direction: 'rtl' }}>
            <div className="text-4xl mb-4">{platformInfo.icon}</div>
            <h3 className="text-xl font-bold mb-4">تسجيل {platformInfo.name}</h3>
            {isEnrolling ? (
              <div className="flex flex-col items-center gap-4">
                <div className="animate-pulse text-6xl">{platformInfo.icon}</div>
                <p className="text-gray-600">اتبع التعليمات التي تظهر على جهازك...</p>
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-gray-600 mb-6">
                  اضغط على الزر أدناه وستظهر لك تعليمات على جهازك لتسجيل {platformInfo.name}
                </p>
                <button
                  onClick={handleEnrollment}
                  className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors text-lg"
                >
                  بدء التسجيل
                </button>
              </div>
            )}
          </div>
        );

      case 'success':
        return (
          <div className="text-center" style={{ direction: 'rtl' }}>
            <div className="text-6xl mb-4">✅</div>
            <h3 className="text-xl font-bold mb-4 text-green-700">تم التسجيل بنجاح!</h3>
            <p className="text-gray-600 mb-6">
              تم تفعيل {platformInfo.name} بنجاح. يمكنك الآن استخدامه لتسجيل الدخول
            </p>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-700 text-sm">
                سيتم إعادة توجيهك تلقائياً...
              </p>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="text-center" style={{ direction: 'rtl' }}>
            <div className="text-6xl mb-4">❌</div>
            <h3 className="text-xl font-bold mb-4 text-red-700">فشل في التسجيل</h3>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-700">
                {enrollmentError || error || 'حدث خطأ غير متوقع'}
              </p>
            </div>
            <div className="flex gap-3 justify-center">
              <button
                onClick={() => {
                  setCurrentStep('enrollment');
                  setEnrollmentError(null);
                  clearError();
                }}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                إعادة المحاولة
              </button>
              <button
                onClick={onCancel}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`biometric-enrollment ${className}`}>
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
        {renderStep()}
      </div>
    </div>
  );
};

export default BiometricEnrollment;
