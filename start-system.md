# E-Signature System - Startup Guide

## Prerequisites

1. **Node.js** (v14 or higher)
2. **PostgreSQL** database
3. **npm** or **yarn**

## Setup Instructions

### 1. Database Setup

1. Install PostgreSQL and create a database named `esign`
2. Update the database credentials in `backend/.env`
3. Run the database initialization:

```bash
cd database
node init-db.js
```

### 2. Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Copy the environment file and configure it:
```bash
cp .env.example .env
```

3. Edit `.env` file with your database credentials:
```
NODE_ENV=development
PORT=3001
DB_HOST=localhost
DB_PORT=5432
DB_NAME=esign
DB_USER=your_postgres_user
DB_PASSWORD=your_postgres_password
JWT_SECRET=your-secret-key-here-change-in-production
ENCRYPTION_KEY=your-encryption-key-here-32-chars
```

4. Install dependencies:
```bash
npm install
```

5. Start the backend server:
```bash
npm run dev
```

The backend will be available at `http://localhost:3001`

### 3. Frontend Setup

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Copy the environment file:
```bash
cp .env.example .env
```

3. Install dependencies:
```bash
npm install
```

4. Start the frontend development server:
```bash
npm start
```

The frontend will be available at `http://localhost:3000`

## Testing the System

### 1. Manual Testing

1. Open `http://localhost:3000` in your browser
2. Register a new account
3. Upload a signature image (PNG, JPG, or SVG)
4. Upload a PDF document and sign it
5. View the signed document in the history

### 2. API Testing

Test the backend endpoints using curl or Postman:

```bash
# Health check
curl http://localhost:3001/health

# Register user
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Login
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### 3. Running Tests

```bash
cd backend
npm test
```

## Features

- ✅ User registration and authentication
- ✅ JWT-based security
- ✅ Signature upload and encryption
- ✅ PDF document signing
- ✅ Serial number generation
- ✅ Document history and management
- ✅ File download functionality
- ✅ Responsive web interface

## Security Features

- Password hashing with bcrypt
- JWT token authentication
- Input validation and sanitization
- Rate limiting
- Encrypted signature storage
- Audit logging
- CORS protection

## File Structure

```
esign/
├── backend/           # Node.js backend
├── frontend/          # React frontend
├── database/          # Database migrations
├── README.md
└── start-system.md
```

## Troubleshooting

### Common Issues

1. **Database connection error**: Check PostgreSQL is running and credentials are correct
2. **Port already in use**: Change PORT in backend/.env or stop other services
3. **CORS errors**: Ensure frontend URL is configured in backend CORS settings
4. **File upload errors**: Check file permissions in backend/uploads directory

### Logs

- Backend logs: Check console output when running `npm run dev`
- Database logs: Check PostgreSQL logs for connection issues
- Frontend logs: Check browser developer console

## Production Deployment

For production deployment:

1. Set `NODE_ENV=production` in backend/.env
2. Use a production PostgreSQL database
3. Configure proper JWT secrets and encryption keys
4. Set up HTTPS
5. Configure proper CORS origins
6. Set up file storage with proper permissions
