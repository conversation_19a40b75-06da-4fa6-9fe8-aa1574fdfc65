<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Console Debug for PDF Viewer</title>
    <style>
        body {
            font-family: 'Alma<PERSON>', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .log-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .error { color: #ff4444; }
        .warning { color: #ffaa00; }
        .success { color: #44ff44; }
        .info { color: #4444ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Browser Console Debug for PDF Viewer</h1>
        <p>هذه الصفحة تساعد في تشخيص مشاكل عارض PDF في المتصفح</p>

        <div class="debug-section">
            <h3>1. Browser Environment Check</h3>
            <button onclick="checkBrowserEnvironment()">فحص بيئة المتصفح</button>
            <div id="browserCheck" class="log-output"></div>
        </div>

        <div class="debug-section">
            <h3>2. PDF.js Library Test</h3>
            <button onclick="testPDFJS()">اختبار PDF.js</button>
            <div id="pdfjsCheck" class="log-output"></div>
        </div>

        <div class="debug-section">
            <h3>3. React-PDF Integration Test</h3>
            <button onclick="testReactPDF()">اختبار React-PDF</button>
            <div id="reactPdfCheck" class="log-output"></div>
        </div>

        <div class="debug-section">
            <h3>4. Network and CORS Test</h3>
            <button onclick="testNetworkAndCORS()">اختبار الشبكة و CORS</button>
            <div id="networkCheck" class="log-output"></div>
        </div>

        <div class="debug-section">
            <h3>5. Memory and Performance Test</h3>
            <button onclick="testMemoryAndPerformance()">اختبار الذاكرة والأداء</button>
            <div id="memoryCheck" class="log-output"></div>
        </div>

        <div class="debug-section">
            <h3>6. Console Error Monitor</h3>
            <button onclick="startErrorMonitoring()">بدء مراقبة الأخطاء</button>
            <button onclick="stopErrorMonitoring()">إيقاف المراقبة</button>
            <div id="errorMonitor" class="log-output"></div>
        </div>
    </div>

    <script>
        let errorMonitorActive = false;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function checkBrowserEnvironment() {
            clearLog('browserCheck');
            log('browserCheck', '🔍 Checking browser environment...', 'info');
            
            // Browser info
            log('browserCheck', `Browser: ${navigator.userAgent}`, 'info');
            log('browserCheck', `Language: ${navigator.language}`, 'info');
            log('browserCheck', `Platform: ${navigator.platform}`, 'info');
            
            // JavaScript features
            log('browserCheck', `ES6 Support: ${typeof Promise !== 'undefined' ? '✅' : '❌'}`, 'info');
            log('browserCheck', `Fetch API: ${typeof fetch !== 'undefined' ? '✅' : '❌'}`, 'info');
            log('browserCheck', `ArrayBuffer: ${typeof ArrayBuffer !== 'undefined' ? '✅' : '❌'}`, 'info');
            log('browserCheck', `Uint8Array: ${typeof Uint8Array !== 'undefined' ? '✅' : '❌'}`, 'info');
            log('browserCheck', `Blob: ${typeof Blob !== 'undefined' ? '✅' : '❌'}`, 'info');
            log('browserCheck', `URL.createObjectURL: ${typeof URL !== 'undefined' && typeof URL.createObjectURL !== 'undefined' ? '✅' : '❌'}`, 'info');
            
            // Memory info
            if (performance.memory) {
                log('browserCheck', `Memory - Used: ${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log('browserCheck', `Memory - Total: ${(performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log('browserCheck', `Memory - Limit: ${(performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`, 'info');
            }
            
            // Local storage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                log('browserCheck', 'LocalStorage: ✅', 'success');
                
                const token = localStorage.getItem('token');
                log('browserCheck', `Auth Token: ${token ? '✅ Found' : '❌ Missing'}`, token ? 'success' : 'error');
            } catch (e) {
                log('browserCheck', 'LocalStorage: ❌ Not available', 'error');
            }
        }

        async function testPDFJS() {
            clearLog('pdfjsCheck');
            log('pdfjsCheck', '📚 Testing PDF.js library...', 'info');
            
            // Check if PDF.js is loaded
            if (typeof pdfjsLib === 'undefined') {
                log('pdfjsCheck', '❌ PDF.js not loaded, loading from CDN...', 'warning');
                
                try {
                    const script = document.createElement('script');
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
                    document.head.appendChild(script);
                    
                    await new Promise((resolve, reject) => {
                        script.onload = resolve;
                        script.onerror = reject;
                        setTimeout(reject, 10000); // 10 second timeout
                    });
                    
                    log('pdfjsCheck', '✅ PDF.js loaded from CDN', 'success');
                } catch (error) {
                    log('pdfjsCheck', `❌ Failed to load PDF.js: ${error}`, 'error');
                    return;
                }
            } else {
                log('pdfjsCheck', '✅ PDF.js already loaded', 'success');
            }
            
            // Test PDF.js version and worker
            log('pdfjsCheck', `PDF.js version: ${pdfjsLib.version}`, 'info');
            
            // Set worker URL
            const workerUrl = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
            pdfjsLib.GlobalWorkerOptions.workerSrc = workerUrl;
            log('pdfjsCheck', `Worker URL: ${workerUrl}`, 'info');
            
            // Test worker accessibility
            try {
                const workerResponse = await fetch(workerUrl, { method: 'HEAD' });
                log('pdfjsCheck', `Worker accessible: ${workerResponse.ok ? '✅' : '❌'}`, workerResponse.ok ? 'success' : 'error');
            } catch (error) {
                log('pdfjsCheck', `Worker check failed: ${error}`, 'error');
            }
            
            // Test basic PDF.js functionality
            try {
                const testPdfData = new Uint8Array([37, 80, 68, 70]); // %PDF header
                log('pdfjsCheck', 'Testing PDF.js document loading...', 'info');
                // This will fail but we're testing if the API is available
                pdfjsLib.getDocument({ data: testPdfData });
                log('pdfjsCheck', '✅ PDF.js API is functional', 'success');
            } catch (error) {
                log('pdfjsCheck', `PDF.js API test: ${error.message}`, 'info');
            }
        }

        function testReactPDF() {
            clearLog('reactPdfCheck');
            log('reactPdfCheck', '⚛️ Testing React-PDF integration...', 'info');
            
            // Check if we're in a React environment
            if (typeof React === 'undefined') {
                log('reactPdfCheck', '❌ Not in React environment', 'warning');
                log('reactPdfCheck', 'This test should be run from the React application', 'info');
                return;
            }
            
            // Check for react-pdf
            log('reactPdfCheck', 'React-PDF should be tested within the React application', 'info');
            log('reactPdfCheck', 'Check the DocumentViewer component directly', 'info');
        }

        async function testNetworkAndCORS() {
            clearLog('networkCheck');
            log('networkCheck', '🌐 Testing network and CORS...', 'info');
            
            const token = localStorage.getItem('token');
            if (!token) {
                log('networkCheck', '❌ No authentication token found', 'error');
                return;
            }
            
            const API_BASE_URL = 'http://localhost:3001/api';
            
            // Test backend connectivity
            try {
                const healthResponse = await fetch('http://localhost:3001/health');
                log('networkCheck', `Backend health: ${healthResponse.ok ? '✅' : '❌'} (${healthResponse.status})`, healthResponse.ok ? 'success' : 'error');
            } catch (error) {
                log('networkCheck', `Backend connectivity: ❌ ${error}`, 'error');
            }
            
            // Test documents API
            try {
                const documentsResponse = await fetch(`${API_BASE_URL}/documents`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                log('networkCheck', `Documents API: ${documentsResponse.ok ? '✅' : '❌'} (${documentsResponse.status})`, documentsResponse.ok ? 'success' : 'error');
                
                if (documentsResponse.ok) {
                    const data = await documentsResponse.json();
                    const signedDocs = data.documents?.filter(doc => doc.status === 'signed') || [];
                    log('networkCheck', `Signed documents: ${signedDocs.length}`, 'info');
                    
                    if (signedDocs.length > 0) {
                        // Test PDF download
                        const testDoc = signedDocs[0];
                        const pdfUrl = `${API_BASE_URL}/documents/${testDoc.id}/view?token=${encodeURIComponent(token)}`;
                        
                        try {
                            const pdfResponse = await fetch(pdfUrl);
                            log('networkCheck', `PDF download: ${pdfResponse.ok ? '✅' : '❌'} (${pdfResponse.status})`, pdfResponse.ok ? 'success' : 'error');
                            
                            if (pdfResponse.ok) {
                                const contentType = pdfResponse.headers.get('content-type');
                                const contentLength = pdfResponse.headers.get('content-length');
                                log('networkCheck', `Content-Type: ${contentType}`, 'info');
                                log('networkCheck', `Content-Length: ${contentLength} bytes`, 'info');
                                
                                // Check CORS headers
                                const corsHeaders = [
                                    'access-control-allow-origin',
                                    'access-control-allow-methods',
                                    'access-control-allow-headers'
                                ];
                                
                                corsHeaders.forEach(header => {
                                    const value = pdfResponse.headers.get(header);
                                    if (value) {
                                        log('networkCheck', `${header}: ${value}`, 'info');
                                    }
                                });
                            }
                        } catch (error) {
                            log('networkCheck', `PDF download error: ${error}`, 'error');
                        }
                    }
                }
            } catch (error) {
                log('networkCheck', `Documents API error: ${error}`, 'error');
            }
        }

        function testMemoryAndPerformance() {
            clearLog('memoryCheck');
            log('memoryCheck', '💾 Testing memory and performance...', 'info');
            
            if (performance.memory) {
                const memory = performance.memory;
                log('memoryCheck', `Used JS Heap: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log('memoryCheck', `Total JS Heap: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log('memoryCheck', `JS Heap Limit: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`, 'info');
                
                const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                log('memoryCheck', `Memory usage: ${usagePercent.toFixed(1)}%`, usagePercent > 80 ? 'warning' : 'success');
            } else {
                log('memoryCheck', 'Memory API not available in this browser', 'warning');
            }
            
            // Test large array buffer creation (simulating PDF loading)
            try {
                const testSize = 10 * 1024 * 1024; // 10MB
                const testBuffer = new ArrayBuffer(testSize);
                log('memoryCheck', `✅ Can allocate ${testSize / 1024 / 1024}MB ArrayBuffer`, 'success');
                
                // Clean up
                // Note: ArrayBuffer will be garbage collected
            } catch (error) {
                log('memoryCheck', `❌ Failed to allocate test buffer: ${error}`, 'error');
            }
        }

        function startErrorMonitoring() {
            clearLog('errorMonitor');
            log('errorMonitor', '👁️ Starting error monitoring...', 'info');
            errorMonitorActive = true;
            
            // Override console methods
            console.error = function(...args) {
                if (errorMonitorActive) {
                    log('errorMonitor', `ERROR: ${args.join(' ')}`, 'error');
                }
                originalConsoleError.apply(console, args);
            };
            
            console.warn = function(...args) {
                if (errorMonitorActive) {
                    log('errorMonitor', `WARNING: ${args.join(' ')}`, 'warning');
                }
                originalConsoleWarn.apply(console, args);
            };
            
            // Listen for unhandled errors
            window.addEventListener('error', function(event) {
                if (errorMonitorActive) {
                    log('errorMonitor', `UNHANDLED ERROR: ${event.error?.message || event.message} at ${event.filename}:${event.lineno}`, 'error');
                }
            });
            
            // Listen for unhandled promise rejections
            window.addEventListener('unhandledrejection', function(event) {
                if (errorMonitorActive) {
                    log('errorMonitor', `UNHANDLED PROMISE REJECTION: ${event.reason}`, 'error');
                }
            });
            
            log('errorMonitor', '✅ Error monitoring active', 'success');
        }

        function stopErrorMonitoring() {
            errorMonitorActive = false;
            console.error = originalConsoleError;
            console.warn = originalConsoleWarn;
            log('errorMonitor', '⏹️ Error monitoring stopped', 'info');
        }

        // Auto-start browser environment check
        window.onload = function() {
            checkBrowserEnvironment();
        };
    </script>
</body>
</html>
