require('dotenv').config();

/**
 * Quick setup verification script
 */
async function checkSetup() {
  console.log('🔍 WhatsApp Notification Setup Verification\n');

  let allGood = true;

  // 1. Check environment variables
  console.log('1️⃣ Environment Variables:');
  const requiredEnvVars = [
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN', 
    'TWILIO_WHATSAPP_FROM'
  ];

  requiredEnvVars.forEach(varName => {
    const value = process.env[varName];
    if (value && value !== 'your-twilio-account-sid-here' && value !== 'your-twilio-auth-token-here') {
      console.log(`   ✅ ${varName}: Configured`);
    } else {
      console.log(`   ❌ ${varName}: Not configured`);
      allGood = false;
    }
  });

  // 2. Check optional environment variables
  console.log('\n2️⃣ Optional Configuration:');
  const optionalVars = [
    'WHATSAPP_NOTIFICATIONS_ENABLED',
    'WHATSAPP_ADMIN_NUMBERS',
    'WHATSAPP_RETRY_ATTEMPTS',
    'WHATSAPP_RETRY_DELAY'
  ];

  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`   ✅ ${varName}: ${value}`);
    } else {
      console.log(`   ⚠️  ${varName}: Using default`);
    }
  });

  // 3. Check database connection
  console.log('\n3️⃣ Database Connection:');
  try {
    const { query } = require('../models/database');
    await query('SELECT 1');
    console.log('   ✅ Database connection: Working');
  } catch (error) {
    console.log('   ❌ Database connection: Failed');
    console.log(`      Error: ${error.message}`);
    allGood = false;
  }

  // 4. Check if notification tables exist
  console.log('\n4️⃣ Database Tables:');
  try {
    const { query } = require('../models/database');
    
    // Check notification_logs table
    const notificationLogsCheck = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'notification_logs'
      );
    `);

    if (notificationLogsCheck.rows[0].exists) {
      console.log('   ✅ notification_logs table: Exists');
    } else {
      console.log('   ❌ notification_logs table: Missing');
      console.log('      Run: POST /api/setup/database');
      allGood = false;
    }

    // Check user table columns
    const userColumnsCheck = await query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name IN ('phone_number', 'whatsapp_notifications_enabled', 'notification_preferences');
    `);

    const expectedColumns = ['phone_number', 'whatsapp_notifications_enabled', 'notification_preferences'];
    const existingColumns = userColumnsCheck.rows.map(row => row.column_name);
    const missingColumns = expectedColumns.filter(col => !existingColumns.includes(col));

    if (missingColumns.length === 0) {
      console.log('   ✅ User table columns: All present');
    } else {
      console.log('   ❌ User table columns: Missing some');
      missingColumns.forEach(col => console.log(`      Missing: ${col}`));
      console.log('      Run: POST /api/setup/database');
      allGood = false;
    }

  } catch (error) {
    console.log('   ❌ Database table check: Failed');
    console.log(`      Error: ${error.message}`);
    allGood = false;
  }

  // 5. Check Twilio connection (if credentials are configured)
  if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN && 
      process.env.TWILIO_ACCOUNT_SID !== 'your-twilio-account-sid-here') {
    
    console.log('\n5️⃣ Twilio Connection:');
    try {
      const twilio = require('twilio');
      const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
      
      const account = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
      console.log(`   ✅ Twilio connection: Working`);
      console.log(`   📋 Account: ${account.friendlyName} (${account.status})`);
    } catch (error) {
      console.log('   ❌ Twilio connection: Failed');
      console.log(`      Error: ${error.message}`);
      allGood = false;
    }
  } else {
    console.log('\n5️⃣ Twilio Connection: Skipped (credentials not configured)');
  }

  // 6. Summary
  console.log('\n📋 Setup Summary:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  if (allGood) {
    console.log('🎉 Setup Status: READY');
    console.log('✅ All components are properly configured');
    console.log('\n🚀 Next Steps:');
    console.log('1. Start the server: npm run dev');
    console.log('2. Test notifications: POST /api/notifications/test');
    console.log('3. Add phone numbers to user profiles');
    console.log('4. Sign a document to trigger notifications');
  } else {
    console.log('⚠️  Setup Status: INCOMPLETE');
    console.log('❌ Some components need configuration');
    console.log('\n🔧 Required Actions:');
    console.log('1. Configure missing environment variables in .env');
    console.log('2. Run database setup if tables are missing');
    console.log('3. Verify Twilio credentials');
    console.log('4. Re-run this check: node src/scripts/checkSetup.js');
  }

  console.log('\n📚 For detailed instructions, see: SETUP_INSTRUCTIONS.md');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

  return allGood;
}

// Run check if this script is executed directly
if (require.main === module) {
  checkSetup()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Fatal error during setup check:', error);
      process.exit(1);
    });
}

module.exports = { checkSetup };
