import React, { useState, useCallback } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

// Import CSS files
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set up PDF.js worker with explicit version
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface PDFTestViewerProps {
  documentId: string;
  onClose: () => void;
}

const PDFTestViewer: React.FC<PDFTestViewerProps> = ({ documentId, onClose }) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [debugInfo, setDebugInfo] = useState<string>('');

  // Construct PDF URL
  const token = localStorage.getItem('token');
  const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
  const pdfUrl = `${API_BASE_URL}/documents/${documentId}/view?token=${encodeURIComponent(token || '')}`;

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    console.log('✅ PDF loaded successfully:', { numPages });
    setNumPages(numPages);
    setLoading(false);
    setError('');
    setDebugInfo(`✅ PDF loaded: ${numPages} pages`);
  }, []);

  const onDocumentLoadError = useCallback((error: Error) => {
    console.error('❌ PDF load error:', error);
    setError(`PDF Load Error: ${error.message}`);
    setLoading(false);
    setDebugInfo(`❌ Error: ${error.message}`);
  }, []);

  const onDocumentLoadProgress = useCallback(({ loaded, total }: { loaded: number; total: number }) => {
    const progress = total > 0 ? Math.round((loaded / total) * 100) : 0;
    console.log(`📥 PDF loading progress: ${progress}%`);
    setDebugInfo(`📥 Loading: ${progress}% (${loaded}/${total} bytes)`);
  }, []);

  const testDirectFetch = async () => {
    try {
      setDebugInfo('🔍 Testing direct fetch...');
      const response = await fetch(pdfUrl);
      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const header = new TextDecoder().decode(uint8Array.slice(0, 8));
      
      setDebugInfo(`✅ Direct fetch: ${contentType}, ${contentLength} bytes, header: "${header}"`);
      
      // Try to load with PDF.js directly
      const loadingTask = pdfjs.getDocument({ data: uint8Array });
      const pdf = await loadingTask.promise;
      setDebugInfo(`✅ PDF.js direct load: ${pdf.numPages} pages`);
      
    } catch (error) {
      console.error('❌ Direct fetch error:', error);
      setDebugInfo(`❌ Direct fetch error: ${error}`);
    }
  };

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center">
      <div className="bg-white w-11/12 h-5/6 max-w-4xl rounded-lg shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-bold">PDF Test Viewer</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        {/* Debug Info */}
        <div className="p-4 bg-gray-100 border-b">
          <p className="text-sm font-mono">{debugInfo}</p>
          <p className="text-xs text-gray-600 mt-1">URL: {pdfUrl}</p>
          <p className="text-xs text-gray-600">PDF.js version: {pdfjs.version}</p>
          <button
            onClick={testDirectFetch}
            className="mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded"
          >
            Test Direct Fetch
          </button>
        </div>

        {/* PDF Content */}
        <div className="flex-1 overflow-auto bg-gray-100 flex items-center justify-center p-4">
          {loading && (
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading PDF...</p>
            </div>
          )}

          {error && (
            <div className="text-center max-w-md">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-red-500 text-2xl">⚠</span>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">PDF Load Error</h3>
              <p className="text-red-600 mb-4 text-sm">{error}</p>
            </div>
          )}

          {!loading && !error && (
            <Document
              file={pdfUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadError}
              onLoadProgress={onDocumentLoadProgress}
              loading={
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                  <p className="text-gray-600">Loading document...</p>
                </div>
              }
              error={
                <div className="text-center">
                  <p className="text-red-600">Failed to load document</p>
                </div>
              }
              options={{
                cMapUrl: 'https://unpkg.com/pdfjs-dist@3.11.174/cmaps/',
                cMapPacked: true,
                standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@3.11.174/standard_fonts/',
              }}
            >
              <Page
                pageNumber={pageNumber}
                scale={1.0}
                renderTextLayer={true}
                renderAnnotationLayer={true}
                className="shadow-lg"
              />
            </Document>
          )}
        </div>

        {/* Navigation */}
        {numPages > 0 && (
          <div className="p-4 border-t bg-gray-50 flex items-center justify-center space-x-4">
            <button
              onClick={() => setPageNumber(Math.max(1, pageNumber - 1))}
              disabled={pageNumber <= 1}
              className="px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50"
            >
              Previous
            </button>
            <span className="text-sm">
              Page {pageNumber} of {numPages}
            </span>
            <button
              onClick={() => setPageNumber(Math.min(numPages, pageNumber + 1))}
              disabled={pageNumber >= numPages}
              className="px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PDFTestViewer;
