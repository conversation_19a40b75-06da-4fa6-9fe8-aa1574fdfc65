const { query } = require('../src/models/database');

async function runSimpleMigration() {
  try {
    console.log('Running simple user roles migration...');
    
    // Step 1: Add role column
    try {
      console.log('1. Adding role column to users table...');
      await query("ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT 'user'");
      console.log('✓ Role column added successfully');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠ Role column already exists, skipping');
      } else {
        throw error;
      }
    }
    
    // Step 2: Create index
    try {
      console.log('2. Creating index on role column...');
      await query('CREATE INDEX idx_users_role ON users(role)');
      console.log('✓ Index created successfully');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠ Index already exists, skipping');
      } else {
        throw error;
      }
    }
    
    // Step 3: Update first user to admin
    try {
      console.log('3. Setting first user as admin...');
      const result = await query(`
        UPDATE users SET role = 'admin' 
        WHERE id = (
          SELECT id FROM users ORDER BY created_at ASC LIMIT 1
        )
      `);
      console.log(`✓ Updated ${result.rowCount} user(s) to admin role`);
    } catch (error) {
      console.log('⚠ Error updating user role:', error.message);
    }
    
    // Step 4: Add check constraint
    try {
      console.log('4. Adding role check constraint...');
      await query(`
        ALTER TABLE users ADD CONSTRAINT check_user_role 
        CHECK (role IN ('admin', 'user'))
      `);
      console.log('✓ Check constraint added successfully');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠ Check constraint already exists, skipping');
      } else {
        throw error;
      }
    }
    
    // Step 5: Add comment
    try {
      console.log('5. Adding column comment...');
      await query(`
        COMMENT ON COLUMN users.role IS 'User role: admin (full access) or user (limited access)'
      `);
      console.log('✓ Column comment added successfully');
    } catch (error) {
      console.log('⚠ Error adding comment:', error.message);
    }
    
    console.log('\n✅ Migration completed successfully!');
    
    // Verify the migration
    console.log('\n📊 Verification:');
    
    const totalUsers = await query('SELECT COUNT(*) as count FROM users');
    console.log(`Total users: ${totalUsers.rows[0].count}`);
    
    const usersWithRoles = await query('SELECT COUNT(*) as count FROM users WHERE role IS NOT NULL');
    console.log(`Users with roles: ${usersWithRoles.rows[0].count}`);
    
    const adminCount = await query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
    console.log(`Admin users: ${adminCount.rows[0].count}`);
    
    const regularUserCount = await query("SELECT COUNT(*) as count FROM users WHERE role = 'user'");
    console.log(`Regular users: ${regularUserCount.rows[0].count}`);
    
    // Show user roles
    const userRoles = await query('SELECT id, email, role, created_at FROM users ORDER BY created_at ASC');
    console.log('\n👥 User Roles:');
    userRoles.rows.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} - ${user.role} (${user.created_at.toISOString().split('T')[0]})`);
    });
    
    console.log('\n🎉 Role-based access control is now active!');
    console.log('📝 Notes:');
    console.log('- Admin users: Full access to all features');
    console.log('- Regular users: Cannot upload signatures or verify documents');
    console.log('- First registered user is automatically an admin');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runSimpleMigration();
