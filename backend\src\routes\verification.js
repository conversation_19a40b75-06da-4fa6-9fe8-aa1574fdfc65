const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { requirePermission } = require('../middleware/roleAuth');
const { query } = require('../models/database');

const router = express.Router();

// Verify document by serial number (admin only)
router.post('/verify', authenticateToken, requirePermission('verify_documents'), async (req, res) => {
  try {
    const { serialNumber } = req.body;

    if (!serialNumber) {
      return res.status(400).json({
        success: false,
        message: 'الرقم التسلسلي مطلوب'
      });
    }

    // Search for document by serial number
    const result = await query(
      `SELECT d.id, d.original_filename, d.signed_filename, d.serial_number, 
              d.signed_date, d.file_size, d.status, u.email as signer_email
       FROM documents d
       JOIN users u ON d.user_id = u.id
       WHERE d.serial_number = $1`,
      [serialNumber]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على مستند بهذا الرقم التسلسلي'
      });
    }

    const document = result.rows[0];

    // Log verification attempt
    await query(
      'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
      [req.user.userId, 'DOCUMENT_VERIFIED', { 
        serialNumber, 
        documentId: document.id,
        verifiedBy: req.user.userId 
      }]
    );

    res.json({
      success: true,
      message: 'تم العثور على المستند بنجاح',
      document: {
        id: document.id,
        originalFilename: document.original_filename,
        signedFilename: document.signed_filename,
        serialNumber: document.serial_number,
        signedDate: document.signed_date,
        fileSize: document.file_size,
        status: document.status,
        signerEmail: document.signer_email
      }
    });

  } catch (error) {
    console.error('Document verification error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في التحقق من المستند'
    });
  }
});

// Get verification history (admin only)
router.get('/history', authenticateToken, requirePermission('verify_documents'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Get verification logs
    const result = await query(
      `SELECT l.id, l.action, l.details, l.created_at, u.email as verifier_email
       FROM logs l
       JOIN users u ON l.user_id = u.id
       WHERE l.action = 'DOCUMENT_VERIFIED'
       ORDER BY l.created_at DESC
       LIMIT $1 OFFSET $2`,
      [limit, offset]
    );

    // Get total count
    const countResult = await query(
      "SELECT COUNT(*) FROM logs WHERE action = 'DOCUMENT_VERIFIED'"
    );

    const total = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        verifications: result.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    });

  } catch (error) {
    console.error('Get verification history error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب سجل التحقق'
    });
  }
});

module.exports = router;
