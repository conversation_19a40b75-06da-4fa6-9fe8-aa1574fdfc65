import api from './api';

/**
 * WebAuthn Service for Frontend
 * Handles biometric authentication using WebAuthn API
 * Supports Face ID, Touch ID, Windows Hello, and other platform authenticators
 */

export interface BiometricCapabilities {
  isSupported: boolean;
  isAvailable: boolean;
  supportedAuthenticators: string[];
  platformAuthenticator: boolean;
  userVerification: boolean;
}

export interface DeviceInfo {
  platform: string;
  browser: string;
  browserVersion: string;
  osVersion: string;
  deviceModel: string;
  deviceName: string;
  authenticatorName?: string;
  capabilities?: any;
  deviceId?: string;
}

export interface RegistrationOptions {
  challenge: string;
  rp: {
    name: string;
    id: string;
  };
  user: {
    id: string;
    name: string;
    displayName: string;
  };
  pubKeyCredParams: Array<{
    alg: number;
    type: string;
  }>;
  authenticatorSelection: {
    authenticatorAttachment: string;
    userVerification: string;
    residentKey: string;
  };
  timeout: number;
  attestation: string;
  excludeCredentials?: Array<{
    id: string;
    type: string;
    transports: string[];
  }>;
}

export interface AuthenticationOptions {
  challenge: string;
  timeout: number;
  rpId: string;
  allowCredentials: Array<{
    id: string;
    type: string;
    transports: string[];
  }>;
  userVerification: string;
}

class WebAuthnService {
  private isSupported: boolean;

  constructor() {
    this.isSupported = this.checkWebAuthnSupport();
  }

  /**
   * Check if WebAuthn is supported in the current browser
   */
  checkWebAuthnSupport(): boolean {
    return !!(
      window.PublicKeyCredential &&
      navigator.credentials &&
      navigator.credentials.create &&
      navigator.credentials.get
    );
  }

  /**
   * Get detailed biometric capabilities of the current device
   */
  async getBiometricCapabilities(): Promise<BiometricCapabilities> {
    const capabilities: BiometricCapabilities = {
      isSupported: this.isSupported,
      isAvailable: false,
      supportedAuthenticators: [],
      platformAuthenticator: false,
      userVerification: false
    };

    if (!this.isSupported) {
      return capabilities;
    }

    try {
      // Check for platform authenticator availability
      const platformAuthenticatorAvailable = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      capabilities.platformAuthenticator = platformAuthenticatorAvailable;
      capabilities.isAvailable = platformAuthenticatorAvailable;

      if (platformAuthenticatorAvailable) {
        capabilities.supportedAuthenticators.push('platform');
      }

      // Check for conditional mediation support (for autofill)
      if ('isConditionalMediationAvailable' in PublicKeyCredential) {
        const conditionalAvailable = await (PublicKeyCredential as any).isConditionalMediationAvailable();
        if (conditionalAvailable) {
          capabilities.supportedAuthenticators.push('conditional');
        }
      }

      capabilities.userVerification = true;

    } catch (error) {
      console.warn('خطأ في فحص قدرات المصادقة البيومترية:', error);
    }

    return capabilities;
  }

  /**
   * Get device information for registration
   */
  getDeviceInfo(): DeviceInfo {
    const userAgent = navigator.userAgent;
    const platform = navigator.platform;
    
    // Parse browser information
    let browser = 'Unknown';
    let browserVersion = '';
    
    if (userAgent.includes('Chrome')) {
      browser = 'Chrome';
      const match = userAgent.match(/Chrome\/([0-9.]+)/);
      browserVersion = match ? match[1] : '';
    } else if (userAgent.includes('Firefox')) {
      browser = 'Firefox';
      const match = userAgent.match(/Firefox\/([0-9.]+)/);
      browserVersion = match ? match[1] : '';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      browser = 'Safari';
      const match = userAgent.match(/Version\/([0-9.]+)/);
      browserVersion = match ? match[1] : '';
    } else if (userAgent.includes('Edge')) {
      browser = 'Edge';
      const match = userAgent.match(/Edge\/([0-9.]+)/);
      browserVersion = match ? match[1] : '';
    }

    // Determine platform
    let platformName = 'Unknown';
    if (platform.includes('Win')) {
      platformName = 'Windows';
    } else if (platform.includes('Mac')) {
      platformName = 'macOS';
    } else if (platform.includes('Linux')) {
      platformName = 'Linux';
    } else if (userAgent.includes('iPhone')) {
      platformName = 'iOS';
    } else if (userAgent.includes('Android')) {
      platformName = 'Android';
    }

    return {
      platform: platformName,
      browser,
      browserVersion,
      osVersion: platform,
      deviceModel: platform,
      deviceName: `${browser} على ${platformName}`,
      capabilities: {
        webauthn: this.isSupported,
        platform: platformName,
        browser: browser
      }
    };
  }

  /**
   * Register biometric credential
   */
  async registerBiometric(): Promise<{ success: boolean; message: string; credentialId?: string }> {
    try {
      if (!this.isSupported) {
        return {
          success: false,
          message: 'المصادقة البيومترية غير مدعومة في هذا المتصفح'
        };
      }

      const capabilities = await this.getBiometricCapabilities();
      if (!capabilities.isAvailable) {
        return {
          success: false,
          message: 'المصادقة البيومترية غير متاحة على هذا الجهاز'
        };
      }

      // Get device info
      const deviceInfo = this.getDeviceInfo();

      // Request registration challenge from server
      const challengeResponse = await api.post('/biometric/register/challenge', {
        deviceInfo
      });

      if (!challengeResponse.data.success) {
        return {
          success: false,
          message: challengeResponse.data.message || 'فشل في الحصول على تحدي التسجيل'
        };
      }

      const options: RegistrationOptions = challengeResponse.data.options;
      const challengeId = challengeResponse.data.challengeId;

      // Convert challenge and user ID from base64url to ArrayBuffer
      const publicKeyCredentialCreationOptions: PublicKeyCredentialCreationOptions = {
        ...options,
        challenge: this.base64urlToArrayBuffer(options.challenge),
        user: {
          ...options.user,
          id: this.base64urlToArrayBuffer(options.user.id)
        },
        excludeCredentials: options.excludeCredentials?.map(cred => ({
          ...cred,
          id: this.base64urlToArrayBuffer(cred.id)
        }))
      };

      // Create credential using WebAuthn
      const credential = await navigator.credentials.create({
        publicKey: publicKeyCredentialCreationOptions
      }) as PublicKeyCredential;

      if (!credential) {
        return {
          success: false,
          message: 'فشل في إنشاء بيانات الاعتماد البيومترية'
        };
      }

      const response = credential.response as AuthenticatorAttestationResponse;

      // Prepare registration data
      const registrationData = {
        challengeId,
        credentialId: this.arrayBufferToBase64url(credential.rawId),
        publicKey: this.arrayBufferToBase64url(response.getPublicKey()!),
        attestationObject: this.arrayBufferToBase64url(response.attestationObject),
        clientDataJSON: this.arrayBufferToBase64url(response.clientDataJSON),
        deviceInfo: {
          ...deviceInfo,
          authenticatorName: 'Platform Authenticator'
        }
      };

      // Complete registration on server
      const completionResponse = await api.post('/biometric/register/complete', registrationData);

      return {
        success: completionResponse.data.success,
        message: completionResponse.data.message,
        credentialId: completionResponse.data.credentialId
      };

    } catch (error: any) {
      console.error('خطأ في تسجيل المصادقة البيومترية:', error);
      
      let message = 'فشل في تسجيل المصادقة البيومترية';
      
      if (error.name === 'NotAllowedError') {
        message = 'تم إلغاء العملية أو رفض الإذن';
      } else if (error.name === 'NotSupportedError') {
        message = 'المصادقة البيومترية غير مدعومة';
      } else if (error.name === 'SecurityError') {
        message = 'خطأ أمني في المصادقة البيومترية';
      } else if (error.name === 'InvalidStateError') {
        message = 'بيانات الاعتماد موجودة بالفعل';
      }

      return {
        success: false,
        message
      };
    }
  }

  /**
   * Authenticate using biometric
   */
  async authenticateBiometric(email: string): Promise<{ success: boolean; message: string; token?: string; refreshToken?: string; user?: any }> {
    try {
      if (!this.isSupported) {
        return {
          success: false,
          message: 'المصادقة البيومترية غير مدعومة في هذا المتصفح'
        };
      }

      const deviceInfo = this.getDeviceInfo();

      // Request authentication challenge from server
      const challengeResponse = await api.post('/biometric/auth/challenge', {
        email,
        deviceInfo
      });

      if (!challengeResponse.data.success) {
        return {
          success: false,
          message: challengeResponse.data.message || 'فشل في الحصول على تحدي المصادقة'
        };
      }

      const options: AuthenticationOptions = challengeResponse.data.options;
      const challengeId = challengeResponse.data.challengeId;

      // Convert challenge and credential IDs from base64url to ArrayBuffer
      const publicKeyCredentialRequestOptions: PublicKeyCredentialRequestOptions = {
        ...options,
        challenge: this.base64urlToArrayBuffer(options.challenge),
        allowCredentials: options.allowCredentials.map(cred => ({
          ...cred,
          id: this.base64urlToArrayBuffer(cred.id)
        }))
      };

      // Get assertion using WebAuthn
      const assertion = await navigator.credentials.get({
        publicKey: publicKeyCredentialRequestOptions
      }) as PublicKeyCredential;

      if (!assertion) {
        return {
          success: false,
          message: 'فشل في المصادقة البيومترية'
        };
      }

      const response = assertion.response as AuthenticatorAssertionResponse;

      // Prepare authentication data
      const authenticationData = {
        challengeId,
        credentialId: this.arrayBufferToBase64url(assertion.rawId),
        authenticatorData: this.arrayBufferToBase64url(response.authenticatorData),
        signature: this.arrayBufferToBase64url(response.signature),
        clientDataJSON: this.arrayBufferToBase64url(response.clientDataJSON),
        deviceInfo
      };

      // Complete authentication on server
      const completionResponse = await api.post('/biometric/auth/complete', authenticationData);

      return completionResponse.data;

    } catch (error: any) {
      console.error('خطأ في المصادقة البيومترية:', error);
      
      let message = 'فشل في المصادقة البيومترية';
      
      if (error.name === 'NotAllowedError') {
        message = 'تم إلغاء العملية أو رفض الإذن';
      } else if (error.name === 'NotSupportedError') {
        message = 'المصادقة البيومترية غير مدعومة';
      } else if (error.name === 'SecurityError') {
        message = 'خطأ أمني في المصادقة البيومترية';
      }

      return {
        success: false,
        message
      };
    }
  }

  /**
   * Convert ArrayBuffer to base64url string
   */
  private arrayBufferToBase64url(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Convert base64url string to ArrayBuffer
   */
  private base64urlToArrayBuffer(base64url: string): ArrayBuffer {
    const base64 = base64url
      .replace(/-/g, '+')
      .replace(/_/g, '/');
    
    const padded = base64.padEnd(base64.length + (4 - base64.length % 4) % 4, '=');
    const binary = atob(padded);
    const bytes = new Uint8Array(binary.length);
    
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    
    return bytes.buffer;
  }
}

export default new WebAuthnService();
