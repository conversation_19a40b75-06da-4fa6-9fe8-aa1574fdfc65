const axios = require('axios');

async function debugDocumentsPage() {
  console.log('🔍 Debugging Documents Page\n');

  try {
    // Test 1: Check if we can get documents list
    console.log('1. Testing documents list endpoint...');
    
    // You'll need to get a real token from the browser
    // Open browser console on http://localhost:3000/documents and run: localStorage.getItem('token')
    const token = 'YOUR_REAL_TOKEN_HERE'; // Replace with actual token from browser
    
    if (token === 'YOUR_REAL_TOKEN_HERE') {
      console.log('❌ Please update this script with a real token from the browser');
      console.log('   1. Open http://localhost:3000/documents in browser');
      console.log('   2. Open browser console (F12)');
      console.log('   3. Run: localStorage.getItem("token")');
      console.log('   4. Copy the token and replace YOUR_REAL_TOKEN_HERE in this script');
      return;
    }

    try {
      const documentsResponse = await axios.get('http://localhost:3001/api/documents', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      });
      
      console.log('✅ Documents list retrieved successfully');
      console.log(`   Found ${documentsResponse.data.documents?.length || 0} documents`);
      
      // Find a signed document to test
      const signedDocs = documentsResponse.data.documents?.filter(doc => doc.status === 'signed') || [];
      console.log(`   Found ${signedDocs.length} signed documents`);
      
      if (signedDocs.length > 0) {
        const testDoc = signedDocs[0];
        console.log(`   Testing with document: ${testDoc.id} (${testDoc.original_filename})`);
        
        // Test 2: Try to view the document
        console.log('\n2. Testing document view endpoint...');
        const viewUrl = `http://localhost:3001/api/documents/${testDoc.id}/view?token=${encodeURIComponent(token)}`;
        
        const viewResponse = await axios.get(viewUrl, {
          responseType: 'arraybuffer',
          timeout: 30000
        });
        
        console.log('✅ Document view successful');
        console.log(`   Status: ${viewResponse.status}`);
        console.log(`   Content-Type: ${viewResponse.headers['content-type']}`);
        console.log(`   Size: ${viewResponse.headers['content-length']} bytes`);
        
        // Test 3: Verify PDF format
        const buffer = Buffer.from(viewResponse.data);
        const pdfHeader = buffer.slice(0, 4).toString();
        console.log(`   PDF Header: ${pdfHeader}`);
        
        if (pdfHeader === '%PDF') {
          console.log('✅ Valid PDF confirmed');
        } else {
          console.log('❌ Invalid PDF format');
        }
        
      } else {
        console.log('⚠️ No signed documents found to test with');
      }
      
    } catch (error) {
      console.log('❌ Documents API failed:', error.message);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Data: ${error.response.data}`);
      }
    }

    // Test 4: Check frontend accessibility
    console.log('\n3. Testing frontend pages...');
    try {
      const frontendResponse = await axios.get('http://localhost:3000/documents');
      console.log('✅ Documents page accessible');
    } catch (error) {
      console.log('❌ Documents page not accessible:', error.message);
    }

    console.log('\n📋 Manual Testing Steps:');
    console.log('1. Open http://localhost:3000/documents in browser');
    console.log('2. Login if not already logged in');
    console.log('3. Look for documents with "signed" status');
    console.log('4. Click "عرض المستند" button on a signed document');
    console.log('5. Check browser console for any errors');
    console.log('6. Verify that DocumentViewer modal opens');

    console.log('\n🔧 If PDF viewer not working:');
    console.log('- Check browser console for JavaScript errors');
    console.log('- Verify token is present in localStorage');
    console.log('- Check Network tab for failed requests');
    console.log('- Ensure document status is "signed"');
    console.log('- Verify DocumentViewer component is rendering');

  } catch (error) {
    console.error('❌ Debug script failed:', error.message);
  }
}

// Instructions for getting the token
console.log('📝 To run this debug script:');
console.log('1. Open http://localhost:3000/documents in your browser');
console.log('2. Open browser console (F12)');
console.log('3. Run: localStorage.getItem("token")');
console.log('4. Copy the token value');
console.log('5. Replace YOUR_REAL_TOKEN_HERE in this script with the actual token');
console.log('6. Run: node debug-documents-page.js\n');

// Run the debug script
debugDocumentsPage().catch(console.error);
