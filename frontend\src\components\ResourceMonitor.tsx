import React, { useState, useEffect } from 'react';

interface ResourceData {
  timestamp: string;
  memory: {
    used: number;
    total: number;
    external: number;
    rss: number;
    usagePercent: number;
  };
  cpu: {
    user: number;
    system: number;
  };
  uptime: number;
  loadAverage: number[];
  recommendations: Array<{
    type: string;
    severity: 'low' | 'medium' | 'high' | 'info';
    message: string;
    action: string;
  }>;
}

interface ResourceMonitorProps {
  className?: string;
  refreshInterval?: number;
}

const ResourceMonitor: React.FC<ResourceMonitorProps> = ({ 
  className = '', 
  refreshInterval = 30000 
}) => {
  const [resourceData, setResourceData] = useState<ResourceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCleaningUp, setIsCleaningUp] = useState(false);

  const fetchResourceData = async () => {
    try {
      const response = await fetch('/api/performance/resources', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch resource data');
      }
      
      const data = await response.json();
      setResourceData(data.data);
      setError(null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const triggerCleanup = async () => {
    setIsCleaningUp(true);
    try {
      const response = await fetch('/api/performance/cleanup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to trigger cleanup');
      }
      
      // Refresh data after cleanup
      setTimeout(fetchResourceData, 2000);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsCleaningUp(false);
    }
  };

  useEffect(() => {
    fetchResourceData();
    
    const interval = setInterval(fetchResourceData, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const formatBytes = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 80) return 'text-red-600 bg-red-100';
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-100 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'info': return 'text-gray-600 bg-gray-100 border-gray-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">خطأ في تحميل بيانات الموارد</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchResourceData}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  if (!resourceData) return null;

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">مراقب الموارد</h2>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={triggerCleanup}
            disabled={isCleaningUp}
            className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 disabled:opacity-50 text-sm"
          >
            {isCleaningUp ? 'جاري التنظيف...' : 'تنظيف الموارد'}
          </button>
          <button
            onClick={fetchResourceData}
            className="bg-gray-600 text-white px-3 py-1 rounded-md hover:bg-gray-700 text-sm"
          >
            تحديث
          </button>
        </div>
      </div>

      {/* Resource Usage */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">استخدام الذاكرة</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">الاستخدام الحالي</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUsageColor(resourceData.memory.usagePercent)}`}>
                {resourceData.memory.usagePercent}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  resourceData.memory.usagePercent >= 80 ? 'bg-red-500' :
                  resourceData.memory.usagePercent >= 60 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${resourceData.memory.usagePercent}%` }}
              ></div>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-600">المستخدم:</span>
                <span className="font-medium mr-1">{formatBytes(resourceData.memory.used)}</span>
              </div>
              <div>
                <span className="text-gray-600">الإجمالي:</span>
                <span className="font-medium mr-1">{formatBytes(resourceData.memory.total)}</span>
              </div>
              <div>
                <span className="text-gray-600">الخارجي:</span>
                <span className="font-medium mr-1">{formatBytes(resourceData.memory.external)}</span>
              </div>
              <div>
                <span className="text-gray-600">RSS:</span>
                <span className="font-medium mr-1">{formatBytes(resourceData.memory.rss)}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">معلومات النظام</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">وقت التشغيل</span>
              <span className="font-medium text-blue-600">{formatUptime(resourceData.uptime)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">معالج المستخدم</span>
              <span className="font-medium">{(resourceData.cpu.user / 1000).toFixed(2)}ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">معالج النظام</span>
              <span className="font-medium">{(resourceData.cpu.system / 1000).toFixed(2)}ms</span>
            </div>
            {resourceData.loadAverage.length > 0 && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">متوسط الحمولة</span>
                <span className="font-medium">{resourceData.loadAverage[0].toFixed(2)}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Recommendations */}
      {resourceData.recommendations.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">التوصيات</h3>
          <div className="space-y-3">
            {resourceData.recommendations.map((rec, index) => (
              <div key={index} className={`p-3 rounded-lg border ${getSeverityColor(rec.severity)}`}>
                <div className="flex items-start">
                  <div className="flex-shrink-0 ml-3">
                    {rec.severity === 'high' && (
                      <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    )}
                    {rec.severity === 'medium' && (
                      <svg className="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    )}
                    {rec.severity === 'info' && (
                      <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{rec.message}</h4>
                    <p className="text-sm mt-1">{rec.action}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mt-4 text-xs text-gray-500 text-center">
        آخر تحديث: {new Date(resourceData.timestamp).toLocaleString('ar-SA')}
      </div>
    </div>
  );
};

export default ResourceMonitor;
