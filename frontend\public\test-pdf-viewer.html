<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Viewer Test</title>
    <style>
        body {
            font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 5px;
        }
        .loading {
            color: blue;
            background: #e6f3ff;
            padding: 10px;
            border-radius: 5px;
        }
        #pdfContainer {
            border: 1px solid #ccc;
            min-height: 400px;
            background: #f9f9f9;
            padding: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار عارض PDF</h1>
        
        <div class="test-section">
            <h2>1. اختبار الاتصال بالخادم</h2>
            <button onclick="testServerConnection()">اختبار الاتصال</button>
            <div id="serverTest"></div>
        </div>

        <div class="test-section">
            <h2>2. اختبار تحميل PDF.js</h2>
            <button onclick="testPDFJS()">اختبار PDF.js</button>
            <div id="pdfjsTest"></div>
        </div>

        <div class="test-section">
            <h2>3. اختبار تحميل المستند</h2>
            <label>معرف المستند:</label>
            <input type="text" id="documentId" value="5dfdd344-43cc-4060-911c-2579571972d1" />
            <label>رمز المصادقة:</label>
            <input type="text" id="authToken" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIwZjFlYmIzZS03NWY2LTQ0ZGQtOGFiYS1iZjJlM2U4NTdhMDQiLCJpYXQiOjE3NTI2ODQ4MjcsImV4cCI6MTc1Mjc3MTIyN30.fHhH70Nf1L11XiXq6EvHzlOg71x2B-qFkmQL9PpZVS4" />
            <button onclick="testDocumentLoad()">اختبار تحميل المستند</button>
            <div id="documentTest"></div>
        </div>

        <div class="test-section">
            <h2>4. عارض PDF</h2>
            <button onclick="loadPDF()">تحميل PDF</button>
            <div id="pdfContainer"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // Set PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'loading';
            element.innerHTML = `<div class="${className}">${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function testServerConnection() {
            log('serverTest', 'جاري اختبار الاتصال بالخادم...', 'loading');
            
            try {
                const response = await fetch('http://localhost:3001/health');
                if (response.ok) {
                    log('serverTest', 'الخادم يعمل بشكل صحيح ✓', 'success');
                } else {
                    log('serverTest', `خطأ في الخادم: ${response.status}`, 'error');
                }
            } catch (error) {
                log('serverTest', `فشل الاتصال بالخادم: ${error.message}`, 'error');
            }
        }

        function testPDFJS() {
            log('pdfjsTest', 'جاري اختبار PDF.js...', 'loading');
            
            if (typeof pdfjsLib !== 'undefined') {
                log('pdfjsTest', `PDF.js محمل بنجاح - الإصدار: ${pdfjsLib.version} ✓`, 'success');
            } else {
                log('pdfjsTest', 'فشل في تحميل PDF.js', 'error');
            }
        }

        async function testDocumentLoad() {
            const documentId = document.getElementById('documentId').value;
            const authToken = document.getElementById('authToken').value;
            
            if (!documentId || !authToken) {
                log('documentTest', 'يرجى إدخال معرف المستند ورمز المصادقة', 'error');
                return;
            }

            log('documentTest', 'جاري اختبار تحميل المستند...', 'loading');
            
            try {
                const url = `http://localhost:3001/api/documents/${documentId}/view?token=${encodeURIComponent(authToken)}`;
                console.log('Testing URL:', url);
                
                const response = await fetch(url);
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    const contentLength = response.headers.get('content-length');
                    
                    if (contentType === 'application/pdf') {
                        log('documentTest', `المستند محمل بنجاح ✓<br>نوع المحتوى: ${contentType}<br>حجم الملف: ${contentLength} بايت`, 'success');
                    } else {
                        log('documentTest', `نوع محتوى غير صحيح: ${contentType}`, 'error');
                    }
                } else {
                    const errorText = await response.text();
                    log('documentTest', `فشل تحميل المستند: ${response.status}<br>${errorText}`, 'error');
                }
            } catch (error) {
                log('documentTest', `خطأ في تحميل المستند: ${error.message}`, 'error');
            }
        }

        async function loadPDF() {
            const documentId = document.getElementById('documentId').value;
            const authToken = document.getElementById('authToken').value;

            if (!documentId || !authToken) {
                log('pdfContainer', 'يرجى إدخال معرف المستند ورمز المصادقة أولاً', 'error');
                return;
            }

            const container = document.getElementById('pdfContainer');
            container.innerHTML = '<div class="loading">جاري تحميل PDF...</div>';

            try {
                const url = `http://localhost:3001/api/documents/${documentId}/view?token=${encodeURIComponent(authToken)}`;
                console.log('Loading PDF from:', url);

                // First test if we can fetch the PDF data directly
                console.log('Testing direct fetch...');
                const fetchResponse = await fetch(url);
                console.log('Fetch response status:', fetchResponse.status);
                console.log('Fetch response headers:', [...fetchResponse.headers.entries()]);

                if (!fetchResponse.ok) {
                    throw new Error(`HTTP ${fetchResponse.status}: ${await fetchResponse.text()}`);
                }

                const arrayBuffer = await fetchResponse.arrayBuffer();
                console.log('PDF data size:', arrayBuffer.byteLength);

                // Check if it's actually a PDF
                const uint8Array = new Uint8Array(arrayBuffer);
                const pdfHeader = String.fromCharCode(...uint8Array.slice(0, 4));
                console.log('PDF header:', pdfHeader);

                if (pdfHeader !== '%PDF') {
                    throw new Error('Response is not a valid PDF file');
                }

                container.innerHTML = '<div class="loading">PDF data fetched successfully, loading with PDF.js...</div>';

                // Now try to load with PDF.js
                const loadingTask = pdfjsLib.getDocument({
                    data: arrayBuffer,
                    verbosity: pdfjsLib.VerbosityLevel.INFOS
                });

                loadingTask.onProgress = function(progress) {
                    console.log('PDF.js loading progress:', progress);
                    if (progress.total > 0) {
                        const percent = Math.round((progress.loaded / progress.total) * 100);
                        container.innerHTML = `<div class="loading">جاري معالجة PDF... ${percent}%</div>`;
                    }
                };

                const pdf = await loadingTask.promise;
                console.log('PDF loaded successfully with PDF.js:', pdf);

                container.innerHTML = `<div class="success">PDF محمل بنجاح! عدد الصفحات: ${pdf.numPages}</div>`;

                // Render first page
                const page = await pdf.getPage(1);
                const scale = 1.0;
                const viewport = page.getViewport({ scale: scale });

                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                canvas.style.border = '1px solid #ccc';
                canvas.style.maxWidth = '100%';

                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };

                await page.render(renderContext).promise;

                container.innerHTML = '<div class="success">PDF محمل ومعروض بنجاح!</div>';
                container.appendChild(canvas);

            } catch (error) {
                console.error('PDF loading error:', error);
                console.error('Error stack:', error.stack);
                container.innerHTML = `<div class="error">فشل في تحميل PDF:<br><strong>Error:</strong> ${error.message}<br><strong>Type:</strong> ${error.name}</div>`;
            }
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            testServerConnection();
            testPDFJS();
        };
    </script>
</body>
</html>
