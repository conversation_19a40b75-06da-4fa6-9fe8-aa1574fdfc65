const crypto = require('crypto');
const { query } = require('../models/database');

/**
 * WebAuthn Security Service
 * Handles cryptographic operations for biometric authentication
 * Implements WebAuthn specification for secure authentication
 */

class WebAuthnService {
  constructor() {
    this.rpId = process.env.WEBAUTHN_RP_ID || 'localhost';
    this.rpName = process.env.APP_NAME || 'نظام التوقيع الإلكتروني';
    this.origin = process.env.WEBAUTHN_ORIGIN || 'http://localhost:3000';
    this.challengeTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Generate cryptographically secure challenge
   * @returns {string} Base64URL encoded challenge
   */
  generateChallenge() {
    return crypto.randomBytes(32).toString('base64url');
  }

  /**
   * Create registration options for WebAuthn
   * @param {Object} user - User object with id and email
   * @param {Array} excludeCredentials - Existing credentials to exclude
   * @returns {Object} WebAuthn registration options
   */
  createRegistrationOptions(user, excludeCredentials = []) {
    const challenge = this.generateChallenge();
    
    return {
      challenge,
      rp: {
        name: this.rpName,
        id: this.rpId
      },
      user: {
        id: Buffer.from(user.id).toString('base64url'),
        name: user.email,
        displayName: user.email
      },
      pubKeyCredParams: [
        { alg: -7, type: 'public-key' },   // ES256 (ECDSA w/ SHA-256)
        { alg: -257, type: 'public-key' }, // RS256 (RSASSA-PKCS1-v1_5 w/ SHA-256)
        { alg: -37, type: 'public-key' },  // PS256 (RSASSA-PSS w/ SHA-256)
        { alg: -35, type: 'public-key' },  // ES384 (ECDSA w/ SHA-384)
        { alg: -36, type: 'public-key' }   // ES512 (ECDSA w/ SHA-512)
      ],
      authenticatorSelection: {
        authenticatorAttachment: 'platform', // Prefer platform authenticators
        userVerification: 'required',
        residentKey: 'preferred',
        requireResidentKey: false
      },
      timeout: 60000, // 60 seconds
      attestation: 'direct',
      excludeCredentials: excludeCredentials.map(cred => ({
        id: cred.credential_id,
        type: 'public-key',
        transports: cred.transport_methods || ['internal']
      }))
    };
  }

  /**
   * Create authentication options for WebAuthn
   * @param {Array} allowCredentials - Allowed credentials for authentication
   * @returns {Object} WebAuthn authentication options
   */
  createAuthenticationOptions(allowCredentials = []) {
    const challenge = this.generateChallenge();
    
    return {
      challenge,
      timeout: 60000,
      rpId: this.rpId,
      allowCredentials: allowCredentials.map(cred => ({
        id: cred.id || cred.credential_id,
        type: 'public-key',
        transports: cred.transports || cred.transport_methods || ['internal']
      })),
      userVerification: 'required'
    };
  }

  /**
   * Verify client data JSON
   * @param {string} clientDataJSON - Base64URL encoded client data
   * @param {string} expectedChallenge - Expected challenge value
   * @param {string} expectedOrigin - Expected origin
   * @param {string} expectedType - Expected type ('webauthn.create' or 'webauthn.get')
   * @returns {Object} Parsed and verified client data
   */
  verifyClientData(clientDataJSON, expectedChallenge, expectedOrigin, expectedType) {
    try {
      const clientDataBuffer = Buffer.from(clientDataJSON, 'base64url');
      const clientData = JSON.parse(clientDataBuffer.toString());

      // Verify challenge
      if (clientData.challenge !== expectedChallenge) {
        throw new Error('Challenge mismatch');
      }

      // Verify origin
      if (clientData.origin !== expectedOrigin) {
        throw new Error('Origin mismatch');
      }

      // Verify type
      if (clientData.type !== expectedType) {
        throw new Error('Type mismatch');
      }

      return {
        valid: true,
        clientData,
        clientDataHash: crypto.createHash('sha256').update(clientDataBuffer).digest()
      };
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Parse authenticator data
   * @param {string} authenticatorData - Base64URL encoded authenticator data
   * @returns {Object} Parsed authenticator data
   */
  parseAuthenticatorData(authenticatorData) {
    try {
      const buffer = Buffer.from(authenticatorData, 'base64url');
      
      if (buffer.length < 37) {
        throw new Error('Authenticator data too short');
      }

      const rpIdHash = buffer.slice(0, 32);
      const flags = buffer.readUInt8(32);
      const counter = buffer.readUInt32BE(33);

      // Parse flags
      const userPresent = !!(flags & 0x01);
      const userVerified = !!(flags & 0x04);
      const attestedCredentialData = !!(flags & 0x40);
      const extensionDataIncluded = !!(flags & 0x80);

      let credentialData = null;
      let extensions = null;
      let offset = 37;

      // Parse attested credential data if present
      if (attestedCredentialData && buffer.length > offset + 16) {
        const aaguid = buffer.slice(offset, offset + 16);
        offset += 16;

        const credentialIdLength = buffer.readUInt16BE(offset);
        offset += 2;

        if (buffer.length >= offset + credentialIdLength) {
          const credentialId = buffer.slice(offset, offset + credentialIdLength);
          offset += credentialIdLength;

          // The rest would be the credential public key in CBOR format
          // For now, we'll store the remaining data as the public key
          const credentialPublicKey = buffer.slice(offset);

          credentialData = {
            aaguid: aaguid.toString('hex'),
            credentialId: credentialId.toString('base64url'),
            credentialPublicKey: credentialPublicKey.toString('base64url')
          };
        }
      }

      return {
        rpIdHash: rpIdHash.toString('hex'),
        userPresent,
        userVerified,
        counter,
        attestedCredentialData,
        extensionDataIncluded,
        credentialData,
        extensions
      };
    } catch (error) {
      throw new Error(`Failed to parse authenticator data: ${error.message}`);
    }
  }

  /**
   * Verify RP ID hash
   * @param {string} rpIdHash - RP ID hash from authenticator data
   * @param {string} rpId - Expected RP ID
   * @returns {boolean} True if hash matches
   */
  verifyRpIdHash(rpIdHash, rpId = this.rpId) {
    const expectedHash = crypto.createHash('sha256').update(rpId).digest('hex');
    return rpIdHash === expectedHash;
  }

  /**
   * Verify signature counter (prevents replay attacks)
   * @param {number} newCounter - New counter value
   * @param {number} storedCounter - Stored counter value
   * @returns {boolean} True if counter is valid
   */
  verifyCounter(newCounter, storedCounter) {
    // Counter must be greater than stored value (prevents replay attacks)
    // Counter of 0 is allowed for some authenticators
    return newCounter === 0 || newCounter > storedCounter;
  }

  /**
   * Calculate risk score based on authentication context
   * @param {Object} context - Authentication context
   * @returns {number} Risk score between 0.00 and 1.00
   */
  calculateRiskScore(context) {
    let riskScore = 0.0;

    // Check for suspicious IP patterns
    if (context.ipAddress) {
      // Add risk for VPN/proxy detection (simplified)
      if (this.isVpnOrProxy(context.ipAddress)) {
        riskScore += 0.2;
      }
    }

    // Check device consistency
    if (context.deviceInfo && context.previousDeviceInfo) {
      if (context.deviceInfo.platform !== context.previousDeviceInfo.platform) {
        riskScore += 0.3;
      }
    }

    // Check time-based patterns
    if (context.lastAuthTime) {
      const timeDiff = Date.now() - new Date(context.lastAuthTime).getTime();
      if (timeDiff < 60000) { // Less than 1 minute
        riskScore += 0.4;
      }
    }

    // Check geolocation if available
    if (context.geolocation && context.previousGeolocation) {
      const distance = this.calculateDistance(
        context.geolocation,
        context.previousGeolocation
      );
      if (distance > 1000) { // More than 1000km
        riskScore += 0.3;
      }
    }

    return Math.min(riskScore, 1.0);
  }

  /**
   * Simple VPN/Proxy detection (placeholder)
   * @param {string} ipAddress - IP address to check
   * @returns {boolean} True if likely VPN/proxy
   */
  isVpnOrProxy(ipAddress) {
    // This is a simplified implementation
    // In production, use a proper VPN/proxy detection service
    const vpnRanges = [
      '10.0.0.0/8',
      '**********/12',
      '***********/16'
    ];
    
    // Check if IP is in private ranges (simplified)
    return vpnRanges.some(range => {
      // Simplified check - in production use proper CIDR matching
      return ipAddress.startsWith(range.split('/')[0].split('.').slice(0, 2).join('.'));
    });
  }

  /**
   * Calculate distance between two geographic points
   * @param {Object} point1 - {lat, lng}
   * @param {Object} point2 - {lat, lng}
   * @returns {number} Distance in kilometers
   */
  calculateDistance(point1, point2) {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRadians(point2.lat - point1.lat);
    const dLng = this.toRadians(point2.lng - point1.lng);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convert degrees to radians
   * @param {number} degrees - Degrees
   * @returns {number} Radians
   */
  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }
}

module.exports = new WebAuthnService();
