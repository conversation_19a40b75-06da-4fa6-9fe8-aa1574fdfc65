const crypto = require('crypto');
const { query } = require('../models/database');
const { generateToken, generateRefreshToken } = require('../middleware/auth');
const { validateUUID, sanitizeInput } = require('../utils/validation');

/**
 * Biometric Authentication Controller
 * Handles WebAuthn-based biometric authentication operations
 * Supports Face ID, Touch ID, Windows Hello, and other platform authenticators
 */

// Generate WebAuthn registration challenge
const generateRegistrationChallenge = async (req, res) => {
  try {
    const { userId } = req.user;
    const { deviceInfo } = req.body;

    // Validate user exists and can register biometric
    const userResult = await query(
      'SELECT id, email, biometric_enabled FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // Generate cryptographic challenge
    const challenge = crypto.randomBytes(32).toString('base64url');
    const challengeExpiry = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    // Store challenge temporarily (in production, use Redis or similar)
    await query(
      `INSERT INTO biometric_auth_logs (user_id, event_type, event_status, challenge, device_info, timestamp)
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [userId, 'challenge_generated', 'pending', challenge, deviceInfo || {}, new Date()]
    );

    // WebAuthn registration options
    const registrationOptions = {
      challenge: challenge,
      rp: {
        name: process.env.APP_NAME || 'نظام التوقيع الإلكتروني',
        id: process.env.WEBAUTHN_RP_ID || 'localhost'
      },
      user: {
        id: Buffer.from(userId).toString('base64url'),
        name: userResult.rows[0].email,
        displayName: userResult.rows[0].email
      },
      pubKeyCredParams: [
        { alg: -7, type: 'public-key' }, // ES256
        { alg: -257, type: 'public-key' } // RS256
      ],
      authenticatorSelection: {
        authenticatorAttachment: 'platform', // Prefer platform authenticators (Face ID, Touch ID)
        userVerification: 'required',
        residentKey: 'preferred'
      },
      timeout: 60000, // 60 seconds
      attestation: 'direct'
    };

    // Get existing credentials to exclude
    const existingCreds = await query(
      'SELECT credential_id FROM biometric_credentials WHERE user_id = $1 AND is_active = true',
      [userId]
    );

    if (existingCreds.rows.length > 0) {
      registrationOptions.excludeCredentials = existingCreds.rows.map(cred => ({
        id: cred.credential_id,
        type: 'public-key',
        transports: ['internal']
      }));
    }

    res.json({
      success: true,
      message: 'تم إنشاء تحدي التسجيل بنجاح',
      options: registrationOptions,
      challengeId: challenge
    });

  } catch (error) {
    console.error('خطأ في إنشاء تحدي التسجيل:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في إنشاء تحدي التسجيل'
    });
  }
};

// Complete biometric registration
const completeRegistration = async (req, res) => {
  try {
    const { userId } = req.user;
    const { 
      challengeId, 
      credentialId, 
      publicKey, 
      attestationObject, 
      clientDataJSON,
      deviceInfo 
    } = req.body;

    // Validate required fields
    if (!challengeId || !credentialId || !publicKey || !attestationObject || !clientDataJSON) {
      return res.status(400).json({
        success: false,
        message: 'بيانات التسجيل غير مكتملة'
      });
    }

    // Verify challenge exists and is not expired
    const challengeResult = await query(
      `SELECT id FROM biometric_auth_logs 
       WHERE user_id = $1 AND challenge = $2 AND event_type = 'challenge_generated' 
       AND timestamp > NOW() - INTERVAL '5 minutes'`,
      [userId, challengeId]
    );

    if (challengeResult.rows.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'التحدي غير صالح أو منتهي الصلاحية'
      });
    }

    // Parse client data to verify challenge and origin
    const clientData = JSON.parse(Buffer.from(clientDataJSON, 'base64url').toString());
    
    if (clientData.challenge !== challengeId) {
      return res.status(400).json({
        success: false,
        message: 'التحدي غير متطابق'
      });
    }

    // Verify origin (in production, check against allowed origins)
    const allowedOrigins = [
      process.env.FRONTEND_URL || 'http://localhost:3000',
      process.env.WEBAUTHN_ORIGIN || 'http://localhost:3000'
    ];

    if (!allowedOrigins.includes(clientData.origin)) {
      return res.status(400).json({
        success: false,
        message: 'المصدر غير مصرح به'
      });
    }

    // Store the credential
    const credentialResult = await query(
      `INSERT INTO biometric_credentials (
        user_id, credential_id, public_key, device_type, 
        authenticator_name, attestation_statement, transport_methods
      ) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`,
      [
        userId,
        credentialId,
        publicKey,
        'platform',
        deviceInfo?.authenticatorName || 'Platform Authenticator',
        { attestationObject, clientDataJSON },
        ['internal']
      ]
    );

    // Update user biometric status
    await query(
      `UPDATE users SET 
        biometric_enabled = true, 
        biometric_enrolled_at = NOW(),
        biometric_consent_given = true,
        biometric_consent_date = NOW(),
        preferred_auth_method = CASE 
          WHEN preferred_auth_method = 'password' THEN 'both'
          ELSE preferred_auth_method
        END
       WHERE id = $1`,
      [userId]
    );

    // Register device if provided
    if (deviceInfo) {
      await query(
        `SELECT register_device($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        [
          userId,
          deviceInfo.deviceId || crypto.randomUUID(),
          deviceInfo.deviceName || 'Unknown Device',
          deviceInfo.platform || 'unknown',
          deviceInfo.browser,
          deviceInfo.browserVersion,
          deviceInfo.osVersion,
          deviceInfo.deviceModel,
          deviceInfo.capabilities || {},
          req.ip
        ]
      );
    }

    // Log successful registration
    await query(
      `SELECT log_biometric_event($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [
        userId,
        credentialId,
        req.sessionID,
        'registration',
        'success',
        challengeId,
        req.ip,
        req.get('User-Agent'),
        deviceInfo || {}
      ]
    );

    res.json({
      success: true,
      message: 'تم تسجيل المصادقة البيومترية بنجاح',
      credentialId: credentialResult.rows[0].id
    });

  } catch (error) {
    console.error('خطأ في إكمال التسجيل البيومتري:', error);
    
    // Log failed registration
    if (req.user?.userId) {
      await query(
        `SELECT log_biometric_event($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
        [
          req.user.userId,
          req.body.credentialId || null,
          req.sessionID,
          'registration',
          'failure',
          req.body.challengeId || null,
          req.ip,
          req.get('User-Agent'),
          req.body.deviceInfo || {},
          'REGISTRATION_ERROR',
          error.message
        ]
      );
    }

    res.status(500).json({
      success: false,
      message: 'فشل في تسجيل المصادقة البيومترية'
    });
  }
};

// Generate WebAuthn authentication challenge
const generateAuthenticationChallenge = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني مطلوب'
      });
    }

    // Find user and their active credentials
    const userResult = await query(
      `SELECT u.id, u.email, u.biometric_enabled, u.biometric_locked_until,
              array_agg(
                jsonb_build_object(
                  'id', bc.credential_id,
                  'type', 'public-key',
                  'transports', bc.transport_methods
                )
              ) FILTER (WHERE bc.is_active = true) as credentials
       FROM users u
       LEFT JOIN biometric_credentials bc ON u.id = bc.user_id AND bc.is_active = true
       WHERE u.email = $1 AND u.biometric_enabled = true
       GROUP BY u.id, u.email, u.biometric_enabled, u.biometric_locked_until`,
      [email.toLowerCase()]
    );

    if (userResult.rows.length === 0 || !userResult.rows[0].credentials) {
      return res.status(404).json({
        success: false,
        message: 'لا توجد بيانات مصادقة بيومترية لهذا المستخدم'
      });
    }

    const user = userResult.rows[0];

    // Check if user is temporarily locked
    if (user.biometric_locked_until && new Date() < user.biometric_locked_until) {
      return res.status(423).json({
        success: false,
        message: 'المصادقة البيومترية مؤقتة مؤقتاً. حاول مرة أخرى لاحقاً'
      });
    }

    // Generate challenge
    const challenge = crypto.randomBytes(32).toString('base64url');

    // Store challenge
    await query(
      `INSERT INTO biometric_auth_logs (user_id, event_type, event_status, challenge, timestamp)
       VALUES ($1, $2, $3, $4, $5)`,
      [user.id, 'challenge_generated', 'pending', challenge, new Date()]
    );

    // WebAuthn authentication options
    const authenticationOptions = {
      challenge: challenge,
      timeout: 60000,
      rpId: process.env.WEBAUTHN_RP_ID || 'localhost',
      allowCredentials: user.credentials,
      userVerification: 'required'
    };

    res.json({
      success: true,
      message: 'تم إنشاء تحدي المصادقة بنجاح',
      options: authenticationOptions,
      challengeId: challenge
    });

  } catch (error) {
    console.error('خطأ في إنشاء تحدي المصادقة:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في إنشاء تحدي المصادقة'
    });
  }
};

// Complete biometric authentication
const completeAuthentication = async (req, res) => {
  try {
    const {
      challengeId,
      credentialId,
      authenticatorData,
      signature,
      clientDataJSON,
      deviceInfo
    } = req.body;

    // Validate required fields
    if (!challengeId || !credentialId || !authenticatorData || !signature || !clientDataJSON) {
      return res.status(400).json({
        success: false,
        message: 'بيانات المصادقة غير مكتملة'
      });
    }

    // Parse client data
    const clientData = JSON.parse(Buffer.from(clientDataJSON, 'base64url').toString());

    if (clientData.challenge !== challengeId) {
      return res.status(400).json({
        success: false,
        message: 'التحدي غير متطابق'
      });
    }

    // Find credential and user
    const credentialResult = await query(
      `SELECT bc.*, u.id as user_id, u.email, u.role, u.failed_biometric_attempts
       FROM biometric_credentials bc
       JOIN users u ON bc.user_id = u.id
       WHERE bc.credential_id = $1 AND bc.is_active = true`,
      [credentialId]
    );

    if (credentialResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'بيانات الاعتماد غير موجودة'
      });
    }

    const credential = credentialResult.rows[0];

    // Verify signature counter (prevents replay attacks)
    const authData = Buffer.from(authenticatorData, 'base64url');
    const counter = authData.readUInt32BE(33); // Counter is at bytes 33-36

    if (counter <= credential.counter) {
      // Log potential replay attack
      await query(
        `SELECT log_biometric_event($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
        [
          credential.user_id,
          credentialId,
          req.sessionID,
          'authentication',
          'failure',
          challengeId,
          req.ip,
          req.get('User-Agent'),
          deviceInfo || {},
          'REPLAY_ATTACK',
          'Counter value indicates potential replay attack'
        ]
      );

      return res.status(400).json({
        success: false,
        message: 'فشل في التحقق من المصادقة'
      });
    }

    // In a real implementation, you would verify the signature here
    // This requires implementing the WebAuthn signature verification algorithm
    // For now, we'll assume the signature is valid if we reach this point

    // Update credential counter
    const counterUpdated = await query(
      'SELECT update_credential_counter($1, $2)',
      [credentialId, counter]
    );

    if (!counterUpdated.rows[0].update_credential_counter) {
      return res.status(400).json({
        success: false,
        message: 'فشل في تحديث عداد المصادقة'
      });
    }

    // Reset failed attempts
    await query(
      'UPDATE users SET failed_biometric_attempts = 0, biometric_locked_until = NULL WHERE id = $1',
      [credential.user_id]
    );

    // Generate JWT tokens with biometric auth method
    const token = generateToken(credential.user_id, 'biometric');
    const refreshToken = generateRefreshToken(credential.user_id, 'biometric');

    // Store refresh token
    await query(
      'UPDATE users SET refresh_token = $1, refresh_token_expires = $2 WHERE id = $3',
      [refreshToken, new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), credential.user_id]
    );

    // Log successful authentication
    await query(
      `SELECT log_biometric_event($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [
        credential.user_id,
        credentialId,
        req.sessionID,
        'authentication',
        'success',
        challengeId,
        req.ip,
        req.get('User-Agent'),
        deviceInfo || {}
      ]
    );

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      token,
      refreshToken,
      user: {
        id: credential.user_id,
        email: credential.email,
        role: credential.role,
        language: 'ar',
        textDirection: 'rtl',
        authMethod: 'biometric'
      }
    });

  } catch (error) {
    console.error('خطأ في إكمال المصادقة البيومترية:', error);

    // Increment failed attempts
    if (req.body.credentialId) {
      const credResult = await query(
        'SELECT user_id FROM biometric_credentials WHERE credential_id = $1',
        [req.body.credentialId]
      );

      if (credResult.rows.length > 0) {
        await query(
          `UPDATE users SET
            failed_biometric_attempts = failed_biometric_attempts + 1,
            biometric_locked_until = CASE
              WHEN failed_biometric_attempts >= 4 THEN NOW() + INTERVAL '15 minutes'
              ELSE biometric_locked_until
            END
           WHERE id = $1`,
          [credResult.rows[0].user_id]
        );

        // Log failed authentication
        await query(
          `SELECT log_biometric_event($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
          [
            credResult.rows[0].user_id,
            req.body.credentialId,
            req.sessionID,
            'authentication',
            'failure',
            req.body.challengeId || null,
            req.ip,
            req.get('User-Agent'),
            req.body.deviceInfo || {},
            'AUTH_ERROR',
            error.message
          ]
        );
      }
    }

    res.status(500).json({
      success: false,
      message: 'فشل في المصادقة البيومترية'
    });
  }
};

// Get user's biometric credentials
const getBiometricCredentials = async (req, res) => {
  try {
    const { userId } = req.user;

    const credentialsResult = await query(
      `SELECT bc.id, bc.credential_id, bc.device_type, bc.authenticator_name,
              bc.last_used_at, bc.created_at, bc.transport_methods,
              rd.device_name, rd.platform, rd.trust_level
       FROM biometric_credentials bc
       LEFT JOIN registered_devices rd ON bc.user_id = rd.user_id
       WHERE bc.user_id = $1 AND bc.is_active = true
       ORDER BY bc.last_used_at DESC NULLS LAST, bc.created_at DESC`,
      [userId]
    );

    const userBiometricStatus = await query(
      `SELECT biometric_enabled, biometric_enrolled_at, preferred_auth_method,
              failed_biometric_attempts, biometric_locked_until
       FROM users WHERE id = $1`,
      [userId]
    );

    res.json({
      success: true,
      biometricStatus: userBiometricStatus.rows[0] || {},
      credentials: credentialsResult.rows,
      message: 'تم جلب بيانات المصادقة البيومترية بنجاح'
    });

  } catch (error) {
    console.error('خطأ في جلب بيانات المصادقة البيومترية:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب بيانات المصادقة البيومترية'
    });
  }
};

// Remove biometric credential
const removeBiometricCredential = async (req, res) => {
  try {
    const { userId } = req.user;
    const { credentialId } = req.params;

    if (!credentialId || !validateUUID(credentialId)) {
      return res.status(400).json({
        success: false,
        message: 'معرف بيانات الاعتماد غير صالح'
      });
    }

    // Verify credential belongs to user
    const credentialResult = await query(
      'SELECT credential_id FROM biometric_credentials WHERE id = $1 AND user_id = $2 AND is_active = true',
      [credentialId, userId]
    );

    if (credentialResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'بيانات الاعتماد غير موجودة'
      });
    }

    // Deactivate credential
    const deactivated = await query(
      'SELECT deactivate_biometric_credential($1, $2, $3)',
      [userId, credentialResult.rows[0].credential_id, 'user_requested']
    );

    if (!deactivated.rows[0].deactivate_biometric_credential) {
      return res.status(500).json({
        success: false,
        message: 'فشل في إزالة بيانات الاعتماد'
      });
    }

    // Check if this was the last credential
    const remainingCreds = await query(
      'SELECT COUNT(*) as count FROM biometric_credentials WHERE user_id = $1 AND is_active = true',
      [userId]
    );

    // If no more credentials, disable biometric authentication
    if (remainingCreds.rows[0].count === '0') {
      await query(
        `UPDATE users SET
          biometric_enabled = false,
          preferred_auth_method = CASE
            WHEN preferred_auth_method = 'biometric' THEN 'password'
            WHEN preferred_auth_method = 'both' THEN 'password'
            ELSE preferred_auth_method
          END
         WHERE id = $1`,
        [userId]
      );
    }

    res.json({
      success: true,
      message: 'تم حذف بيانات المصادقة البيومترية بنجاح'
    });

  } catch (error) {
    console.error('خطأ في حذف بيانات المصادقة البيومترية:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في حذف بيانات المصادقة البيومترية'
    });
  }
};

// Update biometric preferences
const updateBiometricPreferences = async (req, res) => {
  try {
    const { userId } = req.user;
    const { preferredAuthMethod, notificationPreferences } = req.body;

    // Validate preferred auth method
    const validAuthMethods = ['password', 'biometric', 'both'];
    if (preferredAuthMethod && !validAuthMethods.includes(preferredAuthMethod)) {
      return res.status(400).json({
        success: false,
        message: 'طريقة المصادقة المفضلة غير صالحة'
      });
    }

    // Check if user has biometric credentials for biometric preferences
    if (preferredAuthMethod === 'biometric' || preferredAuthMethod === 'both') {
      const hasCredentials = await query(
        'SELECT has_active_biometric_credentials($1)',
        [userId]
      );

      if (!hasCredentials.rows[0].has_active_biometric_credentials) {
        return res.status(400).json({
          success: false,
          message: 'يجب تسجيل المصادقة البيومترية أولاً'
        });
      }
    }

    // Update preferences
    const updateFields = [];
    const updateValues = [];
    let paramCount = 1;

    if (preferredAuthMethod) {
      updateFields.push(`preferred_auth_method = $${paramCount++}`);
      updateValues.push(preferredAuthMethod);
    }

    if (notificationPreferences) {
      updateFields.push(`notification_preferences = $${paramCount++}`);
      updateValues.push(JSON.stringify(notificationPreferences));
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'لا توجد تفضيلات للتحديث'
      });
    }

    updateValues.push(userId);

    await query(
      `UPDATE users SET ${updateFields.join(', ')}, updated_at = NOW() WHERE id = $${paramCount}`,
      updateValues
    );

    res.json({
      success: true,
      message: 'تم تحديث تفضيلات المصادقة البيومترية بنجاح'
    });

  } catch (error) {
    console.error('خطأ في تحديث تفضيلات المصادقة البيومترية:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحديث تفضيلات المصادقة البيومترية'
    });
  }
};

// Get biometric authentication logs
const getBiometricLogs = async (req, res) => {
  try {
    const { userId } = req.user;
    const { page = 1, limit = 20 } = req.query;

    const offset = (page - 1) * limit;

    const logsResult = await query(
      `SELECT bal.timestamp, bal.event_type, bal.event_status,
              bal.ip_address, bal.device_info, bal.error_code,
              bc.authenticator_name, bc.device_type
       FROM biometric_auth_logs bal
       LEFT JOIN biometric_credentials bc ON bal.credential_id = bc.credential_id
       WHERE bal.user_id = $1
       ORDER BY bal.timestamp DESC
       LIMIT $2 OFFSET $3`,
      [userId, limit, offset]
    );

    const totalResult = await query(
      'SELECT COUNT(*) as total FROM biometric_auth_logs WHERE user_id = $1',
      [userId]
    );

    res.json({
      success: true,
      logs: logsResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(totalResult.rows[0].total),
        pages: Math.ceil(totalResult.rows[0].total / limit)
      },
      message: 'تم جلب سجلات المصادقة البيومترية بنجاح'
    });

  } catch (error) {
    console.error('خطأ في جلب سجلات المصادقة البيومترية:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب سجلات المصادقة البيومترية'
    });
  }
};

module.exports = {
  generateRegistrationChallenge,
  completeRegistration,
  generateAuthenticationChallenge,
  completeAuthentication,
  getBiometricCredentials,
  removeBiometricCredential,
  updateBiometricPreferences,
  getBiometricLogs
};
