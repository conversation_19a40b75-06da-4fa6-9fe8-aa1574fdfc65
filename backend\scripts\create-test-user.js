const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { query } = require('../src/models/database');

async function createTestUser() {
  try {
    console.log('🔄 Creating test user...');

    // Test user details
    const testUser = {
      id: uuidv4(),
      email: '<EMAIL>',
      password: 'password123',
      full_name: 'Regular User',
      role: 'user' // Regular user role (not admin)
    };

    // Hash the password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(testUser.password, saltRounds);

    // Check if user already exists
    const existingUser = await query(
      'SELECT id FROM users WHERE email = $1',
      [testUser.email]
    );

    if (existingUser.rows.length > 0) {
      console.log('⚠️  Test user already exists with email:', testUser.email);
      console.log('📧 Email:', testUser.email);
      console.log('🔑 Password:', testUser.password);
      return;
    }

    // Insert the test user
    const result = await query(
      `INSERT INTO users (
        id, email, password_hash, full_name, role, language, text_direction,
        created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      RETURNING id, email, full_name, role`,
      [
        testUser.id,
        testUser.email,
        hashedPassword,
        testUser.full_name,
        testUser.role,
        'ar',
        'rtl'
      ]
    );

    console.log('✅ Test user created successfully!');
    console.log('👤 User Details:');
    console.log('   📧 Email:', testUser.email);
    console.log('   🔑 Password:', testUser.password);
    console.log('   👤 Name:', testUser.full_name);
    console.log('   🎭 Role:', testUser.role);
    console.log('   🆔 ID:', testUser.id);
    console.log('');
    console.log('🎯 You can now use these credentials to test the Mail functionality:');
    console.log('   • Login as regular user to upload documents for review');
    console.log('   • Login as admin to review and sign/reject documents');
    console.log('');
    console.log('📝 Test Scenarios:');
    console.log('   1. Login as test user → Upload PDF in Mail page');
    console.log('   2. Login as admin → Review pending documents in Mail page');
    console.log('   3. Admin can sign or reject the uploaded document');

  } catch (error) {
    console.error('❌ Error creating test user:', error);
    process.exit(1);
  }
}

// Run the script
createTestUser()
  .then(() => {
    console.log('🎉 Test user creation completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
