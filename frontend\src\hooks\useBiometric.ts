import { useState, useEffect, useCallback } from 'react';
import webauthnService, { BiometricCapabilities } from '../services/webauthnService';
import api from '../services/api';

interface BiometricCredential {
  id: string;
  credential_id: string;
  device_type: string;
  authenticator_name: string;
  last_used_at: string | null;
  created_at: string;
  device_name?: string;
  platform?: string;
  trust_level?: string;
}

interface BiometricStatus {
  biometric_enabled: boolean;
  biometric_enrolled_at: string | null;
  preferred_auth_method: string;
  failed_biometric_attempts: number;
  biometric_locked_until: string | null;
}

interface UseBiometricReturn {
  // Capabilities
  capabilities: BiometricCapabilities | null;
  isSupported: boolean;
  isAvailable: boolean;
  
  // Status
  biometricStatus: BiometricStatus | null;
  credentials: BiometricCredential[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  checkCapabilities: () => Promise<void>;
  registerBiometric: () => Promise<{ success: boolean; message: string }>;
  authenticateBiometric: (email: string) => Promise<{ success: boolean; message: string; token?: string; refreshToken?: string; user?: any }>;
  removeBiometric: (credentialId: string) => Promise<{ success: boolean; message: string }>;
  updatePreferences: (preferences: { preferredAuthMethod?: string; notificationPreferences?: any }) => Promise<{ success: boolean; message: string }>;
  refreshCredentials: () => Promise<void>;
  clearError: () => void;
}

export const useBiometric = (): UseBiometricReturn => {
  const [capabilities, setCapabilities] = useState<BiometricCapabilities | null>(null);
  const [biometricStatus, setBiometricStatus] = useState<BiometricStatus | null>(null);
  const [credentials, setCredentials] = useState<BiometricCredential[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check biometric capabilities
  const checkCapabilities = useCallback(async () => {
    try {
      setIsLoading(true);
      const caps = await webauthnService.getBiometricCapabilities();
      setCapabilities(caps);
    } catch (err: any) {
      setError(err.message || 'فشل في فحص قدرات المصادقة البيومترية');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load user's biometric credentials and status
  const refreshCredentials = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/biometric/credentials');
      
      if (response.data.success) {
        setBiometricStatus(response.data.biometricStatus);
        setCredentials(response.data.credentials || []);
      } else {
        setError(response.data.message || 'فشل في جلب بيانات المصادقة البيومترية');
      }
    } catch (err: any) {
      // Don't set error for 401 (user not logged in)
      if (err.response?.status !== 401) {
        setError(err.response?.data?.message || 'فشل في جلب بيانات المصادقة البيومترية');
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Register biometric authentication
  const registerBiometric = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await webauthnService.registerBiometric();
      
      if (result.success) {
        // Refresh credentials after successful registration
        await refreshCredentials();
      }
      
      return result;
    } catch (err: any) {
      const errorMessage = err.message || 'فشل في تسجيل المصادقة البيومترية';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, [refreshCredentials]);

  // Authenticate using biometric
  const authenticateBiometric = useCallback(async (email: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await webauthnService.authenticateBiometric(email);
      
      return result;
    } catch (err: any) {
      const errorMessage = err.message || 'فشل في المصادقة البيومترية';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Remove biometric credential
  const removeBiometric = useCallback(async (credentialId: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await api.delete(`/biometric/credentials/${credentialId}`);
      
      if (response.data.success) {
        // Refresh credentials after successful removal
        await refreshCredentials();
      }
      
      return {
        success: response.data.success,
        message: response.data.message
      };
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'فشل في حذف بيانات المصادقة البيومترية';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, [refreshCredentials]);

  // Update biometric preferences
  const updatePreferences = useCallback(async (preferences: { preferredAuthMethod?: string; notificationPreferences?: any }) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await api.put('/biometric/preferences', preferences);
      
      if (response.data.success) {
        // Refresh status after successful update
        await refreshCredentials();
      }
      
      return {
        success: response.data.success,
        message: response.data.message
      };
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'فشل في تحديث تفضيلات المصادقة البيومترية';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, [refreshCredentials]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Initialize capabilities on mount
  useEffect(() => {
    checkCapabilities();
  }, [checkCapabilities]);

  // Derived values
  const isSupported = capabilities?.isSupported ?? false;
  const isAvailable = capabilities?.isAvailable ?? false;

  return {
    // Capabilities
    capabilities,
    isSupported,
    isAvailable,
    
    // Status
    biometricStatus,
    credentials,
    isLoading,
    error,
    
    // Actions
    checkCapabilities,
    registerBiometric,
    authenticateBiometric,
    removeBiometric,
    updatePreferences,
    refreshCredentials,
    clearError
  };
};

export default useBiometric;
