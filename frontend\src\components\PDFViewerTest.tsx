import React, { useState, useMemo } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

// Import CSS files for react-pdf
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const PDFViewerTest: React.FC = () => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [pdfData, setPdfData] = useState<string | null>(null);
  const [testStatus, setTestStatus] = useState<string>('');

  const documentId = '5dfdd344-43cc-4060-911c-2579571972d1';
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIwZjFlYmIzZS03NWY2LTQ0ZGQtOGFiYS1iZjJlM2U4NTdhMDQiLCJpYXQiOjE3NTI2ODQ4MjcsImV4cCI6MTc1Mjc3MTIyN30.fHhH70Nf1L11XiXq6EvHzlOg71x2B-qFkmQL9PpZVS4';

  const fetchPDF = async () => {
    try {
      setLoading(true);
      setError('');
      setTestStatus('جاري تحميل PDF...');

      const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
      const url = `${API_BASE_URL}/documents/${documentId}/view?token=${encodeURIComponent(token)}`;

      console.log('Fetching PDF from:', url);
      setTestStatus(`جاري تحميل من: ${url}`);

      const response = await fetch(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType !== 'application/pdf') {
        throw new Error(`Expected PDF but got ${contentType}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      console.log('PDF data fetched, size:', arrayBuffer.byteLength);
      setTestStatus(`تم تحميل PDF بنجاح - الحجم: ${arrayBuffer.byteLength} بايت`);

      // Convert to Uint8Array to verify PDF format
      const uint8Array = new Uint8Array(arrayBuffer);

      // Verify it's a valid PDF
      const headerBytes = Array.from(uint8Array.slice(0, 4));
      const pdfHeader = String.fromCharCode.apply(null, headerBytes);

      if (pdfHeader !== '%PDF') {
        throw new Error('Invalid PDF file format');
      }

      // Create a blob URL for react-pdf
      const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
      const blobUrl = URL.createObjectURL(blob);

      setPdfData(blobUrl);
      setTestStatus('PDF جاهز للعرض');
      
    } catch (error) {
      console.error('PDF fetch error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(`فشل في تحميل PDF: ${errorMessage}`);
      setTestStatus(`خطأ: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    console.log('PDF loaded successfully with', numPages, 'pages');
    setNumPages(numPages);
    setTestStatus(`PDF محمل بنجاح - ${numPages} صفحة`);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error('PDF.js load error:', error);
    setError(`فشل في معالجة PDF: ${error.message}`);
    setTestStatus(`خطأ PDF.js: ${error.message}`);
  };

  // Memoize PDF.js options to prevent unnecessary reloads
  const pdfOptions = useMemo(() => ({
    cMapUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`,
    cMapPacked: true,
    standardFontDataUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/standard_fonts/`,
    verbosity: 1
  }), []);

  return (
    <div className="p-6 max-w-4xl mx-auto" dir="rtl">
      <h1 className="text-2xl font-bold mb-6 font-['Almarai']">اختبار عارض PDF</h1>
      
      {/* Test Controls */}
      <div className="mb-6 p-4 border rounded-lg bg-gray-50">
        <h2 className="text-lg font-semibold mb-4 font-['Almarai']">التحكم في الاختبار</h2>
        
        <div className="space-y-2 mb-4">
          <p className="text-sm font-['Almarai']"><strong>معرف المستند:</strong> {documentId}</p>
          <p className="text-sm font-['Almarai']"><strong>إصدار PDF.js:</strong> {pdfjs.version}</p>
          <p className="text-sm font-['Almarai']"><strong>Worker URL:</strong> {pdfjs.GlobalWorkerOptions.workerSrc}</p>
        </div>
        
        <button
          onClick={fetchPDF}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 font-['Almarai']"
        >
          {loading ? 'جاري التحميل...' : 'تحميل PDF'}
        </button>
        
        {testStatus && (
          <div className="mt-4 p-3 bg-blue-100 border border-blue-300 rounded">
            <p className="text-blue-800 font-['Almarai']">{testStatus}</p>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-300 rounded">
          <h3 className="text-red-800 font-bold font-['Almarai']">خطأ</h3>
          <p className="text-red-700 font-['Almarai']">{error}</p>
        </div>
      )}

      {/* PDF Viewer */}
      {pdfData && !error && (
        <div className="border rounded-lg overflow-hidden">
          <div className="bg-gray-100 p-3 border-b">
            <h3 className="font-semibold font-['Almarai']">عارض PDF</h3>
            {numPages > 0 && (
              <div className="flex items-center justify-between mt-2">
                <span className="text-sm font-['Almarai']">صفحة {pageNumber} من {numPages}</span>
                <div className="space-x-2 space-x-reverse">
                  <button
                    onClick={() => setPageNumber(Math.max(1, pageNumber - 1))}
                    disabled={pageNumber <= 1}
                    className="px-3 py-1 bg-gray-300 rounded disabled:opacity-50 font-['Almarai']"
                  >
                    السابق
                  </button>
                  <button
                    onClick={() => setPageNumber(Math.min(numPages, pageNumber + 1))}
                    disabled={pageNumber >= numPages}
                    className="px-3 py-1 bg-gray-300 rounded disabled:opacity-50 font-['Almarai']"
                  >
                    التالي
                  </button>
                </div>
              </div>
            )}
          </div>
          
          <div className="p-4 bg-gray-50 flex justify-center">
            <Document
              file={pdfData}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadError}
              loading={
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                  <p className="text-gray-600 font-['Almarai']">جاري معالجة PDF...</p>
                </div>
              }
              error={
                <div className="text-center">
                  <p className="text-red-600 font-['Almarai']">فشل في معالجة PDF</p>
                </div>
              }
              options={pdfOptions}
            >
              <Page
                pageNumber={pageNumber}
                scale={1.0}
                renderTextLayer={true}
                renderAnnotationLayer={true}
                className="shadow-lg"
              />
            </Document>
          </div>
        </div>
      )}
    </div>
  );
};

export default PDFViewerTest;
