import React from 'react';

interface Step {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  current: boolean;
}

interface ProgressIndicatorProps {
  steps: Step[];
  className?: string;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ steps, className = '' }) => {
  return (
    <div className={`w-full ${className}`}>
      <nav aria-label="Progress">
        <ol className="flex items-center">
          {steps.map((step, stepIdx) => (
            <li key={step.id} className={`relative ${stepIdx !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''} flex-1`}>
              {/* Connector line */}
              {stepIdx !== steps.length - 1 && (
                <div className="absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300" aria-hidden="true">
                  <div 
                    className={`h-full w-0.5 transition-all duration-500 ${
                      step.completed ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  />
                </div>
              )}
              
              <div className="group relative flex items-start">
                <span className="flex h-9 items-center">
                  <span
                    className={`relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 transition-all duration-300 ${
                      step.completed
                        ? 'border-blue-600 bg-blue-600'
                        : step.current
                        ? 'border-blue-600 bg-white'
                        : 'border-gray-300 bg-white'
                    }`}
                  >
                    {step.completed ? (
                      <svg className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : step.current ? (
                      <span className="h-2.5 w-2.5 rounded-full bg-blue-600" />
                    ) : (
                      <span className="h-2.5 w-2.5 rounded-full bg-transparent group-hover:bg-gray-300" />
                    )}
                  </span>
                </span>
                
                <span className="mr-4 flex min-w-0 flex-col">
                  <span
                    className={`text-sm font-medium transition-colors duration-300 ${
                      step.completed || step.current ? 'text-blue-600' : 'text-gray-500'
                    }`}
                  >
                    {step.title}
                  </span>
                  {step.description && (
                    <span className="text-sm text-gray-500 mt-1">
                      {step.description}
                    </span>
                  )}
                </span>
              </div>
            </li>
          ))}
        </ol>
      </nav>
    </div>
  );
};

// Predefined step configurations for common workflows
export const DocumentSigningSteps = (currentStep: number): Step[] => [
  {
    id: 'upload',
    title: 'رفع المستند',
    description: 'اختيار وتحميل المستند',
    completed: currentStep > 1,
    current: currentStep === 1
  },
  {
    id: 'review',
    title: 'مراجعة المستند',
    description: 'التحقق من محتوى المستند',
    completed: currentStep > 2,
    current: currentStep === 2
  },
  {
    id: 'sign',
    title: 'التوقيع',
    description: 'إضافة التوقيع الرقمي',
    completed: currentStep > 3,
    current: currentStep === 3
  },
  {
    id: 'complete',
    title: 'اكتمال',
    description: 'تم توقيع المستند بنجاح',
    completed: currentStep > 4,
    current: currentStep === 4
  }
];

export const UserRegistrationSteps = (currentStep: number): Step[] => [
  {
    id: 'info',
    title: 'المعلومات الأساسية',
    description: 'البريد الإلكتروني وكلمة المرور',
    completed: currentStep > 1,
    current: currentStep === 1
  },
  {
    id: 'verification',
    title: 'التحقق',
    description: 'تأكيد البريد الإلكتروني',
    completed: currentStep > 2,
    current: currentStep === 2
  },
  {
    id: 'profile',
    title: 'الملف الشخصي',
    description: 'إكمال المعلومات الشخصية',
    completed: currentStep > 3,
    current: currentStep === 3
  },
  {
    id: 'complete',
    title: 'اكتمال',
    description: 'تم إنشاء الحساب بنجاح',
    completed: currentStep > 4,
    current: currentStep === 4
  }
];

export const DocumentUploadSteps = (currentStep: number): Step[] => [
  {
    id: 'select',
    title: 'اختيار الملف',
    description: 'تحديد المستند للرفع',
    completed: currentStep > 1,
    current: currentStep === 1
  },
  {
    id: 'upload',
    title: 'الرفع',
    description: 'تحميل المستند إلى الخادم',
    completed: currentStep > 2,
    current: currentStep === 2
  },
  {
    id: 'processing',
    title: 'المعالجة',
    description: 'معالجة وفحص المستند',
    completed: currentStep > 3,
    current: currentStep === 3
  },
  {
    id: 'complete',
    title: 'اكتمال',
    description: 'تم رفع المستند بنجاح',
    completed: currentStep > 4,
    current: currentStep === 4
  }
];

export default ProgressIndicator;
