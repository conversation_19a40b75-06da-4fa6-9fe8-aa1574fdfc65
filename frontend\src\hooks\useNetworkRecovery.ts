import { useState, useEffect, useCallback, useRef } from 'react';

interface NetworkState {
  isOnline: boolean;
  isConnecting: boolean;
  lastConnected: Date | null;
  connectionQuality: 'good' | 'poor' | 'offline';
  retryCount: number;
}

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

interface UseNetworkRecoveryOptions {
  retryConfig?: Partial<RetryConfig>;
  onConnectionLost?: () => void;
  onConnectionRestored?: () => void;
  enableAutoRetry?: boolean;
}

const defaultRetryConfig: RetryConfig = {
  maxRetries: 5,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffFactor: 2
};

export const useNetworkRecovery = (options: UseNetworkRecoveryOptions = {}) => {
  const {
    retryConfig = {},
    onConnectionLost,
    onConnectionRestored,
    enableAutoRetry = true
  } = options;

  const config = { ...defaultRetryConfig, ...retryConfig };
  
  const [networkState, setNetworkState] = useState<NetworkState>({
    isOnline: navigator.onLine,
    isConnecting: false,
    lastConnected: navigator.onLine ? new Date() : null,
    connectionQuality: navigator.onLine ? 'good' : 'offline',
    retryCount: 0
  });

  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const connectionTestRef = useRef<AbortController>();

  // Test connection quality
  const testConnectionQuality = useCallback(async (): Promise<'good' | 'poor' | 'offline'> => {
    try {
      const controller = new AbortController();
      connectionTestRef.current = controller;

      const startTime = Date.now();
      const response = await fetch('/api/performance/health', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return responseTime < 2000 ? 'good' : 'poor';
      } else {
        return 'offline';
      }
    } catch (error) {
      return 'offline';
    }
  }, []);

  // Handle online/offline events
  const handleOnline = useCallback(async () => {
    console.log('🌐 Network connection detected, testing quality...');
    
    setNetworkState(prev => ({
      ...prev,
      isOnline: true,
      isConnecting: true
    }));

    const quality = await testConnectionQuality();
    
    setNetworkState(prev => ({
      ...prev,
      isConnecting: false,
      connectionQuality: quality,
      lastConnected: quality !== 'offline' ? new Date() : prev.lastConnected,
      retryCount: 0
    }));

    if (quality !== 'offline') {
      console.log('✅ Network connection restored');
      onConnectionRestored?.();
    }
  }, [testConnectionQuality, onConnectionRestored]);

  const handleOffline = useCallback(() => {
    console.log('❌ Network connection lost');
    
    setNetworkState(prev => ({
      ...prev,
      isOnline: false,
      connectionQuality: 'offline',
      isConnecting: false
    }));

    onConnectionLost?.();
  }, [onConnectionLost]);

  // Retry connection with exponential backoff
  const retryConnection = useCallback(async () => {
    if (!enableAutoRetry) return;

    setNetworkState(prev => {
      if (prev.retryCount >= config.maxRetries) {
        console.log('🚫 Max retry attempts reached');
        return prev;
      }

      const newRetryCount = prev.retryCount + 1;
      const delay = Math.min(
        config.baseDelay * Math.pow(config.backoffFactor, newRetryCount - 1),
        config.maxDelay
      );

      console.log(`🔄 Retrying connection (attempt ${newRetryCount}/${config.maxRetries}) in ${delay}ms`);

      retryTimeoutRef.current = setTimeout(async () => {
        const quality = await testConnectionQuality();
        
        setNetworkState(current => ({
          ...current,
          connectionQuality: quality,
          isOnline: quality !== 'offline',
          lastConnected: quality !== 'offline' ? new Date() : current.lastConnected,
          isConnecting: false
        }));

        if (quality === 'offline') {
          retryConnection();
        } else {
          console.log('✅ Connection restored after retry');
          onConnectionRestored?.();
        }
      }, delay);

      return {
        ...prev,
        retryCount: newRetryCount,
        isConnecting: true
      };
    });
  }, [config, enableAutoRetry, testConnectionQuality, onConnectionRestored]);

  // Manual retry function
  const manualRetry = useCallback(async () => {
    console.log('🔄 Manual connection retry triggered');
    
    setNetworkState(prev => ({
      ...prev,
      isConnecting: true,
      retryCount: 0
    }));

    const quality = await testConnectionQuality();
    
    setNetworkState(prev => ({
      ...prev,
      isConnecting: false,
      connectionQuality: quality,
      isOnline: quality !== 'offline',
      lastConnected: quality !== 'offline' ? new Date() : prev.lastConnected
    }));

    if (quality !== 'offline') {
      onConnectionRestored?.();
    }

    return quality !== 'offline';
  }, [testConnectionQuality, onConnectionRestored]);

  // Reset retry count
  const resetRetryCount = useCallback(() => {
    setNetworkState(prev => ({
      ...prev,
      retryCount: 0
    }));
  }, []);

  // Setup event listeners
  useEffect(() => {
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial connection test
    if (navigator.onLine) {
      testConnectionQuality().then(quality => {
        setNetworkState(prev => ({
          ...prev,
          connectionQuality: quality
        }));
      });
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      
      if (connectionTestRef.current) {
        connectionTestRef.current.abort();
      }
    };
  }, [handleOnline, handleOffline, testConnectionQuality]);

  // Auto-retry when offline
  useEffect(() => {
    if (networkState.connectionQuality === 'offline' && enableAutoRetry) {
      retryConnection();
    }
  }, [networkState.connectionQuality, enableAutoRetry, retryConnection]);

  return {
    ...networkState,
    manualRetry,
    resetRetryCount,
    testConnectionQuality
  };
};

// Hook for automatic request retry with network recovery
export const useRetryableRequest = <T>(
  requestFn: () => Promise<T>,
  options: UseNetworkRecoveryOptions & {
    retryOnNetworkError?: boolean;
    retryOnServerError?: boolean;
  } = {}
) => {
  const { retryOnNetworkError = true, retryOnServerError = false, ...networkOptions } = options;
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<T | null>(null);

  const { isOnline, manualRetry } = useNetworkRecovery(networkOptions);

  const executeRequest = useCallback(async (isRetry = false) => {
    if (!isOnline && !isRetry) {
      setError(new Error('لا يوجد اتصال بالإنترنت'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await requestFn();
      setData(result);
      setError(null);
    } catch (err: any) {
      const isNetworkError = err.name === 'TypeError' || err.message.includes('fetch');
      const isServerError = err.response?.status >= 500;

      if ((isNetworkError && retryOnNetworkError) || (isServerError && retryOnServerError)) {
        // Will be retried automatically by network recovery
        setError(err);
      } else {
        setError(err);
      }
    } finally {
      setIsLoading(false);
    }
  }, [requestFn, isOnline, retryOnNetworkError, retryOnServerError]);

  const retry = useCallback(async () => {
    if (!isOnline) {
      const connected = await manualRetry();
      if (connected) {
        await executeRequest(true);
      }
    } else {
      await executeRequest(true);
    }
  }, [isOnline, manualRetry, executeRequest]);

  return {
    data,
    error,
    isLoading,
    execute: executeRequest,
    retry,
    isOnline
  };
};
