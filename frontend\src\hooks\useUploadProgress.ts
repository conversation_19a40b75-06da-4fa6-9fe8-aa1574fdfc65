import { useState, useCallback } from 'react';
import axios, { AxiosProgressEvent } from 'axios';

// Get the API base URL from environment or default
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed: number; // bytes per second
  timeRemaining: number; // seconds
  isUploading: boolean;
  error: string | null;
}

interface UseUploadProgressReturn {
  progress: UploadProgress;
  uploadFile: (file: File, url: string, additionalData?: Record<string, any>) => Promise<any>;
  resetProgress: () => void;
}

const useUploadProgress = (): UseUploadProgressReturn => {
  const [progress, setProgress] = useState<UploadProgress>({
    loaded: 0,
    total: 0,
    percentage: 0,
    speed: 0,
    timeRemaining: 0,
    isUploading: false,
    error: null,
  });

  // Remove unused startTime state - using local variables instead

  const resetProgress = useCallback(() => {
    setProgress({
      loaded: 0,
      total: 0,
      percentage: 0,
      speed: 0,
      timeRemaining: 0,
      isUploading: false,
      error: null,
    });
  }, []);

  const uploadFile = useCallback(async (
    file: File, 
    url: string, 
    additionalData?: Record<string, any>
  ): Promise<any> => {
    const formData = new FormData();
    formData.append('document', file);
    
    // Add any additional form data
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, typeof value === 'string' ? value : JSON.stringify(value));
      });
    }

    const uploadStartTime = Date.now();

    setProgress(prev => ({
      ...prev,
      isUploading: true,
      error: null,
      total: file.size,
    }));

    try {
      const token = localStorage.getItem('token');
      // Construct full URL if it's a relative path
      const fullUrl = url.startsWith('/') ? `${API_BASE_URL}${url}` : url;

      const response = await axios.post(fullUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        timeout: 10 * 60 * 1000, // 10 minutes timeout
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          const { loaded, total } = progressEvent;
          const currentTime = Date.now();
          const elapsedTime = (currentTime - uploadStartTime) / 1000; // seconds
          
          if (total && loaded && elapsedTime > 0) {
            const percentage = Math.round((loaded / total) * 100);
            const speed = loaded / elapsedTime; // bytes per second
            const remainingBytes = total - loaded;
            const timeRemaining = speed > 0 ? remainingBytes / speed : 0;

            setProgress({
              loaded,
              total,
              percentage,
              speed,
              timeRemaining,
              isUploading: true,
              error: null,
            });
          }
        },
      });

      setProgress(prev => ({
        ...prev,
        isUploading: false,
        percentage: 100,
      }));

      return response.data;
    } catch (error: any) {
      let errorMessage = 'فشل في رفع الملف';
      
      if (error.code === 'ECONNABORTED') {
        errorMessage = 'انتهت مهلة الرفع. يرجى المحاولة مرة أخرى.';
      } else if (error.response?.status === 413) {
        errorMessage = 'الملف كبير جداً. يرجى المحاولة مع ملف أصغر.';
      } else if (error.response?.status === 507) {
        errorMessage = 'مساحة التخزين غير كافية. يرجى المحاولة لاحقاً.';
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setProgress(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage,
      }));

      throw error;
    }
  }, []);

  return {
    progress,
    uploadFile,
    resetProgress,
  };
};

// Chunked upload for very large files
const useChunkedUpload = () => {
  const [progress, setProgress] = useState<UploadProgress>({
    loaded: 0,
    total: 0,
    percentage: 0,
    speed: 0,
    timeRemaining: 0,
    isUploading: false,
    error: null,
  });

  const uploadFileInChunks = useCallback(async (
    file: File,
    url: string,
    chunkSize: number = 10 * 1024 * 1024, // 10MB chunks
    additionalData?: Record<string, any>
  ): Promise<any> => {
    const fileId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const totalChunks = Math.ceil(file.size / chunkSize);
    const uploadStartTime = Date.now();

    setProgress({
      loaded: 0,
      total: file.size,
      percentage: 0,
      speed: 0,
      timeRemaining: 0,
      isUploading: true,
      error: null,
    });

    try {
      let uploadedBytes = 0;

      for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        const start = chunkIndex * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        const chunk = file.slice(start, end);

        const formData = new FormData();
        formData.append('chunk', chunk);
        formData.append('chunkIndex', chunkIndex.toString());
        formData.append('totalChunks', totalChunks.toString());
        formData.append('fileId', fileId);
        formData.append('originalName', file.name);

        // Add additional data
        if (additionalData) {
          Object.entries(additionalData).forEach(([key, value]) => {
            formData.append(key, typeof value === 'string' ? value : JSON.stringify(value));
          });
        }

        const token = localStorage.getItem('token');
        // Construct full URL for chunked upload
        const chunkUrl = url.startsWith('/') ? `${API_BASE_URL}${url}/chunk` : `${url}/chunk`;
        const response = await axios.post(chunkUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            ...(token && { Authorization: `Bearer ${token}` }),
          },
          timeout: 5 * 60 * 1000, // 5 minutes per chunk
        });

        uploadedBytes += chunk.size;
        const currentTime = Date.now();
        const elapsedTime = (currentTime - uploadStartTime) / 1000;
        const speed = uploadedBytes / elapsedTime;
        const remainingBytes = file.size - uploadedBytes;
        const timeRemaining = speed > 0 ? remainingBytes / speed : 0;

        setProgress({
          loaded: uploadedBytes,
          total: file.size,
          percentage: Math.round((uploadedBytes / file.size) * 100),
          speed,
          timeRemaining,
          isUploading: true,
          error: null,
        });

        // If this is the last chunk and it's complete, return the response
        if (response.data.isComplete) {
          setProgress(prev => ({
            ...prev,
            isUploading: false,
            percentage: 100,
          }));
          return response.data;
        }
      }

      throw new Error('Upload completed but file assembly failed');
    } catch (error: any) {
      let errorMessage = 'فشل في رفع الملف';

      if (error.code === 'ECONNABORTED') {
        errorMessage = 'انتهت مهلة الرفع. يرجى المحاولة مرة أخرى.';
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setProgress(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage,
      }));

      throw error;
    }
  }, []);

  const resetProgress = useCallback(() => {
    setProgress({
      loaded: 0,
      total: 0,
      percentage: 0,
      speed: 0,
      timeRemaining: 0,
      isUploading: false,
      error: null,
    });
  }, []);

  return {
    progress,
    uploadFileInChunks,
    resetProgress,
  };
};

export default useUploadProgress;
export { useChunkedUpload };
