const fs = require('fs');
const path = require('path');
const { embedSignatureInPDF } = require('./src/services/pdfService');

async function testSerialNumberEmbedding() {
  try {
    console.log('🧪 Testing Serial Number Embedding in PDF...\n');

    // Create a simple test PDF buffer (minimal PDF structure)
    const testPdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Resources <<
/Font <<
/F1 4 0 R
>>
>>
/Contents 5 0 R
>>
endobj

4 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

5 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test Document) Tj
ET
endstream
endobj

xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000251 00000 n
0000000329 00000 n
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
422
%%EOF`;

    // Create a simple test signature image (1x1 PNG)
    const testSignatureBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    ]);

    console.log('📄 Created test PDF and signature buffers');
    console.log(`PDF buffer size: ${Buffer.from(testPdfContent).length} bytes`);
    console.log(`Signature buffer size: ${testSignatureBuffer.length} bytes\n`);

    // Test the embedding function
    const result = await embedSignatureInPDF(
      Buffer.from(testPdfContent),
      testSignatureBuffer,
      {
        coordinates: { x: 100, y: 100 }
      }
    );

    console.log('✅ Serial number embedding completed successfully!');
    console.log(`📋 Generated Serial Number: ${result.serialNumber}`);
    console.log(`📐 Signature Coordinates: (${result.signatureCoordinates.x}, ${result.signatureCoordinates.y})`);
    console.log(`📏 PDF Size: ${result.pdfBytes.length} bytes`);
    console.log(`🔒 Digital Signature: ${result.digitalSignature.substring(0, 16)}...`);
    console.log(`📅 Timestamp: ${result.timestamp}`);
    console.log(`🌐 Metadata:`, result.metadata);

    // Save the result to a file for manual inspection
    const outputPath = path.join(__dirname, 'test-output-signed.pdf');
    fs.writeFileSync(outputPath, result.pdfBytes);
    console.log(`\n💾 Signed PDF saved to: ${outputPath}`);
    console.log('📖 You can open this file to verify the serial number is visible');

    return result;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testSerialNumberEmbedding()
    .then(() => {
      console.log('\n🎉 Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testSerialNumberEmbedding };