# Almarai Font Configuration Guide

## Overview

The e-signature application has been standardized to use **Almarai** as the single Arabic font across the entire application. This ensures consistency, better performance, and a unified visual experience.

## Font Details

**Almarai** is a modern Arabic font designed by <PERSON><PERSON><PERSON>, available through Google Fonts:
- **Font Family**: Almarai
- **Weights Available**: 300 (Light), 400 (Regular), 700 (Bold), 800 (Extra Bold)
- **Google Fonts URL**: `https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap`
- **License**: Open Font License (OFL)
- **Language Support**: Arabic, Latin

## Implementation Changes

### Frontend Configuration

#### 1. App.tsx - Google Fonts Loading
```typescript
// Load Almarai font from Google Fonts
const link = document.createElement('link');
link.href = 'https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap';
link.rel = 'stylesheet';
document.head.appendChild(link);
```

#### 2. index.css - Global Font Styles
```css
body {
  font-family: 'Almarai', sans-serif;
}

code {
  font-family: 'Almarai', source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

.btn {
  font-family: 'Almarai', sans-serif;
}

.arabic-text {
  font-family: 'Almarai', sans-serif;
}

.btn-rtl {
  font-family: 'Almarai', sans-serif;
}

@media print {
  body {
    font-family: 'Almarai', sans-serif;
  }
}
```

#### 3. tailwind.config.js - Tailwind Font Configuration
```javascript
module.exports = {
  theme: {
    extend: {
      fontFamily: {
        'sans': ['Almarai', 'sans-serif'],
        'arabic': ['Almarai', 'sans-serif'],
      },
    },
  },
}
```

### Backend Configuration

#### 1. arabicFontService.js - Font Service
```javascript
// Arabic font configuration - Using only Almarai
const ARABIC_FONTS = {
  ALMARAI: {
    name: 'Almarai',
    path: path.join(__dirname, '../fonts/Almarai-Regular.ttf'),
    fallbackUrl: 'https://fonts.gstatic.com/s/almarai/v12/tsssAp1RZy0Q_q2-2wjunAcFawmu.ttf'
  }
};

// Always return Almarai font for Arabic-only system
const getAppropriateFont = async () => {
  const fontPath = await downloadFont(ARABIC_FONTS.ALMARAI);
  return {
    fontPath,
    fontName: ARABIC_FONTS.ALMARAI.name,
    isArabic: true
  };
};
```

#### 2. rtlTextService.js - Text Metrics
```javascript
const calculateTextMetrics = (text, fontSize = 12, fontFamily = 'Almarai') => {
  const charWidths = {
    'Almarai': 0.65, // Almarai font character width
  };
  // ... rest of the function
};
```

#### 3. Environment Configuration
```bash
# .env.example
FONT_FAMILY=Almarai
```

## Font Weights Usage

### Available Weights
- **300 (Light)**: For subtle text, captions
- **400 (Regular)**: Default body text, forms, buttons
- **700 (Bold)**: Headings, important text, navigation
- **800 (Extra Bold)**: Main titles, hero text

### CSS Classes for Different Weights
```css
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
```

### Usage Examples
```jsx
// Light text for captions
<p className="font-light text-gray-600">تفاصيل إضافية</p>

// Regular text for body content
<p className="font-normal">محتوى النص الأساسي</p>

// Bold text for headings
<h2 className="font-bold text-xl">عنوان القسم</h2>

// Extra bold for main titles
<h1 className="font-extrabold text-3xl">نظام التوقيع الإلكتروني</h1>
```

## Performance Considerations

### Font Loading Optimization
1. **Preload Critical Font**: Add to HTML head for faster loading
```html
<link rel="preload" href="https://fonts.gstatic.com/s/almarai/v12/tsssAp1RZy0Q_q2-2wjunAcFawmu.ttf" as="font" type="font/ttf" crossorigin>
```

2. **Font Display Strategy**: Using `display=swap` for better performance
```css
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
```

3. **Subset Loading**: Load only required weights to reduce file size

### Browser Support
- **Modern Browsers**: Full support for Almarai via Google Fonts
- **Fallback**: System sans-serif fonts for older browsers
- **Progressive Enhancement**: Application works without custom fonts

## File Structure

### Frontend Files Modified
```
frontend/
├── src/
│   ├── App.tsx                 # Google Fonts loading
│   ├── index.css              # Global font styles
│   └── tailwind.config.js     # Tailwind font configuration
```

### Backend Files Modified
```
backend/
├── src/
│   ├── services/
│   │   ├── arabicFontService.js    # Font service configuration
│   │   └── rtlTextService.js       # Text metrics for Almarai
│   ├── server.js                   # Initialization messages
│   └── .env.example               # Environment configuration
```

## Testing Font Implementation

### Visual Testing
1. **Text Rendering**: Verify all Arabic text uses Almarai
2. **Font Weights**: Check different weights render correctly
3. **RTL Layout**: Ensure proper right-to-left text flow
4. **Print Styles**: Verify font consistency in print mode

### Browser Testing
```javascript
// Check if Almarai is loaded
document.fonts.ready.then(() => {
  const isAlmaraiLoaded = document.fonts.check('16px Almarai');
  console.log('Almarai font loaded:', isAlmaraiLoaded);
});
```

### Performance Testing
1. **Font Load Time**: Monitor Google Fonts loading speed
2. **Render Performance**: Check text rendering performance
3. **Bundle Size**: Verify no unnecessary font files in bundle

## Troubleshooting

### Common Issues

#### 1. Font Not Loading
**Problem**: Almarai font not displaying
**Solution**: 
- Check internet connection for Google Fonts
- Verify correct Google Fonts URL
- Check browser console for font loading errors

#### 2. Fallback Font Showing
**Problem**: System fonts showing instead of Almarai
**Solution**:
- Ensure Google Fonts link is in document head
- Check CSS font-family declarations
- Verify font loading completion

#### 3. PDF Generation Issues
**Problem**: Almarai not embedding in PDFs
**Solution**:
- Download Almarai TTF file to backend/src/fonts/
- Update font path in arabicFontService.js
- Verify fontkit integration

### Debug Commands
```bash
# Check font files in backend
ls -la backend/src/fonts/

# Verify Google Fonts loading in browser
curl -I "https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap"
```

## Migration Benefits

### Before (Multiple Fonts)
- Noto Sans Arabic
- Cairo
- Amiri
- Tahoma (fallback)

### After (Single Font)
- **Almarai only**
- Consistent visual appearance
- Reduced complexity
- Better performance
- Easier maintenance

### Performance Improvements
1. **Reduced Font Loading**: Single font family vs multiple
2. **Smaller Bundle Size**: No multiple font files
3. **Faster Rendering**: Consistent font metrics
4. **Better Caching**: Single font cached across pages

## Maintenance

### Regular Tasks
1. **Monitor Font Loading**: Check Google Fonts availability
2. **Update Font Versions**: Keep up with Google Fonts updates
3. **Test Across Browsers**: Ensure consistent rendering
4. **Performance Monitoring**: Track font loading metrics

### Future Considerations
1. **Self-Hosting**: Consider hosting Almarai locally for better control
2. **Font Subsetting**: Load only required characters for better performance
3. **Variable Fonts**: Upgrade to variable font version when available
4. **Accessibility**: Ensure font meets accessibility standards

This standardization to Almarai provides a clean, consistent, and performant typography solution for the entire e-signature application.
