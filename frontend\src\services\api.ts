import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors with automatic token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = localStorage.getItem('refreshToken');

      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {
            refreshToken
          });

          const { token: newToken, refreshToken: newRefreshToken } = response.data;

          localStorage.setItem('token', newToken);
          localStorage.setItem('refreshToken', newRefreshToken);

          api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
          originalRequest.headers.Authorization = `Bearer ${newToken}`;

          processQueue(null, newToken);

          return api(originalRequest);
        } catch (refreshError) {
          processQueue(refreshError, null);
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('user');
          window.location.href = '/login';
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      } else {
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),
  register: (email: string, password: string) =>
    api.post('/auth/register', { email, password }),
  refreshToken: (refreshToken: string) =>
    api.post('/auth/refresh-token', { refreshToken }),
  getProfile: () =>
    api.get('/auth/profile'),
  getAllUsers: () =>
    api.get('/auth/users'),
};

// Signature API
export const signatureAPI = {
  upload: (file: File) => {
    const formData = new FormData();
    formData.append('signature', file);
    return api.post('/signatures/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  getAll: () => 
    api.get('/signatures'),
  getById: (id: string) => 
    api.get(`/signatures/${id}`),
  delete: (id: string) => 
    api.delete(`/signatures/${id}`),
};

// Document API (Arabic-only)
export const documentAPI = {
  sign: (
    file: File,
    signatureId: string,
    coordinates?: { x: number; y: number }
  ) => {
    const formData = new FormData();
    formData.append('document', file);
    formData.append('signatureId', signatureId);
    if (coordinates) {
      formData.append('coordinates', JSON.stringify(coordinates));
    }
    return api.post('/documents/sign', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  getAll: (page = 1, limit = 10, status?: string) => {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    });
    if (status) params.append('status', status);
    return api.get(`/documents?${params}`);
  },
  verifySerial: (serialNumber: string) =>
    api.get(`/documents/verify/${encodeURIComponent(serialNumber)}`),
  getById: (id: string) =>
    api.get(`/documents/${id}`),
  download: (id: string) =>
    api.get(`/documents/${id}/download`, { responseType: 'blob' }),

  // Mail functionality
  uploadForReview: (formData: FormData) => api.post('/documents/upload-for-review', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),

  getPendingDocuments: () => api.get('/documents/pending'),

  signPendingDocument: (documentId: string) =>
    api.post(`/documents/${documentId}/sign-pending`),

  rejectPendingDocument: (documentId: string, reason: string) =>
    api.post(`/documents/${documentId}/reject`, { reason })
};

export default api;
