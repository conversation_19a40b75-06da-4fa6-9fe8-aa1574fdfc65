const multer = require('multer');
const { query } = require('../models/database');
const { encryptData, saveFile, generateFileName, readFile } = require('../services/encryptionService');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    // Remove artificial file size limit for signatures
    files: 1
  },
  fileFilter: (req, file, cb) => {
    // Allow PNG, JPG, JPEG, and SVG files
    const allowedMimes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Only PNG, JPG, JPEG, and SVG files are allowed'), false);
    }
  }
});

const uploadSignature = async (req, res) => {
  try {
    const { userId } = req.user;
    const file = req.file;

    if (!file) {
      return res.status(400).json({ error: 'No signature file uploaded' });
    }

    // Generate unique filename
    const fileName = generateFileName(file.originalname, userId, 'sig_');
    
    // Encrypt the file data
    const base64Data = file.buffer.toString('base64');
    const encryptedData = encryptData(base64Data);
    
    // Save encrypted file to local storage
    const filePath = await saveFile(Buffer.from(encryptedData), fileName, 'signatures');
    
    // Save metadata to database
    const result = await query(
      `INSERT INTO signatures (user_id, filename, file_path, file_size, mime_type, encrypted_data) 
       VALUES ($1, $2, $3, $4, $5, $6) RETURNING id, upload_date`,
      [userId, file.originalname, filePath, file.size, file.mimetype, 'encrypted']
    );

    // Log the upload
    await query(
      'INSERT INTO logs (user_id, action, resource_type, resource_id, details) VALUES ($1, $2, $3, $4, $5)',
      [userId, 'SIGNATURE_UPLOADED', 'signature', result.rows[0].id, { 
        filename: file.originalname, 
        fileSize: file.size,
        mimeType: file.mimetype 
      }]
    );

    res.status(201).json({
      message: 'Signature uploaded successfully',
      signature: {
        id: result.rows[0].id,
        filename: file.originalname,
        uploadDate: result.rows[0].upload_date,
        fileSize: file.size
      }
    });
  } catch (error) {
    console.error('Signature upload error:', error);
    
    if (error.message.includes('Only PNG, JPG, JPEG, and SVG files are allowed')) {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Signature upload failed' });
  }
};

const getSignatures = async (req, res) => {
  try {
    const { userId } = req.user;

    const result = await query(
      `SELECT id, filename, file_size, mime_type, upload_date
       FROM signatures
       WHERE user_id = $1
       ORDER BY upload_date DESC`,
      [userId]
    );

    // Transform snake_case to camelCase for frontend compatibility
    const signatures = result.rows.map(signature => ({
      id: signature.id,
      filename: signature.filename,
      fileSize: signature.file_size,
      mimeType: signature.mime_type,
      uploadDate: signature.upload_date
    }));

    res.json({
      signatures
    });
  } catch (error) {
    console.error('Get signatures error:', error);
    res.status(500).json({ error: 'Failed to retrieve signatures' });
  }
};

const getSignature = async (req, res) => {
  try {
    const { userId } = req.user;
    const { signatureId } = req.params;
    
    // Get signature metadata
    const result = await query(
      `SELECT id, filename, file_path, mime_type 
       FROM signatures 
       WHERE id = $1 AND user_id = $2`,
      [signatureId, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Signature not found' });
    }

    const signature = result.rows[0];
    
    // Read and decrypt the file
    const encryptedData = await readFile(signature.file_path);
    // Note: For this implementation, we'll return the encrypted data as-is
    // In a real application, you might want to decrypt it here
    
    res.set({
      'Content-Type': signature.mime_type,
      'Content-Disposition': `attachment; filename="${signature.filename}"`
    });
    
    res.send(encryptedData);
  } catch (error) {
    console.error('Get signature error:', error);
    res.status(500).json({ error: 'Failed to retrieve signature' });
  }
};

const deleteSignature = async (req, res) => {
  try {
    const { userId } = req.user;
    const { signatureId } = req.params;
    
    // Get signature info before deletion
    const signatureResult = await query(
      'SELECT id, filename, file_path FROM signatures WHERE id = $1 AND user_id = $2',
      [signatureId, userId]
    );

    if (signatureResult.rows.length === 0) {
      return res.status(404).json({ error: 'Signature not found' });
    }

    const signature = signatureResult.rows[0];
    
    // Delete from database
    await query(
      'DELETE FROM signatures WHERE id = $1 AND user_id = $2',
      [signatureId, userId]
    );

    // Log the deletion
    await query(
      'INSERT INTO logs (user_id, action, resource_type, resource_id, details) VALUES ($1, $2, $3, $4, $5)',
      [userId, 'SIGNATURE_DELETED', 'signature', signatureId, { filename: signature.filename }]
    );

    res.json({ message: 'Signature deleted successfully' });
  } catch (error) {
    console.error('Delete signature error:', error);
    res.status(500).json({ error: 'Failed to delete signature' });
  }
};

module.exports = {
  upload: upload.single('signature'),
  uploadSignature,
  getSignatures,
  getSignature,
  deleteSignature
};
