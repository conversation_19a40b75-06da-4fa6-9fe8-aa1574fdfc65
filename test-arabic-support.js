#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🇸🇦 Starting Comprehensive Arabic Language Support Tests...\n');

// Test configuration
const tests = [
  {
    name: 'Backend Arabic Database Tests',
    command: 'npm',
    args: ['test', 'arabic-database.test.js'],
    cwd: './backend',
    description: 'Testing Arabic text storage and retrieval in PostgreSQL'
  },
  {
    name: 'Backend Comprehensive Arabic Tests',
    command: 'npm',
    args: ['test', 'comprehensive-arabic.test.js'],
    cwd: './backend',
    description: 'Testing complete Arabic API functionality'
  },

  {
    name: 'Frontend Build Test',
    command: 'npm',
    args: ['run', 'build'],
    cwd: './frontend',
    description: 'Testing if Arabic-enabled frontend builds successfully'
  }
];

// Browser compatibility tests
const browserTests = [
  {
    name: 'Chrome Arabic Support',
    description: 'Testing Arabic text rendering in Chrome',
    url: 'http://localhost:3000'
  },
  {
    name: 'Firefox Arabic Support',
    description: 'Testing Arabic text rendering in Firefox',
    url: 'http://localhost:3000'
  },
  {
    name: 'Safari Arabic Support',
    description: 'Testing Arabic text rendering in Safari',
    url: 'http://localhost:3000'
  },
  {
    name: 'Edge Arabic Support',
    description: 'Testing Arabic text rendering in Edge',
    url: 'http://localhost:3000'
  }
];

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

function runTest(test) {
  return new Promise((resolve) => {
    console.log(`📋 Running: ${test.name}`);
    console.log(`   ${test.description}`);
    
    const process = spawn(test.command, test.args, {
      cwd: test.cwd,
      stdio: 'pipe',
      shell: true
    });

    let output = '';
    let errorOutput = '';

    process.stdout.on('data', (data) => {
      output += data.toString();
    });

    process.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    process.on('close', (code) => {
      testResults.total++;
      
      if (code === 0) {
        console.log(`   ✅ PASSED\n`);
        testResults.passed++;
        testResults.details.push({
          name: test.name,
          status: 'PASSED',
          output: output
        });
      } else {
        console.log(`   ❌ FAILED (Exit code: ${code})\n`);
        console.log(`   Error output: ${errorOutput}\n`);
        testResults.failed++;
        testResults.details.push({
          name: test.name,
          status: 'FAILED',
          output: output,
          error: errorOutput
        });
      }
      
      resolve();
    });

    process.on('error', (error) => {
      console.log(`   ❌ FAILED (Error: ${error.message})\n`);
      testResults.total++;
      testResults.failed++;
      testResults.details.push({
        name: test.name,
        status: 'FAILED',
        error: error.message
      });
      resolve();
    });
  });
}

async function runAllTests() {
  console.log('🔧 Setting up test environment...\n');
  
  // Check if required directories exist
  if (!fs.existsSync('./backend')) {
    console.log('❌ Backend directory not found');
    return;
  }
  
  if (!fs.existsSync('./frontend')) {
    console.log('❌ Frontend directory not found');
    return;
  }

  // Run all tests sequentially
  for (const test of tests) {
    await runTest(test);
  }

  // Generate test report
  console.log('📊 Test Results Summary:');
  console.log('========================');
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%\n`);

  // Detailed results
  console.log('📋 Detailed Results:');
  console.log('====================');
  testResults.details.forEach((result, index) => {
    console.log(`${index + 1}. ${result.name}: ${result.status}`);
    if (result.status === 'FAILED' && result.error) {
      console.log(`   Error: ${result.error.substring(0, 200)}...`);
    }
  });

  // Browser compatibility checklist
  console.log('\n🌐 Browser Compatibility Checklist:');
  console.log('====================================');
  console.log('Please manually test the following in each browser:');
  
  browserTests.forEach((browserTest, index) => {
    console.log(`${index + 1}. ${browserTest.name}`);
    console.log(`   ${browserTest.description}`);
    console.log(`   URL: ${browserTest.url}`);
    console.log(`   ☐ Arabic text displays correctly`);
    console.log(`   ☐ RTL layout works properly`);
    console.log(`   ☐ Forms accept Arabic input`);
    console.log(`   ☐ PDF generation includes Arabic text`);
    console.log(`   ☐ File uploads with Arabic names work`);
    console.log('');
  });

  // Arabic-specific feature checklist
  console.log('🔍 Arabic Feature Verification Checklist:');
  console.log('==========================================');
  
  const featureChecklist = [
    'User registration with Arabic email addresses',
    'Login with Arabic credentials',
    'Upload signatures with Arabic filenames',
    'Sign documents with Arabic filenames',
    'View document history with Arabic metadata',
    'Download signed documents with Arabic names',
    'Error messages display in Arabic',
    'Success messages display in Arabic',
    'Form validation messages in Arabic',
    'Date formatting in English locale',
    'Number formatting in Arabic numerals',
    'RTL text direction throughout the app',
    'Arabic font rendering quality',
    'PDF Arabic text rendering',
    'Database Arabic text storage/retrieval',
    'API responses in Arabic',
    'Navigation menu in Arabic',
    'Button labels in Arabic',
    'Table headers and data in Arabic',
    'Pagination controls in Arabic'
  ];

  featureChecklist.forEach((feature, index) => {
    console.log(`${index + 1}. ☐ ${feature}`);
  });

  console.log('\n🎯 Performance Considerations:');
  console.log('===============================');
  console.log('☐ Arabic font loading performance');
  console.log('☐ RTL layout rendering performance');
  console.log('☐ Arabic text search performance');
  console.log('☐ PDF generation with Arabic text performance');
  console.log('☐ Database query performance with Arabic text');

  console.log('\n📱 Mobile Device Testing:');
  console.log('==========================');
  console.log('☐ iOS Safari Arabic support');
  console.log('☐ Android Chrome Arabic support');
  console.log('☐ Mobile keyboard Arabic input');
  console.log('☐ Touch interactions with RTL layout');
  console.log('☐ Mobile PDF viewing with Arabic text');

  console.log('\n🔒 Security Considerations:');
  console.log('============================');
  console.log('☐ Arabic text SQL injection prevention');
  console.log('☐ Arabic filename security validation');
  console.log('☐ Arabic text XSS prevention');
  console.log('☐ Arabic character encoding security');

  // Final recommendations
  console.log('\n💡 Recommendations:');
  console.log('====================');
  
  if (testResults.failed > 0) {
    console.log('❗ Some tests failed. Please review the error messages above.');
    console.log('❗ Fix failing tests before deploying to production.');
  } else {
    console.log('✅ All automated tests passed!');
  }
  
  console.log('📝 Complete the manual browser testing checklist');
  console.log('📝 Test with real Arabic users for usability feedback');
  console.log('📝 Verify Arabic text displays correctly on different screen sizes');
  console.log('📝 Test with various Arabic fonts and browser settings');
  console.log('📝 Validate Arabic text in printed documents');
  
  console.log('\n🎉 Arabic Language Support Testing Complete!');
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Handle process interruption
process.on('SIGINT', () => {
  console.log('\n\n⚠️  Test execution interrupted by user');
  console.log('📊 Partial Results:');
  console.log(`   Completed: ${testResults.total} tests`);
  console.log(`   Passed: ${testResults.passed}`);
  console.log(`   Failed: ${testResults.failed}`);
  process.exit(1);
});

// Start testing
runAllTests().catch((error) => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
