const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const multer = require('multer');
const { uploadErrorHandler, memoryMonitor, uploadTimeout } = require('./middleware/errorHandler');
const { performanceMonitor, healthCheck } = require('./middleware/performanceMonitor');
const {
  rateLimitConfigs,
  sessionCleanup,
  resourceMonitor,
  createUploadSizeLimit
} = require('./middleware/resourceManager');
const monitoringService = require('./services/monitoringService');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const biometricRoutes = require('./routes/biometric');
const signatureRoutes = require('./routes/signatures');
const documentRoutes = require('./routes/documents');
const userRoutes = require('./routes/users');
const setupRoutes = require('./routes/setup');
const notificationRoutes = require('./routes/notifications');
const performanceRoutes = require('./routes/performance');
const analyticsRoutes = require('./routes/analytics');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(morgan('combined'));

// Enhanced resource management and rate limiting
app.use(rateLimitConfigs.general);
app.use(sessionCleanup);
app.use(resourceMonitor);

// Performance and memory monitoring for all requests
app.use(performanceMonitor);
app.use(memoryMonitor);

// Dynamic upload size limiting based on available memory
app.use(createUploadSizeLimit());

// Upload timeout for file upload routes
app.use('/api/documents', uploadTimeout(15 * 60 * 1000)); // 15 minutes for documents
app.use('/api/signatures', uploadTimeout(5 * 60 * 1000)); // 5 minutes for signatures

// Body parsing middleware - configured for large file uploads
app.use(express.json({
  limit: '500mb', // Increased for large documents
  parameterLimit: 50000,
  extended: true
}));
app.use(express.urlencoded({
  extended: true,
  limit: '500mb',
  parameterLimit: 50000
}));

// Enhanced health check endpoint with performance metrics
app.get('/health', healthCheck);

// Simple status endpoint for basic checks
app.get('/status', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'نظام التوقيع الإلكتروني يعمل بشكل طبيعي'
  });
});

// Routes with specific rate limiting
app.use('/api/auth', rateLimitConfigs.auth, authRoutes);
app.use('/api/biometric', rateLimitConfigs.auth, biometricRoutes);
app.use('/api/signatures', rateLimitConfigs.upload, signatureRoutes);
app.use('/api/documents', rateLimitConfigs.documents, documentRoutes);
app.use('/api/users', rateLimitConfigs.admin, userRoutes);
app.use('/api/setup', rateLimitConfigs.admin, setupRoutes);
app.use('/api/notifications', rateLimitConfigs.general, notificationRoutes);
app.use('/api/performance', rateLimitConfigs.admin, performanceRoutes);
app.use('/api/analytics', rateLimitConfigs.general, analyticsRoutes);

// Use enhanced error handling middleware
app.use(uploadErrorHandler);

// Handle frontend routes - redirect to frontend server in development
const frontendRoutes = ['/dashboard', '/documents', '/history', '/signature-upload', '/document-signing', '/sign', '/signing-confirmation', '/signing-error'];
app.get(frontendRoutes, (req, res) => {
  const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
  res.redirect(`${frontendUrl}${req.path}`);
});

// 404 handler for API routes
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

module.exports = app;
