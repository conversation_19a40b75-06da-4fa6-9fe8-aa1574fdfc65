const { query } = require('../models/database');

// Audit action types
const AUDIT_ACTIONS = {
  UPLOAD: 'UPLOAD',
  SIGN: 'SIGN',
  DOWNLOAD: 'DOWNLOAD',
  VIEW: 'VIEW',
  DELETE: 'DELETE',
  SHARE: 'SHARE',
  ACCESS_DENIED: 'ACCESS_DENIED',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  REGISTER: 'REGISTER',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
  SIGNATURE_CREATE: 'SIGNATURE_CREATE',
  SIGNATURE_DELETE: 'SIGNATURE_DELETE'
};

/**
 * Log an audit event
 * @param {Object} auditData - The audit data
 * @param {number} auditData.userId - User ID performing the action
 * @param {number} [auditData.documentId] - Document ID (if applicable)
 * @param {number} [auditData.signatureId] - Signature ID (if applicable)
 * @param {string} auditData.action - Action performed (use AUDIT_ACTIONS constants)
 * @param {Object} [auditData.details] - Additional action-specific details
 * @param {string} [auditData.ipAddress] - User's IP address
 * @param {string} [auditData.userAgent] - User's browser/client info
 * @param {string} [auditData.sessionId] - Session identifier
 * @param {boolean} [auditData.success=true] - Whether the action was successful
 * @param {string} [auditData.errorMessage] - Error message if action failed
 * @param {Object} [auditData.metadata] - Additional metadata
 */
const logAuditEvent = async (auditData) => {
  try {
    const {
      userId,
      documentId = null,
      signatureId = null,
      action,
      details = {},
      ipAddress = null,
      userAgent = null,
      sessionId = null,
      success = true,
      errorMessage = null,
      metadata = {}
    } = auditData;

    // Validate required fields
    if (!userId || !action) {
      console.error('Audit logging failed: userId and action are required');
      return false;
    }

    if (!Object.values(AUDIT_ACTIONS).includes(action)) {
      console.error(`Audit logging failed: Invalid action "${action}"`);
      return false;
    }

    const auditQuery = `
      INSERT INTO audit_logs (
        user_id, document_id, signature_id, action, details, 
        ip_address, user_agent, session_id, success, error_message, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING id, timestamp
    `;

    const values = [
      userId,
      documentId,
      signatureId,
      action,
      JSON.stringify(details),
      ipAddress,
      userAgent,
      sessionId,
      success,
      errorMessage,
      JSON.stringify(metadata)
    ];

    const result = await query(auditQuery, values);
    
    console.log(`Audit event logged: ${action} by user ${userId} at ${result.rows[0].timestamp}`);
    return result.rows[0];

  } catch (error) {
    console.error('Failed to log audit event:', error);
    return false;
  }
};

/**
 * Log document upload event
 */
const logDocumentUpload = async (userId, documentId, details = {}, req = null) => {
  return await logAuditEvent({
    userId,
    documentId,
    action: AUDIT_ACTIONS.UPLOAD,
    details: {
      fileName: details.fileName,
      fileSize: details.fileSize,
      fileType: details.fileType,
      ...details
    },
    ipAddress: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent'),
    sessionId: req?.sessionID,
    metadata: {
      uploadMethod: details.uploadMethod || 'standard',
      processingTime: details.processingTime
    }
  });
};

/**
 * Log document signing event
 */
const logDocumentSigning = async (userId, documentId, signatureId, details = {}, req = null) => {
  return await logAuditEvent({
    userId,
    documentId,
    signatureId,
    action: AUDIT_ACTIONS.SIGN,
    details: {
      signatureType: details.signatureType,
      coordinates: details.coordinates,
      serialNumber: details.serialNumber,
      ...details
    },
    ipAddress: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent'),
    sessionId: req?.sessionID,
    metadata: {
      signingMethod: details.signingMethod || 'standard',
      processingTime: details.processingTime
    }
  });
};

/**
 * Log document download event
 */
const logDocumentDownload = async (userId, documentId, details = {}, req = null) => {
  return await logAuditEvent({
    userId,
    documentId,
    action: AUDIT_ACTIONS.DOWNLOAD,
    details: {
      fileName: details.fileName,
      downloadType: details.downloadType || 'signed',
      ...details
    },
    ipAddress: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent'),
    sessionId: req?.sessionID
  });
};

/**
 * Log document view event
 */
const logDocumentView = async (userId, documentId, details = {}, req = null) => {
  return await logAuditEvent({
    userId,
    documentId,
    action: AUDIT_ACTIONS.VIEW,
    details: {
      viewType: details.viewType || 'browser',
      ...details
    },
    ipAddress: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent'),
    sessionId: req?.sessionID
  });
};

/**
 * Log access denied event
 */
const logAccessDenied = async (userId, documentId, reason, req = null) => {
  return await logAuditEvent({
    userId,
    documentId,
    action: AUDIT_ACTIONS.ACCESS_DENIED,
    success: false,
    errorMessage: reason,
    details: {
      reason,
      attemptedAction: req?.method + ' ' + req?.path
    },
    ipAddress: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent'),
    sessionId: req?.sessionID
  });
};

/**
 * Log user authentication events
 */
const logUserAuth = async (userId, action, details = {}, req = null) => {
  const validAuthActions = [AUDIT_ACTIONS.LOGIN, AUDIT_ACTIONS.LOGOUT, AUDIT_ACTIONS.REGISTER];
  
  if (!validAuthActions.includes(action)) {
    console.error(`Invalid auth action: ${action}`);
    return false;
  }

  return await logAuditEvent({
    userId,
    action,
    details,
    ipAddress: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent'),
    sessionId: req?.sessionID
  });
};

/**
 * Get audit logs for a user
 */
const getUserAuditLogs = async (userId, options = {}) => {
  try {
    const {
      limit = 50,
      offset = 0,
      action = null,
      documentId = null,
      startDate = null,
      endDate = null
    } = options;

    let whereConditions = ['user_id = $1'];
    let values = [userId];
    let paramCount = 1;

    if (action) {
      paramCount++;
      whereConditions.push(`action = $${paramCount}`);
      values.push(action);
    }

    if (documentId) {
      paramCount++;
      whereConditions.push(`document_id = $${paramCount}`);
      values.push(documentId);
    }

    if (startDate) {
      paramCount++;
      whereConditions.push(`timestamp >= $${paramCount}`);
      values.push(startDate);
    }

    if (endDate) {
      paramCount++;
      whereConditions.push(`timestamp <= $${paramCount}`);
      values.push(endDate);
    }

    const auditQuery = `
      SELECT 
        al.*,
        d.original_name as document_name,
        d.serial_number
      FROM audit_logs al
      LEFT JOIN documents d ON al.document_id = d.id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY al.timestamp DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    values.push(limit, offset);

    const result = await query(auditQuery, values);
    return result.rows;

  } catch (error) {
    console.error('Failed to get user audit logs:', error);
    throw error;
  }
};

/**
 * Get document audit logs
 */
const getDocumentAuditLogs = async (documentId, userId = null) => {
  try {
    let auditQuery = `
      SELECT 
        al.*,
        u.username,
        u.email
      FROM audit_logs al
      LEFT JOIN users u ON al.user_id = u.id
      WHERE al.document_id = $1
    `;
    
    let values = [documentId];

    // If userId is provided, only show logs for that user (privacy)
    if (userId) {
      auditQuery += ' AND al.user_id = $2';
      values.push(userId);
    }

    auditQuery += ' ORDER BY al.timestamp DESC';

    const result = await query(auditQuery, values);
    return result.rows;

  } catch (error) {
    console.error('Failed to get document audit logs:', error);
    throw error;
  }
};

/**
 * Get user statistics
 */
const getUserStatistics = async (userId) => {
  try {
    const statsQuery = `
      SELECT * FROM document_statistics 
      WHERE user_id = $1
    `;

    const result = await query(statsQuery, [userId]);
    
    if (result.rows.length === 0) {
      // Return default statistics if none exist
      return {
        user_id: userId,
        total_uploaded: 0,
        total_signed: 0,
        total_downloaded: 0,
        total_viewed: 0,
        last_upload_at: null,
        last_sign_at: null,
        last_download_at: null,
        last_view_at: null
      };
    }

    return result.rows[0];

  } catch (error) {
    console.error('Failed to get user statistics:', error);
    throw error;
  }
};

/**
 * Extract user context from request
 */
const extractUserContext = (req) => {
  return {
    userId: req.user?.id || req.user?.userId,
    ipAddress: req.ip || req.connection?.remoteAddress,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID
  };
};

module.exports = {
  AUDIT_ACTIONS,
  logAuditEvent,
  logDocumentUpload,
  logDocumentSigning,
  logDocumentDownload,
  logDocumentView,
  logAccessDenied,
  logUserAuth,
  getUserAuditLogs,
  getDocumentAuditLogs,
  getUserStatistics,
  extractUserContext
};
