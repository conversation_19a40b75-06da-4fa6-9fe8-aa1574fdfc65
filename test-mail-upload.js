const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testMailUpload() {
  try {
    console.log('🧪 Testing Mail upload endpoint...');

    // First, login to get a token
    console.log('1. Logging in as regular user...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.token;
    console.log('✅ Login successful, token received');

    // Create a simple test PDF file (or use an existing one)
    const testPdfPath = path.join(__dirname, 'test-document.pdf');
    
    // Check if test PDF exists, if not create a simple one
    if (!fs.existsSync(testPdfPath)) {
      console.log('2. Creating test PDF file...');
      // Create a minimal PDF content
      const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;
      
      fs.writeFileSync(testPdfPath, pdfContent);
      console.log('✅ Test PDF created');
    }

    // Create form data
    console.log('3. Preparing upload request...');
    const formData = new FormData();
    formData.append('document', fs.createReadStream(testPdfPath), {
      filename: 'test-document.pdf',
      contentType: 'application/pdf'
    });
    formData.append('notes', 'Test document upload from script');

    // Make the upload request
    console.log('4. Uploading document...');
    const uploadResponse = await axios.post(
      'http://localhost:3001/api/documents/upload-for-review',
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        }
      }
    );

    console.log('✅ Upload successful!');
    console.log('Response:', uploadResponse.data);

  } catch (error) {
    console.error('❌ Test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
      console.error('Headers:', error.response.headers);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testMailUpload();
