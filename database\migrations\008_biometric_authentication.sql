-- Migration: Biometric Authentication System
-- Description: Add tables and functions for Face ID/Touch ID biometric authentication
-- Based on WebAuthn standard and biometric authentication best practices

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Biometric credentials table
-- Stores WebAuthn credential metadata (NOT biometric data)
CREATE TABLE IF NOT EXISTS biometric_credentials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    credential_id TEXT NOT NULL UNIQUE, -- WebAuthn credential ID (base64url encoded)
    public_key TEXT NOT NULL, -- Public key for verification (base64url encoded)
    counter BIGINT NOT NULL DEFAULT 0, -- Signature counter for replay attack prevention
    device_type VARCHAR(50) NOT NULL, -- 'platform' or 'cross-platform'
    authenticator_name VA<PERSON>HA<PERSON>(100), -- Human-readable authenticator name
    aaguid UUID, -- Authenticator Attestation GUID
    attestation_format VARCHAR(50), -- Attestation statement format
    attestation_statement JSONB, -- Attestation statement data
    transport_methods TEXT[], -- Available transport methods ['usb', 'nfc', 'ble', 'internal']
    backup_eligible BOOLEAN DEFAULT false, -- Can credential be backed up
    backup_state BOOLEAN DEFAULT false, -- Is credential currently backed up
    is_active BOOLEAN DEFAULT true, -- Is credential currently active
    last_used_at TIMESTAMP, -- Last successful authentication
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_device_type CHECK (device_type IN ('platform', 'cross-platform')),
    CONSTRAINT check_counter_positive CHECK (counter >= 0)
);

-- Biometric authentication logs table
-- Comprehensive audit trail for all biometric activities
CREATE TABLE IF NOT EXISTS biometric_auth_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    credential_id TEXT, -- Reference to biometric_credentials.credential_id
    session_id VARCHAR(255), -- Link to analytics_sessions if available
    event_type VARCHAR(50) NOT NULL, -- 'registration', 'authentication', 'deactivation', 'failure'
    event_status VARCHAR(20) NOT NULL, -- 'success', 'failure', 'error'
    challenge TEXT, -- WebAuthn challenge used (for audit purposes)
    ip_address INET, -- Client IP address
    user_agent TEXT, -- Client user agent
    device_info JSONB, -- Device information (platform, browser, etc.)
    error_code VARCHAR(50), -- Error code if applicable
    error_message TEXT, -- Error message if applicable
    response_time_ms INTEGER, -- Authentication response time in milliseconds
    risk_score DECIMAL(3,2), -- Risk assessment score (0.00-1.00)
    geolocation JSONB, -- Geographic location data if available
    timestamp TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_event_type CHECK (event_type IN ('registration', 'authentication', 'deactivation', 'failure', 'challenge_generated')),
    CONSTRAINT check_event_status CHECK (event_status IN ('success', 'failure', 'error', 'pending')),
    CONSTRAINT check_risk_score CHECK (risk_score >= 0.00 AND risk_score <= 1.00)
);

-- Registered devices table
-- Device registration and trust management
CREATE TABLE IF NOT EXISTS registered_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_id VARCHAR(255) NOT NULL, -- Unique device identifier
    device_name VARCHAR(100), -- User-friendly device name
    platform VARCHAR(50) NOT NULL, -- 'ios', 'android', 'windows', 'macos', 'linux'
    browser VARCHAR(50), -- Browser name if web-based
    browser_version VARCHAR(50), -- Browser version
    os_version VARCHAR(50), -- Operating system version
    device_model VARCHAR(100), -- Device model information
    capabilities JSONB, -- Device biometric capabilities
    trust_level VARCHAR(20) DEFAULT 'unverified', -- 'trusted', 'verified', 'unverified', 'suspicious'
    approval_status VARCHAR(20) DEFAULT 'pending', -- 'approved', 'pending', 'rejected'
    approved_by UUID REFERENCES users(id), -- Admin who approved the device
    approved_at TIMESTAMP, -- When device was approved
    first_seen_at TIMESTAMP DEFAULT NOW(),
    last_seen_at TIMESTAMP DEFAULT NOW(),
    last_ip_address INET, -- Last known IP address
    registration_method VARCHAR(50) DEFAULT 'biometric', -- How device was registered
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_trust_level CHECK (trust_level IN ('trusted', 'verified', 'unverified', 'suspicious')),
    CONSTRAINT check_approval_status CHECK (approval_status IN ('approved', 'pending', 'rejected')),
    CONSTRAINT unique_user_device UNIQUE (user_id, device_id)
);

-- Add biometric preferences to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS biometric_enabled BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS biometric_enrolled_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS preferred_auth_method VARCHAR(20) DEFAULT 'password',
ADD COLUMN IF NOT EXISTS biometric_consent_given BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS biometric_consent_date TIMESTAMP,
ADD COLUMN IF NOT EXISTS failed_biometric_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS biometric_locked_until TIMESTAMP,
ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"biometric_login": true, "new_device": true, "security_alerts": true}'::jsonb;

-- Add constraints for new user columns
ALTER TABLE users
ADD CONSTRAINT IF NOT EXISTS check_preferred_auth_method
    CHECK (preferred_auth_method IN ('password', 'biometric', 'both'));

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_biometric_credentials_user_id ON biometric_credentials(user_id);
CREATE INDEX IF NOT EXISTS idx_biometric_credentials_credential_id ON biometric_credentials(credential_id);
CREATE INDEX IF NOT EXISTS idx_biometric_credentials_active ON biometric_credentials(user_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_biometric_credentials_last_used ON biometric_credentials(last_used_at);

CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_user_id ON biometric_auth_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_credential_id ON biometric_auth_logs(credential_id);
CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_event_type ON biometric_auth_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_timestamp ON biometric_auth_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_session_id ON biometric_auth_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_ip_address ON biometric_auth_logs(ip_address);

CREATE INDEX IF NOT EXISTS idx_registered_devices_user_id ON registered_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_registered_devices_device_id ON registered_devices(device_id);
CREATE INDEX IF NOT EXISTS idx_registered_devices_trust_level ON registered_devices(trust_level);
CREATE INDEX IF NOT EXISTS idx_registered_devices_active ON registered_devices(user_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_registered_devices_last_seen ON registered_devices(last_seen_at);

CREATE INDEX IF NOT EXISTS idx_users_biometric_enabled ON users(biometric_enabled) WHERE biometric_enabled = true;
CREATE INDEX IF NOT EXISTS idx_users_preferred_auth ON users(preferred_auth_method);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_biometric_logs_user_event_time ON biometric_auth_logs(user_id, event_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_devices_user_trust_active ON registered_devices(user_id, trust_level, is_active);

-- Create functions for biometric authentication management
-- Function to check if user has active biometric credentials
CREATE OR REPLACE FUNCTION has_active_biometric_credentials(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM biometric_credentials
        WHERE user_id = user_uuid AND is_active = true
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get user's active biometric credentials
CREATE OR REPLACE FUNCTION get_user_biometric_credentials(user_uuid UUID)
RETURNS TABLE (
    credential_id TEXT,
    device_type VARCHAR(50),
    authenticator_name VARCHAR(100),
    last_used_at TIMESTAMP,
    created_at TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT bc.credential_id, bc.device_type, bc.authenticator_name, bc.last_used_at, bc.created_at
    FROM biometric_credentials bc
    WHERE bc.user_id = user_uuid AND bc.is_active = true
    ORDER BY bc.last_used_at DESC NULLS LAST, bc.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to log biometric authentication events
CREATE OR REPLACE FUNCTION log_biometric_event(
    p_user_id UUID,
    p_credential_id TEXT,
    p_session_id VARCHAR(255),
    p_event_type VARCHAR(50),
    p_event_status VARCHAR(20),
    p_challenge TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_device_info JSONB DEFAULT NULL,
    p_error_code VARCHAR(50) DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL,
    p_response_time_ms INTEGER DEFAULT NULL,
    p_risk_score DECIMAL(3,2) DEFAULT NULL,
    p_geolocation JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO biometric_auth_logs (
        user_id, credential_id, session_id, event_type, event_status,
        challenge, ip_address, user_agent, device_info, error_code,
        error_message, response_time_ms, risk_score, geolocation
    ) VALUES (
        p_user_id, p_credential_id, p_session_id, p_event_type, p_event_status,
        p_challenge, p_ip_address, p_user_agent, p_device_info, p_error_code,
        p_error_message, p_response_time_ms, p_risk_score, p_geolocation
    ) RETURNING id INTO log_id;

    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update credential counter (prevents replay attacks)
CREATE OR REPLACE FUNCTION update_credential_counter(
    p_credential_id TEXT,
    p_new_counter BIGINT
)
RETURNS BOOLEAN AS $$
DECLARE
    current_counter BIGINT;
BEGIN
    -- Get current counter
    SELECT counter INTO current_counter
    FROM biometric_credentials
    WHERE credential_id = p_credential_id AND is_active = true;

    -- Check if new counter is greater than current (prevents replay attacks)
    IF current_counter IS NULL THEN
        RETURN false; -- Credential not found
    END IF;

    IF p_new_counter <= current_counter THEN
        RETURN false; -- Potential replay attack
    END IF;

    -- Update counter and last used timestamp
    UPDATE biometric_credentials
    SET counter = p_new_counter,
        last_used_at = NOW(),
        updated_at = NOW()
    WHERE credential_id = p_credential_id AND is_active = true;

    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- Function to deactivate biometric credential
CREATE OR REPLACE FUNCTION deactivate_biometric_credential(
    p_user_id UUID,
    p_credential_id TEXT,
    p_reason TEXT DEFAULT 'user_requested'
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE biometric_credentials
    SET is_active = false, updated_at = NOW()
    WHERE user_id = p_user_id AND credential_id = p_credential_id;

    -- Log the deactivation
    PERFORM log_biometric_event(
        p_user_id, p_credential_id, NULL, 'deactivation', 'success',
        NULL, NULL, NULL, jsonb_build_object('reason', p_reason)
    );

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to register new device
CREATE OR REPLACE FUNCTION register_device(
    p_user_id UUID,
    p_device_id VARCHAR(255),
    p_device_name VARCHAR(100),
    p_platform VARCHAR(50),
    p_browser VARCHAR(50) DEFAULT NULL,
    p_browser_version VARCHAR(50) DEFAULT NULL,
    p_os_version VARCHAR(50) DEFAULT NULL,
    p_device_model VARCHAR(100) DEFAULT NULL,
    p_capabilities JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    device_uuid UUID;
BEGIN
    INSERT INTO registered_devices (
        user_id, device_id, device_name, platform, browser, browser_version,
        os_version, device_model, capabilities, last_ip_address
    ) VALUES (
        p_user_id, p_device_id, p_device_name, p_platform, p_browser, p_browser_version,
        p_os_version, p_device_model, p_capabilities, p_ip_address
    )
    ON CONFLICT (user_id, device_id)
    DO UPDATE SET
        last_seen_at = NOW(),
        last_ip_address = p_ip_address,
        updated_at = NOW()
    RETURNING id INTO device_uuid;

    RETURN device_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old biometric data (GDPR compliance)
CREATE OR REPLACE FUNCTION cleanup_old_biometric_data()
RETURNS void AS $$
BEGIN
    -- Deactivate credentials not used in 90 days
    UPDATE biometric_credentials
    SET is_active = false, updated_at = NOW()
    WHERE last_used_at < NOW() - INTERVAL '90 days'
    AND is_active = true;

    -- Delete old authentication logs (keep for 1 year for security audit)
    DELETE FROM biometric_auth_logs
    WHERE timestamp < NOW() - INTERVAL '1 year';

    -- Update device last seen for inactive devices
    UPDATE registered_devices
    SET is_active = false, updated_at = NOW()
    WHERE last_seen_at < NOW() - INTERVAL '90 days'
    AND is_active = true;

    -- Log cleanup activity
    INSERT INTO logs (action, details)
    VALUES ('BIOMETRIC_CLEANUP', jsonb_build_object(
        'timestamp', NOW(),
        'description', 'Cleaned up old biometric authentication data'
    ));
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_biometric_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to tables
CREATE TRIGGER trigger_update_biometric_credentials_updated_at
    BEFORE UPDATE ON biometric_credentials
    FOR EACH ROW
    EXECUTE FUNCTION update_biometric_updated_at();

CREATE TRIGGER trigger_update_registered_devices_updated_at
    BEFORE UPDATE ON registered_devices
    FOR EACH ROW
    EXECUTE FUNCTION update_biometric_updated_at();

-- Create views for common biometric queries
CREATE OR REPLACE VIEW biometric_user_summary AS
SELECT
    u.id as user_id,
    u.email,
    u.biometric_enabled,
    u.biometric_enrolled_at,
    u.preferred_auth_method,
    u.failed_biometric_attempts,
    u.biometric_locked_until,
    COUNT(bc.id) as active_credentials,
    MAX(bc.last_used_at) as last_biometric_login,
    COUNT(rd.id) as registered_devices_count,
    MAX(rd.last_seen_at) as last_device_activity
FROM users u
LEFT JOIN biometric_credentials bc ON u.id = bc.user_id AND bc.is_active = true
LEFT JOIN registered_devices rd ON u.id = rd.user_id AND rd.is_active = true
WHERE u.biometric_enabled = true
GROUP BY u.id, u.email, u.biometric_enabled, u.biometric_enrolled_at,
         u.preferred_auth_method, u.failed_biometric_attempts, u.biometric_locked_until;

-- Create view for security monitoring
CREATE OR REPLACE VIEW biometric_security_events AS
SELECT
    bal.timestamp,
    bal.event_type,
    bal.event_status,
    bal.user_id,
    u.email,
    bal.ip_address,
    bal.device_info->>'platform' as platform,
    bal.device_info->>'browser' as browser,
    bal.error_code,
    bal.risk_score,
    rd.trust_level,
    rd.device_name
FROM biometric_auth_logs bal
LEFT JOIN users u ON bal.user_id = u.id
LEFT JOIN registered_devices rd ON bal.user_id = rd.user_id
    AND bal.device_info->>'deviceId' = rd.device_id
WHERE bal.timestamp >= NOW() - INTERVAL '30 days'
ORDER BY bal.timestamp DESC;

-- Add table comments for documentation
COMMENT ON TABLE biometric_credentials IS 'WebAuthn biometric credentials for user authentication (no biometric data stored)';
COMMENT ON TABLE biometric_auth_logs IS 'Comprehensive audit log for all biometric authentication events';
COMMENT ON TABLE registered_devices IS 'Registered devices for biometric authentication with trust management';

COMMENT ON COLUMN biometric_credentials.credential_id IS 'WebAuthn credential ID (base64url encoded)';
COMMENT ON COLUMN biometric_credentials.public_key IS 'Public key for signature verification (base64url encoded)';
COMMENT ON COLUMN biometric_credentials.counter IS 'Signature counter for replay attack prevention';
COMMENT ON COLUMN biometric_credentials.aaguid IS 'Authenticator Attestation GUID from WebAuthn';

COMMENT ON COLUMN biometric_auth_logs.challenge IS 'WebAuthn challenge used for authentication';
COMMENT ON COLUMN biometric_auth_logs.risk_score IS 'Risk assessment score from 0.00 (low risk) to 1.00 (high risk)';
COMMENT ON COLUMN biometric_auth_logs.response_time_ms IS 'Authentication response time in milliseconds';

COMMENT ON COLUMN registered_devices.trust_level IS 'Device trust level: trusted, verified, unverified, suspicious';
COMMENT ON COLUMN registered_devices.capabilities IS 'JSON object containing device biometric capabilities';

COMMENT ON VIEW biometric_user_summary IS 'Summary view of users with biometric authentication enabled';
COMMENT ON VIEW biometric_security_events IS 'Security monitoring view for biometric authentication events';

-- Create a scheduled cleanup job placeholder (requires pg_cron extension)
-- This would typically be configured separately in production
-- SELECT cron.schedule('biometric-cleanup', '0 3 * * 0', 'SELECT cleanup_old_biometric_data();');
