import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../services/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import BiometricLogin from '../components/BiometricLogin';
import { useBiometric } from '../hooks/useBiometric';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showBiometric, setShowBiometric] = useState(false);
  const { login, user } = useAuth();
  const { t } = useLanguage();
  const { isSupported, isAvailable } = useBiometric();
  const navigate = useNavigate();

  // Redirect if already logged in
  React.useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await login(email, password);
      navigate('/dashboard');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle biometric login success
  const handleBiometricSuccess = (token: string, refreshToken: string, user: any) => {
    // Store tokens and user data
    localStorage.setItem('token', token);
    localStorage.setItem('refreshToken', refreshToken);
    localStorage.setItem('user', JSON.stringify(user));

    // Navigate to dashboard
    navigate('/dashboard');
    window.location.reload(); // Refresh to update auth context
  };

  // Handle biometric login error
  const handleBiometricError = (message: string) => {
    setError(message);
  };

  // Check if we should show biometric option
  React.useEffect(() => {
    if (email && isSupported && isAvailable) {
      setShowBiometric(true);
    } else {
      setShowBiometric(false);
    }
  }, [email, isSupported, isAvailable]);

  return (
    <div className="max-w-md mx-auto mt-8" dir="rtl">
      <div className="bg-white p-8 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold text-center mb-6">{t.auth.login}</h2>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="email" className="block text-gray-700 text-sm font-bold mb-2">
              {t.auth.email}
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
              dir="ltr"
            />
          </div>

          <div className="mb-6">
            <label htmlFor="password" className="block text-gray-700 text-sm font-bold mb-2">
              {t.auth.password}
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? t.auth.loggingIn : t.auth.loginButton}
          </button>
        </form>

        {/* Biometric Login Option */}
        {showBiometric && (
          <div className="mt-4">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">أو</span>
              </div>
            </div>
            <div className="mt-4">
              <BiometricLogin
                email={email}
                onSuccess={handleBiometricSuccess}
                onError={handleBiometricError}
              />
            </div>
          </div>
        )}

        <p className="text-center mt-4 text-gray-600">
          {t.auth.noAccount}{' '}
          <Link to="/register" className="text-blue-500 hover:text-blue-600">
            {t.auth.registerHere}
          </Link>
        </p>
      </div>
    </div>
  );
};

export default Login;
