const axios = require('axios');

async function testAuthenticationFlow() {
  try {
    console.log('🔐 Testing Backend Authentication Flow...\n');
    
    // Test 1: Registration
    console.log('1. Testing user registration...');
    try {
      const registerResponse = await axios.post('http://localhost:3001/api/auth/register', {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('✅ Registration successful:', registerResponse.data.success);
      console.log('   Token received:', registerResponse.data.token ? 'Yes' : 'No');
      console.log('   User ID:', registerResponse.data.user?.id);
    } catch (regError) {
      if (regError.response?.status === 400 && regError.response.data.message?.includes('مسجل بالفعل')) {
        console.log('ℹ️  User already exists, proceeding with login test...');
      } else {
        console.error('❌ Registration failed:', regError.response?.data || regError.message);
      }
    }
    
    // Test 2: Login
    console.log('\n2. Testing user login...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>', 
      password: 'password123'
    });
    console.log('✅ Login successful:', loginResponse.data.success);
    console.log('   Token received:', loginResponse.data.token ? 'Yes' : 'No');
    console.log('   User email:', loginResponse.data.user?.email);
    
    const token = loginResponse.data.token;
    
    // Test 3: Protected endpoint - Signatures
    console.log('\n3. Testing protected endpoint - Signatures...');
    const signaturesResponse = await axios.get('http://localhost:3001/api/signatures', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Signatures endpoint accessible:', signaturesResponse.status === 200);
    console.log('   Signatures found:', signaturesResponse.data.signatures?.length || 0);
    
    // Test 4: Protected endpoint - Documents
    console.log('\n4. Testing protected endpoint - Documents...');
    const documentsResponse = await axios.get('http://localhost:3001/api/documents?page=1&limit=5', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Documents endpoint accessible:', documentsResponse.status === 200);
    console.log('   Documents found:', documentsResponse.data.documents?.length || 0);
    
    // Test 5: Token validation
    console.log('\n5. Testing token validation...');
    const profileResponse = await axios.get('http://localhost:3001/api/auth/profile', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Profile endpoint accessible:', profileResponse.status === 200);
    console.log('   Profile email:', profileResponse.data.user?.email);
    
    console.log('\n🎉 All authentication tests passed!');
    console.log('\n📋 Test Results Summary:');
    console.log('   ✅ User registration/login: Working');
    console.log('   ✅ JWT token generation: Working');
    console.log('   ✅ Protected endpoints: Accessible');
    console.log('   ✅ Token validation: Working');
    
    return { success: true, token, user: loginResponse.data.user };
    
  } catch (error) {
    console.error('\n❌ Authentication test failed:');
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
      console.error('   Headers:', error.response.headers);
    } else {
      console.error('   Error:', error.message);
    }
    
    console.log('\n🔧 Troubleshooting steps:');
    console.log('   1. Check if backend server is running on port 3001');
    console.log('   2. Verify database connection is working');
    console.log('   3. Check JWT_SECRET is configured in .env');
    console.log('   4. Verify user table exists and is accessible');
    
    return { success: false, error: error.message };
  }
}

// Run the test
if (require.main === module) {
  testAuthenticationFlow()
    .then((result) => {
      if (result.success) {
        console.log('\n✅ Authentication system is working correctly!');
        process.exit(0);
      } else {
        console.log('\n❌ Authentication system has issues that need to be fixed.');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Unexpected error:', error.message);
      process.exit(1);
    });
}

module.exports = { testAuthenticationFlow };
