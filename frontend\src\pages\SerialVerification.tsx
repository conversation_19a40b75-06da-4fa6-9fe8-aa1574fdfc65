import React, { useState } from 'react';
import { documentAPI } from '../services/api';
import { useAuth } from '../services/AuthContext';
import AccessDenied from '../components/AccessDenied';

interface VerificationResult {
  isValid: boolean;
  document?: {
    id: string;
    original_filename: string;
    signed_filename: string;
    serial_number: string;
    signed_date: string;
    file_size: number;
    user_id: string;
    user_email: string;
    digital_signature: string;
    signature_coordinates: any;
  };
  error?: string;
}

const SerialVerification: React.FC = () => {
  const { hasPermission } = useAuth();
  const [serialNumber, setSerialNumber] = useState('');
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Check if user has permission to verify documents
  if (!hasPermission('verify_documents')) {
    return <AccessDenied feature="التحقق من المستندات" />;
  }

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!serialNumber.trim()) {
      setError('يرجى إدخال الرقم التسلسلي');
      return;
    }

    setLoading(true);
    setError('');
    setVerificationResult(null);

    try {
      // Note: We'll need to create this API endpoint
      const response = await documentAPI.verifySerial(serialNumber.trim());
      setVerificationResult(response.data);
    } catch (error: any) {
      console.error('Verification error:', error);
      if (error.response?.status === 404) {
        setVerificationResult({
          isValid: false,
          error: 'الرقم التسلسلي غير موجود في النظام'
        });
      } else {
        setError('فشل في التحقق من الرقم التسلسلي. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      console.log('تم نسخ النص إلى الحافظة');
    });
  };

  const downloadDocument = async (documentId: string) => {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const response = await documentAPI.download(documentId);
      // Handle download logic here
      console.log('تحميل المستند:', documentId);
    } catch (error) {
      console.error('فشل في تحميل المستند:', error);
    }
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8 font-['Almarai'] max-w-6xl" dir="rtl">
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2 leading-tight">
          التحقق من الرقم التسلسلي
        </h1>
        <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
          تحقق من صحة المستندات الموقعة ومعلومات الموقع
        </p>
      </div>

      {/* Verification Form */}
      <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-6 sm:mb-8">
        <form onSubmit={handleVerification} className="space-y-4 sm:space-y-6">
          <div>
            <label htmlFor="serialNumber" className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
              الرقم التسلسلي للمستند
            </label>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <input
                type="text"
                id="serialNumber"
                value={serialNumber}
                onChange={(e) => setSerialNumber(e.target.value)}
                placeholder="أدخل الرقم التسلسلي (مثال: DOC123456789)"
                className="flex-1 px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm sm:text-base"
                disabled={loading}
              />
              <button
                type="submit"
                disabled={loading || !serialNumber.trim()}
                className="w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 text-sm sm:text-base font-medium"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    <span className="hidden sm:inline">جاري التحقق...</span>
                    <span className="sm:hidden">جاري التحقق</span>
                  </div>
                ) : (
                  'تحقق'
                )}
              </button>
            </div>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-3 sm:px-4 py-2 sm:py-3 rounded text-sm sm:text-base">
              {error}
            </div>
          )}
        </form>
      </div>

      {/* Verification Results */}
      {verificationResult && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {verificationResult.isValid && verificationResult.document ? (
            // Valid Document
            <div className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center mb-4 sm:mb-6">
                <div className="flex-shrink-0 mb-3 sm:mb-0">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto sm:mx-0">
                    <svg className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="sm:mr-4 text-center sm:text-right">
                  <h3 className="text-base sm:text-lg font-semibold text-green-800">مستند صالح ومُتحقق منه</h3>
                  <p className="text-sm sm:text-base text-green-600">تم العثور على المستند وهو موقع رقمياً</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                {/* Document Information */}
                <div className="space-y-3 sm:space-y-4">
                  <h4 className="text-base sm:text-lg font-semibold text-gray-900 border-b pb-2">معلومات المستند</h4>

                  <div className="space-y-3 sm:space-y-4">
                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-500 mb-1">الرقم التسلسلي</label>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <span className="text-xs sm:text-sm font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1">
                          {verificationResult.document.serial_number}
                        </span>
                        <button
                          onClick={() => copyToClipboard(verificationResult.document!.serial_number)}
                          className="self-start sm:self-auto p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200"
                          title="نسخ الرقم التسلسلي"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-500 mb-1">اسم الملف الأصلي</label>
                      <p className="text-xs sm:text-sm text-gray-900 break-all">{verificationResult.document.original_filename}</p>
                    </div>

                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-500 mb-1">تاريخ التوقيع</label>
                      <p className="text-xs sm:text-sm text-gray-900">{formatDate(verificationResult.document.signed_date)}</p>
                    </div>

                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-500 mb-1">حجم الملف</label>
                      <p className="text-xs sm:text-sm text-gray-900">{formatFileSize(verificationResult.document.file_size)}</p>
                    </div>
                  </div>
                </div>

                {/* User Information */}
                <div className="space-y-3 sm:space-y-4">
                  <h4 className="text-base sm:text-lg font-semibold text-gray-900 border-b pb-2">معلومات الموقع</h4>

                  <div className="space-y-3 sm:space-y-4">
                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-500 mb-1">معرف المستخدم</label>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <span className="text-xs sm:text-sm font-mono bg-blue-100 px-2 py-1 rounded break-all flex-1">
                          {verificationResult.document.user_id}
                        </span>
                        <button
                          onClick={() => copyToClipboard(verificationResult.document!.user_id)}
                          className="self-start sm:self-auto p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200"
                          title="نسخ معرف المستخدم"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-500 mb-1">البريد الإلكتروني</label>
                      <p className="text-xs sm:text-sm text-gray-900 break-all">{verificationResult.document.user_email}</p>
                    </div>

                    <div>
                      <label className="block text-xs sm:text-sm font-medium text-gray-500 mb-1">التوقيع الرقمي</label>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <span className="text-xs font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1">
                          <span className="sm:hidden">{verificationResult.document.digital_signature.substring(0, 16)}...</span>
                          <span className="hidden sm:inline">{verificationResult.document.digital_signature.substring(0, 32)}...</span>
                        </span>
                        <button
                          onClick={() => copyToClipboard(verificationResult.document!.digital_signature)}
                          className="self-start sm:self-auto p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200"
                          title="نسخ التوقيع الرقمي"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-4 sm:mt-6 pt-4 sm:pt-6 border-t border-gray-200">
                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                  <button
                    onClick={() => downloadDocument(verificationResult.document!.id)}
                    className="w-full sm:w-auto px-4 py-2 sm:py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200 text-sm sm:text-base font-medium flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    تحميل المستند
                  </button>
                  <button
                    onClick={() => copyToClipboard(JSON.stringify(verificationResult.document, null, 2))}
                    className="w-full sm:w-auto px-4 py-2 sm:py-3 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200 text-sm sm:text-base font-medium flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    نسخ جميع المعلومات
                  </button>
                </div>
              </div>
            </div>
          ) : (
            // Invalid Document
            <div className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                <div className="flex-shrink-0 mb-3 sm:mb-0">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto sm:mx-0">
                    <svg className="w-5 h-5 sm:w-6 sm:h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                </div>
                <div className="sm:mr-4 text-center sm:text-right">
                  <h3 className="text-base sm:text-lg font-semibold text-red-800">رقم تسلسلي غير صالح</h3>
                  <p className="text-sm sm:text-base text-red-600">
                    {verificationResult.error || 'لم يتم العثور على مستند بهذا الرقم التسلسلي'}
                  </p>
                </div>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-md p-3 sm:p-4">
                <h4 className="text-xs sm:text-sm font-semibold text-red-800 mb-2">الأسباب المحتملة:</h4>
                <ul className="text-xs sm:text-sm text-red-700 space-y-1">
                  <li>• الرقم التسلسلي غير صحيح أو مكتوب خطأ</li>
                  <li>• المستند لم يتم توقيعه من خلال هذا النظام</li>
                  <li>• المستند قد يكون مزوراً أو معدلاً</li>
                  <li>• انتهت صلاحية المستند أو تم إلغاؤه</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      )}


    </div>
  );
};

export default SerialVerification;
