/**
 * Frontend Biometric Security Validator
 * Validates WebAuthn implementation and security on the client side
 */

export interface SecurityValidationResult {
  passed: number;
  failed: number;
  warnings: number;
  tests: Array<{
    level: 'PASS' | 'FAIL' | 'WARN' | 'INFO';
    test: string;
    message: string;
    details?: any;
  }>;
}

export class BiometricSecurityValidator {
  private results: SecurityValidationResult = {
    passed: 0,
    failed: 0,
    warnings: 0,
    tests: []
  };

  private log(level: 'PASS' | 'FAIL' | 'WARN' | 'INFO', test: string, message: string, details?: any) {
    this.results.tests.push({
      level,
      test,
      message,
      details
    });

    if (level === 'PASS') this.results.passed++;
    else if (level === 'FAIL') this.results.failed++;
    else if (level === 'WARN') this.results.warnings++;

    console.log(`[${level}] ${test}: ${message}`, details || '');
  }

  /**
   * Validate WebAuthn API availability and security
   */
  validateWebAuthnAPI(): void {
    console.log('=== WebAuthn API Validation ===');

    // Check basic WebAuthn support
    if (window.PublicKeyCredential) {
      this.log('PASS', 'WebAuthn Support', 'PublicKeyCredential API available');
    } else {
      this.log('FAIL', 'WebAuthn Support', 'PublicKeyCredential API not available');
      return;
    }

    // Check credentials API
    if (navigator.credentials) {
      this.log('PASS', 'Credentials API', 'navigator.credentials available');
    } else {
      this.log('FAIL', 'Credentials API', 'navigator.credentials not available');
    }

    // Check create and get methods
    if (navigator.credentials.create) {
      this.log('PASS', 'Credential Creation', 'credentials.create() available');
    } else {
      this.log('FAIL', 'Credential Creation', 'credentials.create() not available');
    }

    if (navigator.credentials.get) {
      this.log('PASS', 'Credential Retrieval', 'credentials.get() available');
    } else {
      this.log('FAIL', 'Credential Retrieval', 'credentials.get() not available');
    }

    // Check for conditional mediation (autofill)
    if ('isConditionalMediationAvailable' in PublicKeyCredential) {
      this.log('PASS', 'Conditional Mediation', 'Conditional mediation API available');
    } else {
      this.log('WARN', 'Conditional Mediation', 'Conditional mediation not available');
    }
  }

  /**
   * Validate platform authenticator availability
   */
  async validatePlatformAuthenticator(): Promise<void> {
    console.log('=== Platform Authenticator Validation ===');

    try {
      const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      
      if (available) {
        this.log('PASS', 'Platform Authenticator', 'User verifying platform authenticator available');
      } else {
        this.log('WARN', 'Platform Authenticator', 'No user verifying platform authenticator available');
      }
    } catch (error) {
      this.log('FAIL', 'Platform Authenticator', 'Error checking platform authenticator', error);
    }
  }

  /**
   * Validate secure context requirements
   */
  validateSecureContext(): void {
    console.log('=== Secure Context Validation ===');

    // Check if running in secure context
    if (window.isSecureContext) {
      this.log('PASS', 'Secure Context', 'Running in secure context (HTTPS or localhost)');
    } else {
      this.log('FAIL', 'Secure Context', 'Not running in secure context - WebAuthn requires HTTPS');
    }

    // Check origin
    const origin = window.location.origin;
    if (origin.startsWith('https://') || origin.startsWith('http://localhost')) {
      this.log('PASS', 'Origin Security', 'Origin is secure for WebAuthn');
    } else {
      this.log('FAIL', 'Origin Security', 'Origin is not secure for WebAuthn');
    }
  }

  /**
   * Validate browser security features
   */
  validateBrowserSecurity(): void {
    console.log('=== Browser Security Validation ===');

    // Check for CSP
    const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
    if (metaTags.length > 0) {
      this.log('PASS', 'Content Security Policy', 'CSP meta tag found');
    } else {
      this.log('WARN', 'Content Security Policy', 'No CSP meta tag found');
    }

    // Check for HTTPS enforcement
    if (location.protocol === 'https:' || location.hostname === 'localhost') {
      this.log('PASS', 'HTTPS Enforcement', 'Using secure protocol');
    } else {
      this.log('FAIL', 'HTTPS Enforcement', 'Not using HTTPS in production');
    }

    // Check for mixed content
    if (location.protocol === 'https:') {
      const insecureResources = Array.from(document.querySelectorAll('script, img, link')).filter(
        (element: any) => element.src && element.src.startsWith('http://')
      );

      if (insecureResources.length === 0) {
        this.log('PASS', 'Mixed Content', 'No insecure resources detected');
      } else {
        this.log('WARN', 'Mixed Content', `${insecureResources.length} insecure resources found`);
      }
    }
  }

  /**
   * Validate challenge generation and handling
   */
  validateChallengeHandling(): void {
    console.log('=== Challenge Handling Validation ===');

    // Test base64url encoding/decoding
    try {
      const testData = new Uint8Array(32);
      crypto.getRandomValues(testData);
      
      // Test our base64url implementation
      const encoded = this.arrayBufferToBase64url(testData.buffer);
      const decoded = this.base64urlToArrayBuffer(encoded);
      
      if (decoded.byteLength === testData.byteLength) {
        this.log('PASS', 'Base64URL Encoding', 'Base64URL encoding/decoding works correctly');
      } else {
        this.log('FAIL', 'Base64URL Encoding', 'Base64URL encoding/decoding failed');
      }
    } catch (error) {
      this.log('FAIL', 'Base64URL Encoding', 'Error testing base64url encoding', error);
    }

    // Test crypto.getRandomValues
    try {
      const randomArray = new Uint8Array(32);
      crypto.getRandomValues(randomArray);
      
      // Check if all values are not zero (very unlikely with proper randomness)
      const allZero = randomArray.every(byte => byte === 0);
      if (!allZero) {
        this.log('PASS', 'Random Generation', 'crypto.getRandomValues produces random data');
      } else {
        this.log('FAIL', 'Random Generation', 'crypto.getRandomValues may not be working properly');
      }
    } catch (error) {
      this.log('FAIL', 'Random Generation', 'crypto.getRandomValues not available', error);
    }
  }

  /**
   * Validate data handling security
   */
  validateDataHandling(): void {
    console.log('=== Data Handling Validation ===');

    // Check localStorage security
    try {
      const testKey = 'biometric_test_key';
      const testValue = 'test_value';
      
      localStorage.setItem(testKey, testValue);
      const retrieved = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);
      
      if (retrieved === testValue) {
        this.log('PASS', 'Local Storage', 'localStorage is functional');
      } else {
        this.log('WARN', 'Local Storage', 'localStorage may not be working properly');
      }
    } catch (error) {
      this.log('WARN', 'Local Storage', 'localStorage not available or restricted', error);
    }

    // Check for sensitive data exposure
    const sensitivePatterns = [
      /password/i,
      /secret/i,
      /private.*key/i,
      /biometric.*template/i
    ];

    let foundSensitive = false;
    try {
      const scripts = Array.from(document.scripts);
      for (const script of scripts) {
        if (script.textContent) {
          for (const pattern of sensitivePatterns) {
            if (pattern.test(script.textContent)) {
              foundSensitive = true;
              break;
            }
          }
        }
      }

      if (!foundSensitive) {
        this.log('PASS', 'Sensitive Data', 'No sensitive data patterns found in scripts');
      } else {
        this.log('WARN', 'Sensitive Data', 'Potential sensitive data found in scripts');
      }
    } catch (error) {
      this.log('WARN', 'Sensitive Data', 'Could not scan for sensitive data', error);
    }
  }

  /**
   * Validate mobile security features
   */
  validateMobileSecurity(): void {
    console.log('=== Mobile Security Validation ===');

    const userAgent = navigator.userAgent;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

    if (isMobile) {
      // Check for iOS security features
      if (/iPhone|iPad|iPod/.test(userAgent)) {
        this.log('INFO', 'iOS Detection', 'iOS device detected');
        
        // Check for Face ID/Touch ID indicators
        if (userAgent.includes('iPhone')) {
          this.log('INFO', 'iOS Biometrics', 'iPhone detected - Face ID/Touch ID may be available');
        }
      }

      // Check for Android security features
      if (/Android/.test(userAgent)) {
        this.log('INFO', 'Android Detection', 'Android device detected');
        this.log('INFO', 'Android Biometrics', 'Android fingerprint authentication may be available');
      }

      // Check for PWA features
      if (window.matchMedia('(display-mode: standalone)').matches) {
        this.log('PASS', 'PWA Security', 'Running as PWA - enhanced security context');
      } else {
        this.log('INFO', 'PWA Security', 'Not running as PWA');
      }
    } else {
      this.log('INFO', 'Mobile Security', 'Desktop environment detected');
    }
  }

  /**
   * Helper method for base64url encoding
   */
  private arrayBufferToBase64url(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Helper method for base64url decoding
   */
  private base64urlToArrayBuffer(base64url: string): ArrayBuffer {
    const base64 = base64url
      .replace(/-/g, '+')
      .replace(/_/g, '/');
    
    const padded = base64.padEnd(base64.length + (4 - base64.length % 4) % 4, '=');
    const binary = atob(padded);
    const bytes = new Uint8Array(binary.length);
    
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    
    return bytes.buffer;
  }

  /**
   * Run all validation tests
   */
  async runValidation(): Promise<SecurityValidationResult> {
    console.log('🔒 Frontend Biometric Security Validation Starting...\n');

    this.validateWebAuthnAPI();
    await this.validatePlatformAuthenticator();
    this.validateSecureContext();
    this.validateBrowserSecurity();
    this.validateChallengeHandling();
    this.validateDataHandling();
    this.validateMobileSecurity();

    console.log('\n=== Validation Summary ===');
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⚠️ Warnings: ${this.results.warnings}`);
    console.log(`📊 Total Tests: ${this.results.tests.length}`);

    return this.results;
  }

  /**
   * Get validation results
   */
  getResults(): SecurityValidationResult {
    return this.results;
  }
}

// Export singleton instance for easy use
export const biometricSecurityValidator = new BiometricSecurityValidator();
export default biometricSecurityValidator;
