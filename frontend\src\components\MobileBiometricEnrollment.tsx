import React, { useState, useEffect } from 'react';
import { useBiometric } from '../hooks/useBiometric';
import mobileDetection from '../utils/mobileDetection';

interface MobileBiometricEnrollmentProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  className?: string;
}

type EnrollmentStep = 'intro' | 'permissions' | 'enrollment' | 'success' | 'error';

const MobileBiometricEnrollment: React.FC<MobileBiometricEnrollmentProps> = ({
  onSuccess,
  onCancel,
  className = ''
}) => {
  const {
    capabilities,
    isSupported,
    isAvailable,
    isLoading,
    error,
    registerBiometric,
    checkCapabilities,
    clearError
  } = useBiometric();

  const [currentStep, setCurrentStep] = useState<EnrollmentStep>('intro');
  const [enrollmentError, setEnrollmentError] = useState<string | null>(null);
  const [isEnrolling, setIsEnrolling] = useState(false);
  const mobileCapabilities = mobileDetection.getCapabilities();

  // Check capabilities on mount
  useEffect(() => {
    checkCapabilities();
  }, [checkCapabilities]);

  // Update step based on capabilities
  useEffect(() => {
    if (capabilities) {
      if (!capabilities.isSupported) {
        setCurrentStep('error');
        setEnrollmentError('المصادقة البيومترية غير مدعومة في هذا المتصفح');
      } else if (!capabilities.isAvailable) {
        setCurrentStep('error');
        setEnrollmentError('المصادقة البيومترية غير متاحة على هذا الجهاز');
      }
    }
  }, [capabilities]);

  // Handle enrollment process with mobile optimizations
  const handleEnrollment = async () => {
    try {
      setIsEnrolling(true);
      setEnrollmentError(null);
      clearError();

      // Trigger haptic feedback
      mobileDetection.triggerHapticFeedback('warning');

      const result = await registerBiometric();

      if (result.success) {
        mobileDetection.triggerHapticFeedback('success');
        setCurrentStep('success');
        setTimeout(() => {
          onSuccess?.();
        }, 2000);
      } else {
        mobileDetection.triggerHapticFeedback('error');
        setCurrentStep('error');
        setEnrollmentError(result.message);
      }
    } catch (err: any) {
      mobileDetection.triggerHapticFeedback('error');
      setCurrentStep('error');
      setEnrollmentError(err.message || 'فشل في تسجيل المصادقة البيومترية');
    } finally {
      setIsEnrolling(false);
    }
  };

  // Get platform-specific information
  const getBiometricInfo = () => {
    const typeName = mobileDetection.getBiometricTypeName();
    const icon = mobileDetection.getBiometricIcon();
    
    let description = '';
    let setupInstructions = '';
    
    if (mobileCapabilities.supportsFaceID) {
      description = 'استخدم Face ID للدخول السريع والآمن إلى حسابك';
      setupInstructions = 'سيطلب منك النظر إلى الكاميرا الأمامية لتسجيل وجهك';
    } else if (mobileCapabilities.supportsTouchID) {
      description = 'استخدم Touch ID للدخول السريع والآمن إلى حسابك';
      setupInstructions = 'سيطلب منك وضع إصبعك على زر الهوم لتسجيل بصمتك';
    } else if (mobileCapabilities.supportsFingerprint) {
      description = 'استخدم بصمة الإصبع للدخول السريع والآمن إلى حسابك';
      setupInstructions = 'سيطلب منك وضع إصبعك على مستشعر البصمة';
    } else {
      description = 'استخدم المصادقة البيومترية للدخول السريع والآمن';
      setupInstructions = 'اتبع التعليمات التي ستظهر على جهازك';
    }

    return {
      name: typeName,
      icon,
      description,
      setupInstructions
    };
  };

  const biometricInfo = getBiometricInfo();

  const renderStep = () => {
    switch (currentStep) {
      case 'intro':
        return (
          <div className="text-center px-4" style={{ direction: 'rtl' }}>
            <div className="text-8xl mb-6">{biometricInfo.icon}</div>
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              إعداد {biometricInfo.name}
            </h2>
            <p className="text-gray-600 mb-8 leading-relaxed text-lg">
              {biometricInfo.description}
            </p>
            
            {/* Benefits list */}
            <div className="bg-blue-50 rounded-xl p-6 mb-8 text-right">
              <h3 className="font-semibold text-blue-800 mb-4">المزايا:</h3>
              <ul className="text-blue-700 space-y-2">
                <li className="flex items-center gap-3">
                  <span className="text-green-500">✓</span>
                  <span>دخول سريع في ثانية واحدة</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="text-green-500">✓</span>
                  <span>أمان عالي ومشفر</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="text-green-500">✓</span>
                  <span>لا حاجة لتذكر كلمة المرور</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="text-green-500">✓</span>
                  <span>بياناتك محفوظة على جهازك فقط</span>
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <button
                onClick={() => setCurrentStep('permissions')}
                className="w-full bg-blue-600 text-white py-4 px-6 rounded-xl hover:bg-blue-700 transition-colors text-lg font-medium"
              >
                المتابعة
              </button>
              <button
                onClick={onCancel}
                className="w-full bg-gray-200 text-gray-700 py-4 px-6 rounded-xl hover:bg-gray-300 transition-colors text-lg"
              >
                إلغاء
              </button>
            </div>
          </div>
        );

      case 'permissions':
        return (
          <div className="text-center px-4" style={{ direction: 'rtl' }}>
            <div className="text-6xl mb-6">🛡️</div>
            <h2 className="text-2xl font-bold mb-4 text-gray-800">الخصوصية والأمان</h2>
            
            <div className="bg-green-50 rounded-xl p-6 mb-6 text-right">
              <h3 className="font-semibold text-green-800 mb-4">ضمانات الأمان:</h3>
              <ul className="text-green-700 space-y-3">
                <li className="flex items-start gap-3">
                  <span className="text-green-500 mt-1">🔒</span>
                  <span>بياناتك البيومترية لا تغادر جهازك أبداً</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-green-500 mt-1">🔑</span>
                  <span>نحفظ فقط مفتاح التشفير العام</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-green-500 mt-1">⚙️</span>
                  <span>يمكنك إلغاء التفعيل في أي وقت</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-green-500 mt-1">🔄</span>
                  <span>كلمة المرور تبقى متاحة كبديل</span>
                </li>
              </ul>
            </div>

            <p className="text-gray-600 mb-8 text-lg leading-relaxed">
              {biometricInfo.setupInstructions}
            </p>

            <div className="space-y-4">
              <button
                onClick={() => setCurrentStep('enrollment')}
                className="w-full bg-blue-600 text-white py-4 px-6 rounded-xl hover:bg-blue-700 transition-colors text-lg font-medium"
              >
                أوافق وأريد المتابعة
              </button>
              <button
                onClick={onCancel}
                className="w-full bg-gray-200 text-gray-700 py-4 px-6 rounded-xl hover:bg-gray-300 transition-colors text-lg"
              >
                إلغاء
              </button>
            </div>
          </div>
        );

      case 'enrollment':
        return (
          <div className="text-center px-4" style={{ direction: 'rtl' }}>
            <div className={`text-8xl mb-6 ${isEnrolling ? 'animate-pulse' : ''}`}>
              {biometricInfo.icon}
            </div>
            <h2 className="text-2xl font-bold mb-4 text-gray-800">
              تسجيل {biometricInfo.name}
            </h2>
            
            {isEnrolling ? (
              <div className="space-y-6">
                <p className="text-gray-600 text-lg">
                  {biometricInfo.setupInstructions}
                </p>
                <div className="bg-blue-50 rounded-xl p-6">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-blue-700 font-medium">
                    اتبع التعليمات على جهازك...
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <p className="text-gray-600 mb-8 text-lg leading-relaxed">
                  اضغط على الزر أدناه لبدء عملية التسجيل
                </p>
                <button
                  onClick={handleEnrollment}
                  className="w-full bg-blue-600 text-white py-4 px-6 rounded-xl hover:bg-blue-700 transition-colors text-xl font-medium"
                >
                  بدء التسجيل
                </button>
              </div>
            )}
          </div>
        );

      case 'success':
        return (
          <div className="text-center px-4" style={{ direction: 'rtl' }}>
            <div className="text-8xl mb-6">✅</div>
            <h2 className="text-2xl font-bold mb-4 text-green-700">
              تم التسجيل بنجاح!
            </h2>
            <p className="text-gray-600 mb-8 text-lg leading-relaxed">
              تم تفعيل {biometricInfo.name} بنجاح. يمكنك الآن استخدامه لتسجيل الدخول بسرعة وأمان.
            </p>
            <div className="bg-green-50 rounded-xl p-6">
              <div className="animate-pulse text-green-700 font-medium">
                سيتم إعادة توجيهك تلقائياً...
              </div>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="text-center px-4" style={{ direction: 'rtl' }}>
            <div className="text-8xl mb-6">❌</div>
            <h2 className="text-2xl font-bold mb-4 text-red-700">
              فشل في التسجيل
            </h2>
            <div className="bg-red-50 rounded-xl p-6 mb-8">
              <p className="text-red-700 text-lg">
                {enrollmentError || error || 'حدث خطأ غير متوقع'}
              </p>
            </div>
            <div className="space-y-4">
              <button
                onClick={() => {
                  setCurrentStep('enrollment');
                  setEnrollmentError(null);
                  clearError();
                }}
                className="w-full bg-blue-600 text-white py-4 px-6 rounded-xl hover:bg-blue-700 transition-colors text-lg font-medium"
              >
                إعادة المحاولة
              </button>
              <button
                onClick={onCancel}
                className="w-full bg-gray-200 text-gray-700 py-4 px-6 rounded-xl hover:bg-gray-300 transition-colors text-lg"
              >
                إلغاء
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`mobile-biometric-enrollment ${className}`}>
      <div className="bg-white rounded-2xl shadow-xl p-6 max-w-md mx-auto min-h-screen flex items-center">
        <div className="w-full">
          {renderStep()}
        </div>
      </div>

      {/* Mobile-specific styles */}
      <style jsx>{`
        .mobile-biometric-enrollment {
          padding-top: env(safe-area-inset-top);
          padding-bottom: env(safe-area-inset-bottom);
          padding-left: env(safe-area-inset-left);
          padding-right: env(safe-area-inset-right);
        }
        
        @media (display-mode: standalone) {
          .mobile-biometric-enrollment {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
        }
        
        /* iOS specific styles */
        @supports (-webkit-touch-callout: none) {
          .mobile-biometric-enrollment button {
            -webkit-appearance: none;
            -webkit-tap-highlight-color: transparent;
          }
        }
      `}</style>
    </div>
  );
};

export default MobileBiometricEnrollment;
