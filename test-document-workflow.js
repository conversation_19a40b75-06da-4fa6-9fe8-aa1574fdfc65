const axios = require('axios');
const FormData = require('form-data');

async function testDocumentWorkflow() {
  try {
    console.log('🔄 Testing Complete Document Workflow...\n');

    // 1. Test Regular User Workflow
    console.log('1. Testing Regular User Workflow:');
    console.log('=' .repeat(50));
    
    const userLoginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const userToken = userLoginResponse.data.token;
    console.log('✅ Regular user logged in');

    // Test access to document signing endpoint (should fail)
    console.log('\n📝 Testing regular user access to document signing...');
    try {
      const signingResponse = await axios.get(
        'http://localhost:3001/api/signatures',
        {
          headers: {
            'Authorization': `Bearer ${userToken}`
          }
        }
      );
      console.log('❌ SECURITY ISSUE: Regular user can access signing endpoints!');
      console.log('   Response:', signingResponse.data);
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Regular user correctly blocked from signing endpoints');
      } else {
        console.log('⚠️ Unexpected error:', error.response?.status, error.response?.data);
      }
    }

    // Test document upload for review (should work)
    console.log('\n📤 Testing document upload for admin review...');
    const formData = new FormData();
    const pdfContent = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF');
    
    formData.append('document', pdfContent, {
      filename: 'user-workflow-test.pdf',
      contentType: 'application/pdf'
    });
    formData.append('notes', 'Test document from regular user workflow');

    try {
      const uploadResponse = await axios.post(
        'http://localhost:3001/api/documents/upload-for-review',
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${userToken}`
          }
        }
      );
      console.log('✅ Regular user can upload documents for review');
      console.log('   Document ID:', uploadResponse.data.document.id);
    } catch (error) {
      console.log('❌ Regular user cannot upload documents:', error.response?.data);
    }

    // 2. Test Admin Workflow
    console.log('\n\n2. Testing Admin Workflow:');
    console.log('=' .repeat(50));
    
    const adminLoginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const adminToken = adminLoginResponse.data.token;
    console.log('✅ Admin user logged in');

    // Test access to pending documents (should work)
    console.log('\n📋 Testing admin access to pending documents...');
    try {
      const pendingResponse = await axios.get(
        'http://localhost:3001/api/documents/pending',
        {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        }
      );
      console.log('✅ Admin can access pending documents');
      console.log('   Number of pending documents:', pendingResponse.data.documents?.length || 0);
    } catch (error) {
      console.log('❌ Admin cannot access pending documents:', error.response?.data);
    }

    // Test access to signatures (should work)
    console.log('\n🖋️ Testing admin access to signatures...');
    try {
      const signaturesResponse = await axios.get(
        'http://localhost:3001/api/signatures',
        {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        }
      );
      console.log('✅ Admin can access signatures');
      console.log('   Number of signatures:', signaturesResponse.data.signatures?.length || 0);
    } catch (error) {
      console.log('❌ Admin cannot access signatures:', error.response?.data);
    }

    // 3. Test Frontend Route Access
    console.log('\n\n3. Frontend Route Access Summary:');
    console.log('=' .repeat(50));
    
    console.log('\n👤 Regular Users should have access to:');
    console.log('   ✅ /dashboard - Dashboard');
    console.log('   ✅ /mail - Upload documents for review');
    console.log('   ✅ /history - View their document history');
    console.log('   ❌ /document-signing - BLOCKED (admin only)');
    console.log('   ❌ /admin/document-signing - BLOCKED (admin only)');
    console.log('   ❌ /admin/records - BLOCKED (admin only)');

    console.log('\n👑 Admin Users should have access to:');
    console.log('   ✅ All regular user routes');
    console.log('   ✅ /document-signing - Direct document signing');
    console.log('   ✅ /admin/document-signing - Manage pending documents');
    console.log('   ✅ /admin/records - System logs and records');
    console.log('   ✅ /users - User management');

    // 4. Workflow Summary
    console.log('\n\n4. Document Workflow Summary:');
    console.log('=' .repeat(50));
    
    console.log('\n📋 Intended Workflow:');
    console.log('   1. Regular user uploads document via /mail');
    console.log('   2. Document goes to pending_documents table');
    console.log('   3. Admin reviews pending documents via /admin/document-signing');
    console.log('   4. Admin signs or rejects the document');
    console.log('   5. User can see status in /history');

    console.log('\n🔒 Security Measures:');
    console.log('   ✅ Regular users cannot directly sign documents');
    console.log('   ✅ All document signing goes through admin approval');
    console.log('   ✅ Admin-only routes are protected');
    console.log('   ✅ Navigation links hidden from regular users');

    console.log('\n🎉 Document Workflow Test Completed!');

  } catch (error) {
    console.error('\n❌ Workflow test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testDocumentWorkflow();
