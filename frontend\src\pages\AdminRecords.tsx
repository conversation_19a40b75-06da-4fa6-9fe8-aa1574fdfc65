import React, { useState, useEffect } from 'react';
import { useAuth } from '../services/AuthContext';
import api from '../services/api';

interface AuditLog {
  id: number;
  user_id: string;
  user_email: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  details?: string;
  ip_address?: string;
  user_agent?: string;
  timestamp: string;
  status: 'success' | 'failure' | 'warning';
}

interface Document {
  id: string;
  original_filename: string;
  signed_filename: string;
  user_email: string;
  signed_date: string;
  serial_number: string;
  status: string;
  file_size: number;
}

const AdminRecords: React.FC = () => {
  const { } = useAuth();
  const [activeTab, setActiveTab] = useState<'audit' | 'documents'>('audit');
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [actionFilter, setActionFilter] = useState('');

  const fetchAuditLogs = async () => {
    try {
      setLoading(true);
      // This endpoint would need to be implemented in the backend
      const response = await api.get('/audit/logs');
      setAuditLogs(response.data.logs || []);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      // For now, use mock data
      setAuditLogs([
        {
          id: 1,
          user_id: '123',
          user_email: '<EMAIL>',
          action: 'DOCUMENT_SIGNED',
          resource_type: 'document',
          resource_id: 'doc_123',
          details: 'تم توقيع المستند بنجاح',
          ip_address: '***********',
          user_agent: 'Mozilla/5.0...',
          timestamp: new Date().toISOString(),
          status: 'success'
        },
        {
          id: 2,
          user_id: '456',
          user_email: '<EMAIL>',
          action: 'DOCUMENT_UPLOADED',
          resource_type: 'document',
          resource_id: 'doc_124',
          details: 'تم رفع مستند جديد',
          ip_address: '***********',
          user_agent: 'Mozilla/5.0...',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          status: 'success'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const response = await api.get('/documents');
      setDocuments(response.data.documents || []);
    } catch (error) {
      console.error('Error fetching documents:', error);
      setError('فشل في تحميل المستندات');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'audit') {
      fetchAuditLogs();
    } else {
      fetchDocuments();
    }
  }, [activeTab]);

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failure':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'DOCUMENT_SIGNED':
        return (
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'DOCUMENT_UPLOADED':
        return (
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
        );
      case 'USER_LOGIN':
        return (
          <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
    }
  };

  const filteredAuditLogs = auditLogs.filter(log => {
    const matchesSearch = searchTerm === '' || 
      log.user_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (log.details && log.details.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesAction = actionFilter === '' || log.action === actionFilter;
    
    const matchesDate = dateFilter === '' || 
      new Date(log.timestamp).toDateString() === new Date(dateFilter).toDateString();
    
    return matchesSearch && matchesAction && matchesDate;
  });

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = searchTerm === '' || 
      doc.original_filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.user_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.serial_number.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });



  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50" dir="rtl">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 font-['Almarai']">
            السجلات
          </h1>
          <p className="mt-2 text-gray-600 font-['Almarai']">
            عرض سجلات النظام والمستندات
          </p>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg font-['Almarai']">
            {error}
          </div>
        )}

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-reverse space-x-8">
              <button
                onClick={() => setActiveTab('audit')}
                className={`py-2 px-1 border-b-2 font-medium text-sm font-['Almarai'] ${
                  activeTab === 'audit'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                سجل العمليات
              </button>
              <button
                onClick={() => setActiveTab('documents')}
                className={`py-2 px-1 border-b-2 font-medium text-sm font-['Almarai'] ${
                  activeTab === 'documents'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                سجل المستندات
              </button>
            </nav>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 bg-white p-4 rounded-lg shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                البحث
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="البحث في السجلات..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              />
            </div>
            {activeTab === 'audit' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                  نوع العملية
                </label>
                <select
                  value={actionFilter}
                  onChange={(e) => setActionFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
                >
                  <option value="">جميع العمليات</option>
                  <option value="DOCUMENT_SIGNED">توقيع المستندات</option>
                  <option value="DOCUMENT_UPLOADED">رفع المستندات</option>
                  <option value="USER_LOGIN">تسجيل الدخول</option>
                </select>
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                التاريخ
              </label>
              <input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              />
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          {activeTab === 'audit' ? (
            <div>
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 font-['Almarai']">
                  سجل العمليات ({filteredAuditLogs.length})
                </h2>
              </div>
              
              {filteredAuditLogs.length === 0 ? (
                <div className="px-6 py-12 text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2 font-['Almarai']">
                    لا توجد سجلات
                  </h3>
                  <p className="text-gray-500 font-['Almarai']">
                    لا توجد سجلات تطابق معايير البحث
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredAuditLogs.map((log) => (
                    <div key={log.id} className="px-6 py-4">
                      <div className="flex items-start space-x-reverse space-x-3">
                        <div className="flex-shrink-0">
                          {getActionIcon(log.action)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium text-gray-900 font-['Almarai']">
                              {log.user_email}
                            </p>
                            <div className="flex items-center space-x-reverse space-x-2">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium font-['Almarai'] ${getStatusColor(log.status)}`}>
                                {log.status === 'success' ? 'نجح' : log.status === 'failure' ? 'فشل' : 'تحذير'}
                              </span>
                              <span className="text-sm text-gray-500 font-['Almarai']">
                                {formatDate(log.timestamp)}
                              </span>
                            </div>
                          </div>
                          <p className="mt-1 text-sm text-gray-600 font-['Almarai']">
                            {log.details || log.action}
                          </p>
                          {log.ip_address && (
                            <p className="mt-1 text-xs text-gray-500 font-['Almarai']">
                              IP: {log.ip_address}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div>
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 font-['Almarai']">
                  سجل المستندات ({filteredDocuments.length})
                </h2>
              </div>
              
              {filteredDocuments.length === 0 ? (
                <div className="px-6 py-12 text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2 font-['Almarai']">
                    لا توجد مستندات
                  </h3>
                  <p className="text-gray-500 font-['Almarai']">
                    لا توجد مستندات تطابق معايير البحث
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          اسم الملف
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          المستخدم
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          الرقم التسلسلي
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          تاريخ التوقيع
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          حجم الملف
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredDocuments.map((document) => (
                        <tr key={document.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 font-['Almarai']">
                            {document.original_filename}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-['Almarai']">
                            {document.user_email}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-['Almarai']">
                            {document.serial_number}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-['Almarai']">
                            {formatDate(document.signed_date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-['Almarai']">
                            {formatFileSize(document.file_size)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminRecords;
