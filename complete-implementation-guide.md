# E-Signature System - Complete Implementation Guide

## Phase 1: Project Setup (Day 1)

### 1.1 Create Directory Structure
```bash
mkdir esign
cd esign
mkdir -p backend/src/{controllers,middleware,models,routes,services,utils}
mkdir -p backend/tests
mkdir -p frontend/src/{components,pages,services,utils}
mkdir -p frontend/public
mkdir -p database/migrations
```

### 1.2 Initialize Git & Environment
```bash
git init
echo "node_modules/\n.env\n*.log\ndist/\nbuild/" > .gitignore
```

### 1.3 Setup Backend Package.json
```bash
cd backend
npm init -y
npm install express cors helmet morgan dotenv bcryptjs jsonwebtoken
npm install multer aws-sdk pdf-lib pg uuid crypto-js express-rate-limit
npm install -D nodemon jest supertest
```

### 1.4 Setup Frontend Package.json
```bash
cd ../frontend
npx create-react-app . --template typescript
npm install axios react-router-dom tailwindcss
npm install -D @types/react-router-dom cypress
```

## Phase 2: Database Setup (Day 2)

### 2.1 PostgreSQL Database Schema
Create `database/migrations/001_initial_schema.sql`:
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE signatures (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    s3_path VARCHAR(500) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    filename VARCHAR(255) NOT NULL,
    serial_number VARCHAR(100) UNIQUE NOT NULL,
    signed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    s3_path VARCHAR(500) NOT NULL,
    digital_signature TEXT
);

CREATE TABLE logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    details JSONB
);

CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_serial_number ON documents(serial_number);
```

### 2.2 Database Connection
Create `backend/src/models/database.js`:
```javascript
const { Pool } = require('pg');

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

module.exports = pool;
```

## Phase 3: Backend Core (Days 3-5)

### 3.1 Express Server Setup
Create `backend/src/app.js`:
```javascript
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

const authRoutes = require('./routes/auth');
const signatureRoutes = require('./routes/signatures');
const documentRoutes = require('./routes/documents');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/signatures', signatureRoutes);
app.use('/api/documents', documentRoutes);

module.exports = app;
```

### 3.2 Authentication System
Create `backend/src/controllers/authController.js`:
```javascript
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const pool = require('../models/database');
const { validateEmail, validatePassword } = require('../utils/validation');

const register = async (req, res) => {
    try {
        const { email, password } = req.body;
        
        if (!validateEmail(email) || !validatePassword(password)) {
            return res.status(400).json({ error: 'Invalid email or password' });
        }

        const hashedPassword = await bcrypt.hash(password, 12);
        
        const result = await pool.query(
            'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING id, email',
            [email, hashedPassword]
        );

        const token = jwt.sign(
            { userId: result.rows[0].id },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );

        res.status(201).json({
            token,
            user: { id: result.rows[0].id, email: result.rows[0].email }
        });
    } catch (error) {
        if (error.code === '23505') {
            return res.status(400).json({ error: 'Email already exists' });
        }
        res.status(500).json({ error: 'Server error' });
    }
};

const login = async (req, res) => {
    // Implementation for login
};

module.exports = { register, login };
```

### 3.3 JWT Middleware
Create `backend/src/middleware/auth.js`:
```javascript
const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid token' });
        }
        req.user = user;
        next();
    });
};

module.exports = { authenticateToken };
```

## Phase 4: AWS S3 & Encryption (Days 6-7)

### 4.1 S3 Service
Create `backend/src/services/s3Service.js`:
```javascript
const AWS = require('aws-sdk');
const crypto = require('crypto-js');

const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION
});

const uploadFile = async (fileBuffer, fileName, contentType) => {
    const params = {
        Bucket: process.env.S3_BUCKET_NAME,
        Key: fileName,
        Body: fileBuffer,
        ContentType: contentType,
        ServerSideEncryption: 'AES256'
    };

    return s3.upload(params).promise();
};

const encryptSignature = (signatureData) => {
    return crypto.AES.encrypt(signatureData, process.env.ENCRYPTION_KEY).toString();
};

module.exports = { uploadFile, encryptSignature };
```

### 4.2 Signature Upload Controller
Create `backend/src/controllers/signatureController.js`:
```javascript
const multer = require('multer');
const { uploadFile, encryptSignature } = require('../services/s3Service');
const pool = require('../models/database');

const upload = multer({
    storage: multer.memoryStorage(),
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'image/png' || file.mimetype === 'image/svg+xml') {
            cb(null, true);
        } else {
            cb(new Error('Only PNG and SVG files allowed'), false);
        }
    }
});

const uploadSignature = async (req, res) => {
    try {
        const { userId } = req.user;
        const file = req.file;

        if (!file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        // Encrypt signature
        const encryptedSignature = encryptSignature(file.buffer.toString('base64'));
        
        // Upload to S3
        const fileName = `signatures/${userId}/${Date.now()}_${file.originalname}`;
        const s3Result = await uploadFile(
            Buffer.from(encryptedSignature),
            fileName,
            file.mimetype
        );

        // Save metadata to database
        await pool.query(
            'INSERT INTO signatures (user_id, s3_path) VALUES ($1, $2)',
            [userId, s3Result.Location]
        );

        res.json({ message: 'Signature uploaded successfully', path: s3Result.Location });
    } catch (error) {
        res.status(500).json({ error: 'Upload failed' });
    }
};

module.exports = { upload, uploadSignature };
```

## Phase 5: PDF Processing (Days 8-9)

### 5.1 PDF Service
Create `backend/src/services/pdfService.js`:
```javascript
const { PDFDocument, rgb } = require('pdf-lib');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

const embedSignatureInPDF = async (pdfBuffer, signatureBuffer, coordinates = { x: 50, y: 50 }) => {
    const pdfDoc = await PDFDocument.load(pdfBuffer);
    const pages = pdfDoc.getPages();
    const firstPage = pages[0];

    // Embed signature image
    const signatureImage = await pdfDoc.embedPng(signatureBuffer);
    const { width, height } = signatureImage.scale(0.5);

    firstPage.drawImage(signatureImage, {
        x: coordinates.x,
        y: coordinates.y,
        width,
        height,
    });

    // Generate and embed serial number
    const serialNumber = generateSerialNumber();
    firstPage.drawText(`Serial: ${serialNumber}`, {
        x: 50,
        y: 30,
        size: 10,
        color: rgb(0, 0, 0),
    });

    const pdfBytes = await pdfDoc.save();
    return { pdfBytes, serialNumber };
};

const generateSerialNumber = () => {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    return crypto.createHash('sha256').update(timestamp + random).digest('hex').substring(0, 16);
};

module.exports = { embedSignatureInPDF, generateSerialNumber };
```

## Phase 6: Frontend Development (Days 10-12)

### 6.1 React Router Setup
Create `frontend/src/App.tsx`:
```typescript
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Login from './pages/Login';
import SignatureUpload from './pages/SignatureUpload';
import DocumentSigning from './pages/DocumentSigning';
import History from './pages/History';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-100">
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/signature-upload" element={<SignatureUpload />} />
          <Route path="/document-signing" element={<DocumentSigning />} />
          <Route path="/history" element={<History />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
```

### 6.2 API Service
Create `frontend/src/services/api.ts`:
```typescript
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const authAPI = {
  login: (email: string, password: string) => 
    api.post('/auth/login', { email, password }),
  register: (email: string, password: string) => 
    api.post('/auth/register', { email, password }),
};

export const signatureAPI = {
  upload: (file: File) => {
    const formData = new FormData();
    formData.append('signature', file);
    return api.post('/signatures/upload', formData);
  },
};

export const documentAPI = {
  upload: (file: File, coordinates?: { x: number; y: number }) => 
    api.post('/documents/upload', { file, coordinates }),
  getHistory: (userId: string) => 
    api.get(`/documents/history/${userId}`),
};
```

## Phase 7: Testing & Deployment (Days 13-15)

### 7.1 Backend Tests
Create `backend/tests/auth.test.js`:
```javascript
const request = require('supertest');
const app = require('../src/app');

describe('Authentication', () => {
  test('Should register a new user', async () => {
    const response = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    expect(response.status).toBe(201);
    expect(response.body).toHaveProperty('token');
  });
});
```

### 7.2 Docker Setup
Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: esign
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  backend:
    build: ./backend
    ports:
      - "3001:3001"
    depends_on:
      - postgres
    environment:
      - NODE_ENV=development

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  postgres_data:
```

## Environment Variables

### Backend `.env.example`:
```
NODE_ENV=development
PORT=3001
DB_HOST=localhost
DB_PORT=5432
DB_NAME=esign
DB_USER=postgres
DB_PASSWORD=password
JWT_SECRET=your-secret-key
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_REGION=us-east-1
S3_BUCKET_NAME=esign-bucket
ENCRYPTION_KEY=your-encryption-key
```

## Implementation Order:
1. **Day 1**: Project structure, Git setup
2. **Day 2**: Database schema, connection
3. **Day 3**: Express server, authentication
4. **Day 4**: JWT middleware, validation
5. **Day 5**: User registration/login endpoints
6. **Day 6**: AWS S3 integration
7. **Day 7**: Signature encryption/upload
8. **Day 8**: PDF processing with pdf-lib
9. **Day 9**: Document signing endpoints
10. **Day 10**: React setup, routing
11. **Day 11**: Frontend components
12. **Day 12**: API integration
13. **Day 13**: Testing suite
14. **Day 14**: Docker setup
15. **Day 15**: Deployment, documentation