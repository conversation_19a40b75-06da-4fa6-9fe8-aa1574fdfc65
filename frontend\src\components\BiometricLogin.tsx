import React, { useState, useEffect } from 'react';
import { useBiometric } from '../hooks/useBiometric';

interface BiometricLoginProps {
  email: string;
  onSuccess: (token: string, refreshToken: string, user: any) => void;
  onError: (message: string) => void;
  className?: string;
}

const BiometricLogin: React.FC<BiometricLoginProps> = ({
  email,
  onSuccess,
  onError,
  className = ''
}) => {
  const {
    isSupported,
    isAvailable,
    capabilities,
    isLoading,
    error,
    authenticateBiometric,
    clearError
  } = useBiometric();

  const [isAuthenticating, setIsAuthenticating] = useState(false);

  // Handle biometric authentication
  const handleBiometricLogin = async () => {
    if (!email) {
      onError('يرجى إدخال البريد الإلكتروني أولاً');
      return;
    }

    try {
      setIsAuthenticating(true);
      clearError();

      const result = await authenticateBiometric(email);

      if (result.success && result.token && result.refreshToken && result.user) {
        onSuccess(result.token, result.refreshToken, result.user);
      } else {
        onError(result.message || 'فشل في المصادقة البيومترية');
      }
    } catch (err: any) {
      onError(err.message || 'فشل في المصادقة البيومترية');
    } finally {
      setIsAuthenticating(false);
    }
  };

  // Get appropriate icon based on platform
  const getBiometricIcon = () => {
    if (!capabilities) return '🔐';
    
    const platform = capabilities.supportedAuthenticators;
    
    if (platform.includes('platform')) {
      // Detect specific platform
      const userAgent = navigator.userAgent;
      if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
        return '👤'; // Face ID icon
      } else if (userAgent.includes('Mac')) {
        return '👆'; // Touch ID icon
      } else if (userAgent.includes('Windows')) {
        return '🔐'; // Windows Hello icon
      }
    }
    
    return '🔐'; // Generic biometric icon
  };

  // Get appropriate text based on platform
  const getBiometricText = () => {
    if (!capabilities) return 'المصادقة البيومترية';
    
    const userAgent = navigator.userAgent;
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      return 'تسجيل الدخول بـ Face ID';
    } else if (userAgent.includes('Mac')) {
      return 'تسجيل الدخول بـ Touch ID';
    } else if (userAgent.includes('Windows')) {
      return 'تسجيل الدخول بـ Windows Hello';
    }
    
    return 'تسجيل الدخول البيومتري';
  };

  // Show error if there's one
  useEffect(() => {
    if (error) {
      onError(error);
    }
  }, [error, onError]);

  // Don't render if not supported or available
  if (!isSupported || !isAvailable) {
    return null;
  }

  return (
    <div className={`biometric-login ${className}`}>
      <button
        type="button"
        onClick={handleBiometricLogin}
        disabled={isLoading || isAuthenticating || !email}
        className={`
          w-full flex items-center justify-center gap-3 px-4 py-3 
          bg-gradient-to-r from-blue-600 to-purple-600 
          hover:from-blue-700 hover:to-purple-700
          disabled:from-gray-400 disabled:to-gray-500
          text-white font-medium rounded-lg
          transition-all duration-200 ease-in-out
          transform hover:scale-105 disabled:hover:scale-100
          shadow-lg hover:shadow-xl disabled:shadow-md
          ${isAuthenticating ? 'animate-pulse' : ''}
        `}
        style={{ direction: 'rtl' }}
      >
        {isAuthenticating ? (
          <>
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            <span>جاري المصادقة...</span>
          </>
        ) : (
          <>
            <span className="text-xl">{getBiometricIcon()}</span>
            <span>{getBiometricText()}</span>
          </>
        )}
      </button>

      {/* Capability info for debugging (remove in production) */}
      {process.env.NODE_ENV === 'development' && capabilities && (
        <div className="mt-2 text-xs text-gray-500" style={{ direction: 'rtl' }}>
          <div>مدعوم: {capabilities.isSupported ? 'نعم' : 'لا'}</div>
          <div>متاح: {capabilities.isAvailable ? 'نعم' : 'لا'}</div>
          <div>المصادقات: {capabilities.supportedAuthenticators.join(', ')}</div>
        </div>
      )}
    </div>
  );
};

export default BiometricLogin;
