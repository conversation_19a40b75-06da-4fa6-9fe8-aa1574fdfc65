const axios = require('axios');

async function debugPDFViewer() {
  console.log('🔍 Debugging PDF Viewer Frontend Integration\n');

  try {
    // Step 1: Test backend health
    console.log('1. Testing backend health...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('✅ Backend health:', healthResponse.data.status);

    // Step 2: Test frontend health
    console.log('\n2. Testing frontend health...');
    try {
      const frontendResponse = await axios.get('http://localhost:3000');
      console.log('✅ Frontend is accessible');
    } catch (error) {
      console.log('❌ Frontend not accessible:', error.message);
      return;
    }

    // Step 3: Test the test PDF viewer page
    console.log('\n3. Testing test PDF viewer page...');
    try {
      const testPageResponse = await axios.get('http://localhost:3000/test-pdf-viewer.html');
      console.log('✅ Test PDF viewer page is accessible');
    } catch (error) {
      console.log('❌ Test PDF viewer page not accessible:', error.message);
    }

    // Step 4: Test document endpoint with specific document
    console.log('\n4. Testing document endpoint...');
    const documentId = '5dfdd344-43cc-4060-911c-2579571972d1';
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.fHhH70Nf1L11XiXq6EvHzlOg71x2B-qFkmQL9PpZVS4';
    
    const documentUrl = `http://localhost:3001/api/documents/${documentId}/view?token=${encodeURIComponent(token)}`;
    
    try {
      const docResponse = await axios.get(documentUrl, {
        responseType: 'arraybuffer',
        timeout: 30000
      });
      
      console.log('✅ Document endpoint working:');
      console.log(`   Status: ${docResponse.status}`);
      console.log(`   Content-Type: ${docResponse.headers['content-type']}`);
      console.log(`   Content-Length: ${docResponse.headers['content-length']} bytes`);
      
      // Check if it's a valid PDF
      const buffer = Buffer.from(docResponse.data);
      const pdfHeader = buffer.slice(0, 4).toString();
      console.log(`   PDF Header: ${pdfHeader}`);
      
      if (pdfHeader === '%PDF') {
        console.log('✅ Valid PDF file confirmed');
      } else {
        console.log('❌ Invalid PDF file format');
      }
      
    } catch (error) {
      console.log('❌ Document endpoint failed:', error.message);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Data: ${error.response.data}`);
      }
    }

    // Step 5: Test CORS headers
    console.log('\n5. Testing CORS headers...');
    try {
      const corsResponse = await axios.options(documentUrl);
      console.log('✅ CORS preflight successful');
      console.log('   CORS headers:', corsResponse.headers);
    } catch (error) {
      console.log('⚠️ CORS preflight failed (might be normal):', error.message);
    }

    // Step 6: Check PDF.js CDN availability
    console.log('\n6. Testing PDF.js CDN availability...');
    try {
      const pdfjsResponse = await axios.get('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js', {
        timeout: 10000
      });
      console.log('✅ PDF.js library accessible from CDN');
      
      const workerResponse = await axios.get('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js', {
        timeout: 10000
      });
      console.log('✅ PDF.js worker accessible from CDN');
    } catch (error) {
      console.log('❌ PDF.js CDN not accessible:', error.message);
    }

    console.log('\n📋 Summary:');
    console.log('- Backend is running and healthy');
    console.log('- Document endpoint returns valid PDF data');
    console.log('- PDF.js CDN resources are accessible');
    console.log('- Frontend should be able to display PDFs');
    
    console.log('\n🔧 Next steps:');
    console.log('1. Open http://localhost:3000/test-pdf-viewer.html in browser');
    console.log('2. Click "اختبار تحميل المستند" to test document loading');
    console.log('3. Click "تحميل PDF" to test PDF.js rendering');
    console.log('4. Check browser console for any JavaScript errors');

  } catch (error) {
    console.error('❌ Debug script failed:', error.message);
  }
}

// Run the debug script
debugPDFViewer().catch(console.error);
