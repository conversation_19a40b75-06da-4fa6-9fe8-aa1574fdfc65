const { query } = require('./backend/src/models/database');

async function checkTable() {
  try {
    console.log('Checking pending_documents table structure...');
    
    const result = await query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'pending_documents' 
      ORDER BY ordinal_position
    `);
    
    console.log('Table columns:');
    result.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.column_name} (${row.data_type}) - ${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    // Test a simple insert
    console.log('\nTesting simple insert...');
    const testResult = await query(`
      INSERT INTO pending_documents (
        original_filename, file_path, file_size, uploaded_by, uploader_email, notes, status
      ) VALUES ($1, $2, $3, $4, $5, $6, 'pending') RETURNING *
    `, [
      'test.pdf',
      '/test/path.pdf', 
      1024,
      '6a38dad9-42c9-4e61-8776-72f817b86d11', // Test user ID
      '<EMAIL>',
      'Test note'
    ]);
    
    console.log('✅ Insert successful:', testResult.rows[0]);
    
    // Clean up
    await query('DELETE FROM pending_documents WHERE original_filename = $1', ['test.pdf']);
    console.log('✅ Test record cleaned up');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
  
  process.exit(0);
}

checkTable();
