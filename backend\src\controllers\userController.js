const { query } = require('../models/database');
const { getUserAuditLogs, getUserStatistics } = require('../services/auditService');

/**
 * Get user's document history with pagination
 */
const getUserDocuments = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?.userId;
    const { 
      page = 1, 
      limit = 20, 
      status = null, 
      sortBy = 'created_at', 
      sortOrder = 'DESC' 
    } = req.query;

    const offset = (page - 1) * limit;

    // Build query conditions
    let whereConditions = ['user_id = $1'];
    let values = [userId];
    let paramCount = 1;

    if (status) {
      paramCount++;
      whereConditions.push(`status = $${paramCount}`);
      values.push(status);
    }

    // Validate sort parameters
    const allowedSortFields = ['created_at', 'signed_at', 'original_filename', 'file_size'];
    const allowedSortOrders = ['ASC', 'DESC'];
    
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const validSortOrder = allowedSortOrders.includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    const documentsQuery = `
      SELECT 
        d.id,
        d.original_filename,
        d.signed_filename,
        d.serial_number,
        d.file_size,
        d.status,
        d.created_at,
        d.signed_at,
        d.updated_at,
        COUNT(*) OVER() as total_count
      FROM documents d
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ${validSortBy} ${validSortOrder}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    values.push(limit, offset);

    const result = await query(documentsQuery, values);
    const documents = result.rows;
    const totalCount = documents.length > 0 ? parseInt(documents[0].total_count) : 0;

    res.json({
      success: true,
      data: {
        documents: documents.map(doc => ({
          id: doc.id,
          originalFilename: doc.original_filename,
          signedFilename: doc.signed_filename,
          serialNumber: doc.serial_number,
          fileSize: doc.file_size,
          status: doc.status,
          createdAt: doc.created_at,
          signedAt: doc.signed_at,
          updatedAt: doc.updated_at
        })),
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          hasNextPage: (page * limit) < totalCount,
          hasPreviousPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get user documents error:', error);
    res.status(500).json({
      success: false,
      error: 'فشل في تحميل المستندات',
      code: 'GET_DOCUMENTS_ERROR'
    });
  }
};

/**
 * Get user's document statistics
 */
const getUserDocumentStats = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?.userId;

    // Get statistics from audit service
    const stats = await getUserStatistics(userId);

    // Get additional document counts
    const countsQuery = `
      SELECT 
        COUNT(*) as total_documents,
        COUNT(CASE WHEN status = 'signed' THEN 1 END) as signed_documents,
        COUNT(CASE WHEN status = 'unsigned' THEN 1 END) as unsigned_documents,
        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as documents_last_30_days,
        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as documents_last_7_days,
        SUM(file_size) as total_storage_used,
        AVG(file_size) as average_file_size
      FROM documents 
      WHERE user_id = $1
    `;

    const countsResult = await query(countsQuery, [userId]);
    const counts = countsResult.rows[0];

    res.json({
      success: true,
      data: {
        // From audit service
        totalUploaded: stats.total_uploaded || 0,
        totalSigned: stats.total_signed || 0,
        totalDownloaded: stats.total_downloaded || 0,
        totalViewed: stats.total_viewed || 0,
        lastUploadAt: stats.last_upload_at,
        lastSignAt: stats.last_sign_at,
        lastDownloadAt: stats.last_download_at,
        lastViewAt: stats.last_view_at,
        
        // From document counts
        totalDocuments: parseInt(counts.total_documents) || 0,
        signedDocuments: parseInt(counts.signed_documents) || 0,
        unsignedDocuments: parseInt(counts.unsigned_documents) || 0,
        documentsLast30Days: parseInt(counts.documents_last_30_days) || 0,
        documentsLast7Days: parseInt(counts.documents_last_7_days) || 0,
        totalStorageUsed: parseInt(counts.total_storage_used) || 0,
        averageFileSize: parseFloat(counts.average_file_size) || 0,
        
        // Calculated metrics
        signingRate: counts.total_documents > 0 
          ? Math.round((counts.signed_documents / counts.total_documents) * 100) 
          : 0
      }
    });

  } catch (error) {
    console.error('Get user statistics error:', error);
    res.status(500).json({
      success: false,
      error: 'فشل في تحميل الإحصائيات',
      code: 'GET_STATS_ERROR'
    });
  }
};

/**
 * Get user's audit log history
 */
const getUserAuditHistory = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?.userId;
    const { 
      page = 1, 
      limit = 50, 
      action = null, 
      documentId = null,
      startDate = null,
      endDate = null
    } = req.query;

    const offset = (page - 1) * limit;

    const auditLogs = await getUserAuditLogs(userId, {
      limit: parseInt(limit),
      offset: parseInt(offset),
      action,
      documentId: documentId ? parseInt(documentId) : null,
      startDate,
      endDate
    });

    // Get total count for pagination
    let countConditions = ['user_id = $1'];
    let countValues = [userId];
    let countParamCount = 1;

    if (action) {
      countParamCount++;
      countConditions.push(`action = $${countParamCount}`);
      countValues.push(action);
    }

    if (documentId) {
      countParamCount++;
      countConditions.push(`document_id = $${countParamCount}`);
      countValues.push(parseInt(documentId));
    }

    if (startDate) {
      countParamCount++;
      countConditions.push(`timestamp >= $${countParamCount}`);
      countValues.push(startDate);
    }

    if (endDate) {
      countParamCount++;
      countConditions.push(`timestamp <= $${countParamCount}`);
      countValues.push(endDate);
    }

    const countQuery = `
      SELECT COUNT(*) as total_count 
      FROM audit_logs 
      WHERE ${countConditions.join(' AND ')}
    `;

    const countResult = await query(countQuery, countValues);
    const totalCount = parseInt(countResult.rows[0].total_count);

    res.json({
      success: true,
      data: {
        auditLogs: auditLogs.map(log => ({
          id: log.id,
          action: log.action,
          documentId: log.document_id,
          documentName: log.document_name,
          serialNumber: log.serial_number,
          timestamp: log.timestamp,
          success: log.success,
          details: log.details,
          ipAddress: log.ip_address,
          metadata: log.metadata
        })),
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          hasNextPage: (page * limit) < totalCount,
          hasPreviousPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get user audit history error:', error);
    res.status(500).json({
      success: false,
      error: 'فشل في تحميل سجل الأنشطة',
      code: 'GET_AUDIT_HISTORY_ERROR'
    });
  }
};

/**
 * Get user's recent activity summary
 */
const getUserRecentActivity = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?.userId;
    const { days = 7 } = req.query;

    const recentActivityQuery = `
      SELECT 
        action,
        COUNT(*) as count,
        MAX(timestamp) as last_occurrence
      FROM audit_logs 
      WHERE user_id = $1 
        AND timestamp >= CURRENT_DATE - INTERVAL '${parseInt(days)} days'
        AND success = true
      GROUP BY action
      ORDER BY last_occurrence DESC
    `;

    const result = await query(recentActivityQuery, [userId]);

    res.json({
      success: true,
      data: {
        period: `${days} days`,
        activities: result.rows.map(activity => ({
          action: activity.action,
          count: parseInt(activity.count),
          lastOccurrence: activity.last_occurrence
        }))
      }
    });

  } catch (error) {
    console.error('Get recent activity error:', error);
    res.status(500).json({
      success: false,
      error: 'فشل في تحميل الأنشطة الحديثة',
      code: 'GET_RECENT_ACTIVITY_ERROR'
    });
  }
};

module.exports = {
  getUserDocuments,
  getUserDocumentStats,
  getUserAuditHistory,
  getUserRecentActivity
};
