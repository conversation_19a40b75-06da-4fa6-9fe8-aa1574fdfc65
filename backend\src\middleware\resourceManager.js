const rateLimit = require('express-rate-limit');
const { query } = require('../models/database');

/**
 * Advanced resource management middleware
 */

// Memory monitoring and cleanup
class MemoryManager {
  constructor() {
    this.memoryThreshold = parseInt(process.env.MEMORY_THRESHOLD) || 500 * 1024 * 1024; // 500MB
    this.cleanupInterval = parseInt(process.env.CLEANUP_INTERVAL) || 5 * 60 * 1000; // 5 minutes
    this.isCleanupRunning = false;
    
    this.startMonitoring();
  }

  startMonitoring() {
    setInterval(() => {
      this.checkMemoryUsage();
    }, this.cleanupInterval);
  }

  checkMemoryUsage() {
    const memUsage = process.memoryUsage();
    
    if (memUsage.heapUsed > this.memoryThreshold) {
      console.warn('⚠️ High memory usage detected:', {
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB',
        threshold: Math.round(this.memoryThreshold / 1024 / 1024) + 'MB'
      });
      
      this.triggerCleanup();
    }
  }

  async triggerCleanup() {
    if (this.isCleanupRunning) return;
    
    this.isCleanupRunning = true;
    console.log('🧹 Starting memory cleanup...');
    
    try {
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
        console.log('✅ Garbage collection triggered');
      }
      
      // Clear any cached data
      this.clearCaches();
      
      // Clean up old sessions
      await this.cleanupOldSessions();
      
      const memUsageAfter = process.memoryUsage();
      console.log('✅ Memory cleanup completed:', {
        heapUsed: Math.round(memUsageAfter.heapUsed / 1024 / 1024) + 'MB'
      });
      
    } catch (error) {
      console.error('❌ Memory cleanup failed:', error);
    } finally {
      this.isCleanupRunning = false;
    }
  }

  clearCaches() {
    // Clear any application-level caches here
    // For example, if you have a cache object:
    // if (global.appCache) {
    //   global.appCache.clear();
    // }
  }

  async cleanupOldSessions() {
    try {
      // Clean up expired refresh tokens
      const result = await query(
        'UPDATE users SET refresh_token = NULL, refresh_token_expires = NULL WHERE refresh_token_expires < NOW()'
      );
      
      if (result.rowCount > 0) {
        console.log(`🗑️ Cleaned up ${result.rowCount} expired refresh tokens`);
      }
      
      // Clean up old logs (older than 30 days)
      const logCleanup = await query(
        'DELETE FROM logs WHERE timestamp < NOW() - INTERVAL \'30 days\''
      );
      
      if (logCleanup.rowCount > 0) {
        console.log(`🗑️ Cleaned up ${logCleanup.rowCount} old log entries`);
      }
      
    } catch (error) {
      console.error('Session cleanup error:', error);
    }
  }
}

// Initialize memory manager
const memoryManager = new MemoryManager();

// Dynamic rate limiting based on system load
const createDynamicRateLimit = (baseConfig) => {
  return rateLimit({
    ...baseConfig,
    skip: (req) => {
      // Skip rate limiting for health checks
      if (req.path === '/api/performance/health') return true;
      
      // Check system load
      const memUsage = process.memoryUsage();
      const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      
      // If memory usage is high, be more restrictive
      if (memoryUsagePercent > 80) {
        baseConfig.max = Math.floor(baseConfig.max * 0.5); // Reduce by 50%
      } else if (memoryUsagePercent > 60) {
        baseConfig.max = Math.floor(baseConfig.max * 0.75); // Reduce by 25%
      }
      
      return false;
    },
    handler: (req, res) => {
      const memUsage = process.memoryUsage();
      const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      
      res.status(429).json({
        success: false,
        message: 'تم تجاوز الحد الأقصى للطلبات. يرجى المحاولة لاحقاً.',
        retryAfter: Math.ceil(baseConfig.windowMs / 1000),
        systemLoad: {
          memoryUsage: Math.round(memoryUsagePercent) + '%',
          message: memoryUsagePercent > 80 ? 'حمولة عالية على النظام' : 'حمولة عادية'
        }
      });
    }
  });
};

// File upload size restrictions based on available memory
const createUploadSizeLimit = () => {
  return (req, res, next) => {
    const memUsage = process.memoryUsage();
    const availableMemory = memUsage.heapTotal - memUsage.heapUsed;
    const maxFileSize = Math.min(
      parseInt(process.env.MAX_FILE_SIZE) || 100 * 1024 * 1024, // 100MB default
      Math.floor(availableMemory * 0.1) // 10% of available memory
    );
    
    // Set dynamic file size limit
    req.maxFileSize = maxFileSize;
    
    // Add to request headers for multer
    req.headers['x-max-file-size'] = maxFileSize.toString();
    
    next();
  };
};

// Session cleanup middleware
const sessionCleanup = async (req, res, next) => {
  // Periodically clean up expired sessions
  if (Math.random() < 0.01) { // 1% chance to trigger cleanup
    setImmediate(async () => {
      try {
        await memoryManager.cleanupOldSessions();
      } catch (error) {
        console.error('Background session cleanup error:', error);
      }
    });
  }
  
  next();
};

// Resource monitoring middleware
const resourceMonitor = (req, res, next) => {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();
  
  // Monitor request completion
  res.on('finish', () => {
    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    const duration = endTime - startTime;
    const memoryDiff = endMemory.heapUsed - startMemory.heapUsed;
    
    // Log resource-intensive requests
    if (duration > 5000 || memoryDiff > 50 * 1024 * 1024) { // 5 seconds or 50MB
      console.warn('🐌 Resource-intensive request detected:', {
        path: req.path,
        method: req.method,
        duration: duration + 'ms',
        memoryUsed: Math.round(memoryDiff / 1024 / 1024) + 'MB',
        statusCode: res.statusCode,
        userAgent: req.get('User-Agent')?.substring(0, 100)
      });
    }
  });
  
  next();
};

// Predefined rate limit configurations
const rateLimitConfigs = {
  // General API rate limiting
  general: createDynamicRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // requests per window
    message: 'تم تجاوز الحد الأقصى للطلبات العامة'
  }),
  
  // Authentication rate limiting
  auth: createDynamicRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // login attempts per window
    message: 'تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول'
  }),
  
  // File upload rate limiting
  upload: createDynamicRateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 20, // uploads per hour
    message: 'تم تجاوز الحد الأقصى لرفع الملفات'
  }),
  
  // Document operations rate limiting
  documents: createDynamicRateLimit({
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: 50, // document operations per window
    message: 'تم تجاوز الحد الأقصى لعمليات المستندات'
  }),
  
  // Admin operations rate limiting
  admin: createDynamicRateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 200, // admin operations per window
    message: 'تم تجاوز الحد الأقصى للعمليات الإدارية'
  })
};

// Resource health check
const getResourceHealth = () => {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  return {
    memory: {
      used: memUsage.heapUsed,
      total: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      usagePercent: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system
    },
    uptime: process.uptime(),
    loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0]
  };
};

module.exports = {
  memoryManager,
  createDynamicRateLimit,
  createUploadSizeLimit,
  sessionCleanup,
  resourceMonitor,
  rateLimitConfigs,
  getResourceHealth
};
