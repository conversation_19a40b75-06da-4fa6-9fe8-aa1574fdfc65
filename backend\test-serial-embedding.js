const { PDFDocument, rgb, StandardFonts } = require('pdf-lib');
const fs = require('fs');

async function testSerialNumberEmbedding() {
  try {
    console.log('🧪 Testing Serial Number Embedding...\n');

    // Create a simple test PDF
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([600, 800]);
    
    // Test font loading
    console.log('1. Testing font loading...');
    let testFont;
    try {
      testFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
      console.log('✅ Helvetica font loaded successfully');
      console.log('   Font type:', typeof testFont);
      console.log('   Font constructor:', testFont.constructor.name);
    } catch (fontError) {
      console.error('❌ Failed to load Helvetica:', fontError.message);
      return;
    }

    // Test text drawing
    console.log('\n2. Testing text drawing...');
    const testText = 'DIGITALLY SIGNED';
    const serialText = 'Serial: DOC-TEST123456789';
    const dateText = 'Date: 07/16/2025, 05:50';

    try {
      // Draw test text
      page.drawText(testText, {
        x: 100,
        y: 700,
        size: 14,
        font: testFont,
        color: rgb(0, 0, 0),
      });
      console.log('✅ Successfully drew:', testText);

      page.drawText(serialText, {
        x: 100,
        y: 680,
        size: 12,
        font: testFont,
        color: rgb(0, 0, 0),
      });
      console.log('✅ Successfully drew:', serialText);

      page.drawText(dateText, {
        x: 100,
        y: 660,
        size: 10,
        font: testFont,
        color: rgb(0, 0, 0),
      });
      console.log('✅ Successfully drew:', dateText);

    } catch (textError) {
      console.error('❌ Failed to draw text:', textError.message);
      return;
    }

    // Save the test PDF
    console.log('\n3. Saving test PDF...');
    const pdfBytes = await pdfDoc.save();
    const outputPath = './test-serial-output.pdf';
    fs.writeFileSync(outputPath, pdfBytes);
    console.log('✅ Test PDF saved to:', outputPath);
    console.log('   PDF size:', pdfBytes.length, 'bytes');

    console.log('\n🎉 Serial number embedding test completed successfully!');
    console.log('\n📋 Results:');
    console.log('   ✅ Font loading: Working');
    console.log('   ✅ Text drawing: Working');
    console.log('   ✅ PDF generation: Working');
    console.log('\n🔧 This proves the basic functionality works.');
    console.log('   The issue in the main application is that the font is null.');
    console.log('   We need to ensure the fallback font loading actually executes.');

    return { success: true, pdfBytes, outputPath };

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    return { success: false, error: error.message };
  }
}

// Run the test
if (require.main === module) {
  testSerialNumberEmbedding()
    .then((result) => {
      if (result.success) {
        console.log('\n✅ Test passed! The issue is in the main application logic.');
        process.exit(0);
      } else {
        console.log('\n❌ Test failed! There\'s a fundamental issue.');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Unexpected error:', error.message);
      process.exit(1);
    });
}

module.exports = { testSerialNumberEmbedding };
