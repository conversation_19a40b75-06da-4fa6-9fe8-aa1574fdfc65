const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { documentRateLimit } = require('../middleware/documentAuth');

const {
  testNotification,
  getNotificationConfig,
  getNotificationStatistics,
  retryFailedNotificationsEndpoint,
  cleanupNotificationLogs,
  validatePhoneNumberEndpoint,
  getUserNotificationHistoryEndpoint,
  notificationHealthCheck
} = require('../controllers/notificationController');

const router = express.Router();

// All notification routes require authentication
router.use(authenticateToken);

/**
 * User notification endpoints
 */

// Test notification system
router.post('/test', 
  documentRateLimit(5), // Limit to 5 test notifications per window
  testNotification
);

// Get notification configuration and status
router.get('/config', 
  getNotificationConfig
);

// Get user's notification history
router.get('/history', 
  getUserNotificationHistoryEndpoint
);

// Validate phone number format
router.post('/validate-phone', 
  validatePhoneNumberEndpoint
);

// Health check for notification system
router.get('/health', 
  notificationHealthCheck
);

/**
 * Admin notification endpoints
 * Note: In a production system, you would add admin role checking middleware
 */

// Get notification statistics (admin only)
router.get('/admin/stats', 
  documentRateLimit(20),
  getNotificationStatistics
);

// Retry failed notifications (admin only)
router.post('/admin/retry-failed', 
  documentRateLimit(5),
  retryFailedNotificationsEndpoint
);

// Cleanup old notification logs (admin only)
router.post('/admin/cleanup', 
  documentRateLimit(2),
  cleanupNotificationLogs
);

module.exports = router;
