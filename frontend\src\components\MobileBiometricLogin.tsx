import React, { useState, useEffect } from 'react';
import { useBiometric } from '../hooks/useBiometric';
import mobileDetection from '../utils/mobileDetection';

interface MobileBiometricLoginProps {
  email: string;
  onSuccess: (token: string, refreshToken: string, user: any) => void;
  onError: (message: string) => void;
  className?: string;
}

const MobileBiometricLogin: React.FC<MobileBiometricLoginProps> = ({
  email,
  onSuccess,
  onError,
  className = ''
}) => {
  const {
    isSupported,
    isAvailable,
    capabilities,
    isLoading,
    error,
    authenticateBiometric,
    clearError
  } = useBiometric();

  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);
  const mobileCapabilities = mobileDetection.getCapabilities();

  // Handle biometric authentication with mobile optimizations
  const handleBiometricLogin = async () => {
    if (!email) {
      onError('يرجى إدخال البريد الإلكتروني أولاً');
      return;
    }

    try {
      setIsAuthenticating(true);
      setShowInstructions(true);
      clearError();

      // Trigger haptic feedback on mobile
      mobileDetection.triggerHapticFeedback('warning');

      const result = await authenticateBiometric(email);

      if (result.success && result.token && result.refreshToken && result.user) {
        // Success haptic feedback
        mobileDetection.triggerHapticFeedback('success');
        onSuccess(result.token, result.refreshToken, result.user);
      } else {
        // Error haptic feedback
        mobileDetection.triggerHapticFeedback('error');
        onError(result.message || 'فشل في المصادقة البيومترية');
      }
    } catch (err: any) {
      mobileDetection.triggerHapticFeedback('error');
      onError(err.message || 'فشل في المصادقة البيومترية');
    } finally {
      setIsAuthenticating(false);
      setShowInstructions(false);
    }
  };

  // Get platform-specific information
  const getBiometricInfo = () => {
    const typeName = mobileDetection.getBiometricTypeName();
    const icon = mobileDetection.getBiometricIcon();
    
    let instructions = '';
    if (mobileCapabilities.supportsFaceID) {
      instructions = 'انظر إلى الكاميرا الأمامية لجهازك';
    } else if (mobileCapabilities.supportsTouchID) {
      instructions = 'ضع إصبعك على زر الهوم';
    } else if (mobileCapabilities.supportsFingerprint) {
      instructions = 'ضع إصبعك على مستشعر البصمة';
    } else {
      instructions = 'اتبع التعليمات التي تظهر على جهازك';
    }

    return {
      name: typeName,
      icon,
      instructions,
      buttonText: `تسجيل الدخول بـ ${typeName}`
    };
  };

  const biometricInfo = getBiometricInfo();

  // Show error if there's one
  useEffect(() => {
    if (error) {
      onError(error);
    }
  }, [error, onError]);

  // Don't render if not supported or available
  if (!isSupported || !isAvailable || !mobileCapabilities.isMobile) {
    return null;
  }

  return (
    <div className={`mobile-biometric-login ${className}`}>
      {/* Main biometric button */}
      <button
        type="button"
        onClick={handleBiometricLogin}
        disabled={isLoading || isAuthenticating || !email}
        className={`
          w-full flex items-center justify-center gap-3 px-6 py-4
          bg-gradient-to-r from-blue-600 to-purple-600 
          hover:from-blue-700 hover:to-purple-700
          disabled:from-gray-400 disabled:to-gray-500
          text-white font-medium rounded-xl
          transition-all duration-300 ease-in-out
          transform hover:scale-105 disabled:hover:scale-100
          shadow-lg hover:shadow-xl disabled:shadow-md
          ${isAuthenticating ? 'animate-pulse' : ''}
          ${mobileCapabilities.isPWA ? 'mb-safe' : ''}
        `}
        style={{ 
          direction: 'rtl',
          minHeight: '56px', // Touch-friendly height
          fontSize: '16px' // Prevent zoom on iOS
        }}
      >
        {isAuthenticating ? (
          <>
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
            <span>جاري المصادقة...</span>
          </>
        ) : (
          <>
            <span className="text-2xl">{biometricInfo.icon}</span>
            <span>{biometricInfo.buttonText}</span>
          </>
        )}
      </button>

      {/* Instructions overlay for mobile */}
      {showInstructions && isAuthenticating && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-8 m-4 max-w-sm w-full text-center" style={{ direction: 'rtl' }}>
            <div className="text-6xl mb-4 animate-pulse">{biometricInfo.icon}</div>
            <h3 className="text-xl font-bold mb-4 text-gray-800">
              {biometricInfo.name}
            </h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              {biometricInfo.instructions}
            </p>
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
            <button
              onClick={() => {
                setIsAuthenticating(false);
                setShowInstructions(false);
              }}
              className="mt-6 text-gray-500 hover:text-gray-700 text-sm"
            >
              إلغاء
            </button>
          </div>
        </div>
      )}

      {/* Device info for debugging (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-3 p-3 bg-gray-100 rounded-lg text-xs text-gray-600" style={{ direction: 'rtl' }}>
          <div className="font-semibold mb-2">معلومات الجهاز:</div>
          <div>النوع: {mobileCapabilities.isMobile ? 'جوال' : 'سطح مكتب'}</div>
          <div>النظام: {mobileCapabilities.isIOS ? 'iOS' : mobileCapabilities.isAndroid ? 'Android' : 'أخرى'}</div>
          <div>المتصفح: {mobileCapabilities.browserName}</div>
          <div>إصدار النظام: {mobileCapabilities.osVersion}</div>
          <div>نموذج الجهاز: {mobileCapabilities.deviceModel}</div>
          <div>PWA: {mobileCapabilities.isPWA ? 'نعم' : 'لا'}</div>
          <div>Face ID: {mobileCapabilities.supportsFaceID ? 'مدعوم' : 'غير مدعوم'}</div>
          <div>Touch ID: {mobileCapabilities.supportsTouchID ? 'مدعوم' : 'غير مدعوم'}</div>
          <div>بصمة الإصبع: {mobileCapabilities.supportsFingerprint ? 'مدعوم' : 'غير مدعوم'}</div>
        </div>
      )}

      {/* PWA-specific styling */}
      <style jsx>{`
        .mb-safe {
          margin-bottom: env(safe-area-inset-bottom);
        }
        
        @media (display-mode: standalone) {
          .mobile-biometric-login {
            padding-bottom: env(safe-area-inset-bottom);
          }
        }
        
        /* iOS specific styles */
        @supports (-webkit-touch-callout: none) {
          .mobile-biometric-login button {
            -webkit-appearance: none;
            -webkit-tap-highlight-color: transparent;
          }
        }
        
        /* Android specific styles */
        @media (max-width: 768px) and (orientation: portrait) {
          .mobile-biometric-login button {
            font-size: 16px; /* Prevent zoom on Android */
          }
        }
      `}</style>
    </div>
  );
};

export default MobileBiometricLogin;
