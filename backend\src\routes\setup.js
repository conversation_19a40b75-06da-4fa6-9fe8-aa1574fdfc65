const express = require('express');
const { query } = require('../models/database');

const router = express.Router();

const setupDatabase = async (req, res) => {
  try {
    console.log('Setting up database tables...');

    // Enable UUID extension
    await query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
    console.log('✓ UUID extension enabled');

    // Create users table
    await query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✓ Users table created');

    // Create signatures table
    await query(`
      CREATE TABLE IF NOT EXISTS signatures (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INTEGER,
        mime_type VARCHAR(100),
        encrypted_data TEXT,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✓ Signatures table created');

    // Create documents table
    await query(`
      CREATE TABLE IF NOT EXISTS documents (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        original_filename VARCHAR(255) NOT NULL,
        signed_filename VARCHAR(255) NOT NULL,
        serial_number VARCHAR(100) UNIQUE NOT NULL,
        signed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        file_path VARCHAR(500) NOT NULL,
        file_size INTEGER,
        digital_signature TEXT,
        signature_coordinates JSONB,
        status VARCHAR(50) DEFAULT 'signed'
      )
    `);
    console.log('✓ Documents table created');

    // Create logs table
    await query(`
      CREATE TABLE IF NOT EXISTS logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id) ON DELETE SET NULL,
        action VARCHAR(100) NOT NULL,
        resource_type VARCHAR(50),
        resource_id UUID,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address INET,
        user_agent TEXT,
        details JSONB
      )
    `);
    console.log('✓ Logs table created');

    // Add phone number and notification columns to users table
    await query(`
      ALTER TABLE users
      ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20),
      ADD COLUMN IF NOT EXISTS full_name VARCHAR(255),
      ADD COLUMN IF NOT EXISTS whatsapp_notifications_enabled BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"document_signed": true, "document_uploaded": false, "admin_notifications": true}'::jsonb
    `);
    console.log('✓ User notification columns added');

    // Create notification logs table
    await query(`
      CREATE TABLE IF NOT EXISTS notification_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
        notification_type VARCHAR(50) NOT NULL,
        recipients JSONB NOT NULL,
        message_content TEXT NOT NULL,
        success BOOLEAN NOT NULL DEFAULT false,
        result_data JSONB,
        retry_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✓ Notification logs table created');

    // Create indexes
    await query('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');
    await query('CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users(phone_number) WHERE phone_number IS NOT NULL');
    await query('CREATE INDEX IF NOT EXISTS idx_signatures_user_id ON signatures(user_id)');
    await query('CREATE INDEX IF NOT EXISTS idx_signatures_upload_date ON signatures(upload_date)');
    await query('CREATE INDEX IF NOT EXISTS idx_documents_user_id ON documents(user_id)');
    await query('CREATE INDEX IF NOT EXISTS idx_documents_serial_number ON documents(serial_number)');
    await query('CREATE INDEX IF NOT EXISTS idx_documents_signed_date ON documents(signed_date)');
    await query('CREATE INDEX IF NOT EXISTS idx_logs_user_id ON logs(user_id)');
    await query('CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp)');
    await query('CREATE INDEX IF NOT EXISTS idx_logs_action ON logs(action)');
    await query('CREATE INDEX IF NOT EXISTS idx_notification_logs_user_id ON notification_logs(user_id)');
    await query('CREATE INDEX IF NOT EXISTS idx_notification_logs_document_id ON notification_logs(document_id)');
    await query('CREATE INDEX IF NOT EXISTS idx_notification_logs_type ON notification_logs(notification_type)');
    await query('CREATE INDEX IF NOT EXISTS idx_notification_logs_created_at ON notification_logs(created_at)');
    console.log('✓ Indexes created');

    // Create trigger function
    await query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);

    // Create trigger
    await query(`
      DROP TRIGGER IF EXISTS update_users_updated_at ON users;
      CREATE TRIGGER update_users_updated_at 
          BEFORE UPDATE ON users 
          FOR EACH ROW 
          EXECUTE FUNCTION update_updated_at_column()
    `);
    console.log('✓ Triggers created');

    res.json({
      success: true,
      message: 'Database setup completed successfully!',
      tables: ['users', 'signatures', 'documents', 'logs'],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Database setup error:', error);
    res.status(500).json({
      success: false,
      error: 'Database setup failed',
      details: error.message
    });
  }
};

router.post('/database', setupDatabase);

module.exports = router;
