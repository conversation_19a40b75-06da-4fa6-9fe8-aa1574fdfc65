#!/usr/bin/env node

/**
 * Test database connection and users table
 */

const { query } = require('./backend/src/models/database');

async function testDatabase() {
  try {
    console.log('🧪 Testing Database Connection');
    console.log('==============================\n');

    // Test basic connection
    console.log('1. Testing database connection...');
    const connectionTest = await query('SELECT NOW() as current_time');
    console.log('✅ Database connected successfully');
    console.log(`   Current time: ${connectionTest.rows[0].current_time}`);

    // Check users table structure
    console.log('\n2. Checking users table structure...');
    const tableStructure = await query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `);
    
    console.log('✅ Users table columns:');
    tableStructure.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // Test users query
    console.log('\n3. Testing users query...');
    const usersQuery = `
      SELECT 
        id, 
        email, 
        full_name,
        created_at, 
        updated_at,
        CASE 
          WHEN updated_at > NOW() - INTERVAL '30 days' THEN 'active'
          ELSE 'inactive'
        END as status
      FROM users 
      ORDER BY created_at DESC
      LIMIT 5
    `;
    
    const usersResult = await query(usersQuery);
    console.log(`✅ Users query successful - found ${usersResult.rows.length} users`);
    
    if (usersResult.rows.length > 0) {
      console.log('\n📋 Sample users:');
      usersResult.rows.forEach((user, index) => {
        console.log(`${index + 1}. ID: ${user.id}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Full Name: ${user.full_name || 'N/A'}`);
        console.log(`   Created: ${user.created_at}`);
        console.log(`   Updated: ${user.updated_at || 'N/A'}`);
        console.log(`   Status: ${user.status}`);
        console.log('');
      });
    }

    console.log('🎯 Database test completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the test
testDatabase();
