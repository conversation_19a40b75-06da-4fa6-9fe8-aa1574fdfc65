const { initializeTwilio } = require('../services/whatsappNotificationService');
const { getNotificationHealth } = require('../services/notificationMonitoringService');
const { query } = require('../models/database');

/**
 * Initialize and test the WhatsApp notification system
 */
async function initializeNotificationSystem() {
  console.log('🚀 Initializing WhatsApp Notification System...\n');

  try {
    // 1. Check environment variables
    console.log('1️⃣ Checking environment configuration...');
    
    const requiredEnvVars = [
      'TWILIO_ACCOUNT_SID',
      'TWILIO_AUTH_TOKEN',
      'TWILIO_WHATSAPP_FROM'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      console.error('❌ Missing required environment variables:');
      missingVars.forEach(varName => {
        console.error(`   - ${varName}`);
      });
      console.error('\nPlease add these to your .env file and restart.\n');
      return false;
    }

    console.log('✅ Environment variables configured');

    // 2. Initialize Twilio client
    console.log('\n2️⃣ Initializing Twilio client...');
    
    const twilioClient = initializeTwilio();
    
    if (!twilioClient) {
      console.error('❌ Failed to initialize Twilio client');
      console.error('   Check your TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN\n');
      return false;
    }

    console.log('✅ Twilio client initialized successfully');

    // 3. Test Twilio connection
    console.log('\n3️⃣ Testing Twilio connection...');
    
    try {
      const account = await twilioClient.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
      console.log(`✅ Connected to Twilio account: ${account.friendlyName}`);
      console.log(`   Account Status: ${account.status}`);
    } catch (twilioError) {
      console.error('❌ Twilio connection test failed:');
      console.error(`   ${twilioError.message}`);
      return false;
    }

    // 4. Check database tables
    console.log('\n4️⃣ Checking database tables...');
    
    try {
      // Check if notification_logs table exists
      const tableCheck = await query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'notification_logs'
        );
      `);

      if (!tableCheck.rows[0].exists) {
        console.error('❌ notification_logs table not found');
        console.error('   Run database setup: npm run setup-db\n');
        return false;
      }

      // Check if users table has notification columns
      const columnCheck = await query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name IN ('phone_number', 'whatsapp_notifications_enabled', 'notification_preferences');
      `);

      const expectedColumns = ['phone_number', 'whatsapp_notifications_enabled', 'notification_preferences'];
      const existingColumns = columnCheck.rows.map(row => row.column_name);
      const missingColumns = expectedColumns.filter(col => !existingColumns.includes(col));

      if (missingColumns.length > 0) {
        console.error('❌ Missing user table columns:');
        missingColumns.forEach(col => console.error(`   - ${col}`));
        console.error('   Run database setup: npm run setup-db\n');
        return false;
      }

      console.log('✅ Database tables configured correctly');

    } catch (dbError) {
      console.error('❌ Database check failed:');
      console.error(`   ${dbError.message}\n`);
      return false;
    }

    // 5. Check notification health
    console.log('\n5️⃣ Checking notification system health...');
    
    const health = await getNotificationHealth();
    
    console.log(`✅ Notification system status: ${health.status.toUpperCase()}`);
    
    if (health.status !== 'healthy' && health.status !== 'warning') {
      console.warn('⚠️  System health check shows issues - this is normal for new installations');
    }

    // 6. Display configuration summary
    console.log('\n📋 Configuration Summary:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`Notifications Enabled: ${process.env.WHATSAPP_NOTIFICATIONS_ENABLED || 'true'}`);
    console.log(`From Number: ${process.env.TWILIO_WHATSAPP_FROM}`);
    console.log(`Admin Numbers: ${process.env.WHATSAPP_ADMIN_NUMBERS || 'None configured'}`);
    console.log(`Retry Attempts: ${process.env.WHATSAPP_RETRY_ATTEMPTS || '3'}`);
    console.log(`Retry Delay: ${process.env.WHATSAPP_RETRY_DELAY || '5000'}ms`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // 7. Next steps
    console.log('\n🎉 WhatsApp Notification System initialized successfully!\n');
    console.log('📝 Next Steps:');
    console.log('1. Add phone numbers to user profiles via /api/auth/profile');
    console.log('2. Test notifications via /api/notifications/test');
    console.log('3. Sign a document to trigger automatic notifications');
    console.log('4. Monitor system health via /api/notifications/health');
    console.log('5. View statistics via /api/notifications/admin/stats\n');

    console.log('📚 For detailed setup instructions, see: WHATSAPP_NOTIFICATION_SETUP.md\n');

    return true;

  } catch (error) {
    console.error('❌ Initialization failed:');
    console.error(`   ${error.message}\n`);
    return false;
  }
}

// Run initialization if this script is executed directly
if (require.main === module) {
  require('dotenv').config();
  
  initializeNotificationSystem()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Fatal error during initialization:', error);
      process.exit(1);
    });
}

module.exports = { initializeNotificationSystem };
