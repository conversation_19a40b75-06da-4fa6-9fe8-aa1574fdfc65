import React, { useState, useEffect } from 'react';
import { useBiometric } from '../hooks/useBiometric';
import BiometricEnrollment from './BiometricEnrollment';

interface BiometricManagementProps {
  className?: string;
}

const BiometricManagement: React.FC<BiometricManagementProps> = ({
  className = ''
}) => {
  const {
    capabilities,
    isSupported,
    isAvailable,
    biometricStatus,
    credentials,
    isLoading,
    error,
    removeBiometric,
    updatePreferences,
    refreshCredentials,
    clearError
  } = useBiometric();

  const [showEnrollment, setShowEnrollment] = useState(false);
  const [selectedCredential, setSelectedCredential] = useState<string | null>(null);
  const [isRemoving, setIsRemoving] = useState(false);
  const [preferredMethod, setPreferredMethod] = useState<string>('password');
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Load credentials on mount
  useEffect(() => {
    refreshCredentials();
  }, [refreshCredentials]);

  // Update preferred method when status changes
  useEffect(() => {
    if (biometricStatus?.preferred_auth_method) {
      setPreferredMethod(biometricStatus.preferred_auth_method);
    }
  }, [biometricStatus]);

  // Handle credential removal
  const handleRemoveCredential = async (credentialId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه المصادقة البيومترية؟')) {
      return;
    }

    try {
      setIsRemoving(true);
      const result = await removeBiometric(credentialId);
      
      if (result.success) {
        setSuccessMessage('تم حذف المصادقة البيومترية بنجاح');
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 3000);
      }
    } catch (err) {
      // Error is handled by the hook
    } finally {
      setIsRemoving(false);
    }
  };

  // Handle preference update
  const handlePreferenceUpdate = async () => {
    try {
      const result = await updatePreferences({
        preferredAuthMethod: preferredMethod
      });
      
      if (result.success) {
        setSuccessMessage('تم تحديث التفضيلات بنجاح');
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 3000);
      }
    } catch (err) {
      // Error is handled by the hook
    }
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'لم يتم الاستخدام';
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get device icon
  const getDeviceIcon = (platform: string) => {
    switch (platform?.toLowerCase()) {
      case 'ios':
        return '📱';
      case 'android':
        return '🤖';
      case 'windows':
        return '💻';
      case 'macos':
        return '🖥️';
      default:
        return '🔐';
    }
  };

  if (showEnrollment) {
    return (
      <BiometricEnrollment
        onSuccess={() => {
          setShowEnrollment(false);
          refreshCredentials();
          setSuccessMessage('تم تسجيل المصادقة البيومترية بنجاح');
          setShowSuccess(true);
          setTimeout(() => setShowSuccess(false), 3000);
        }}
        onCancel={() => setShowEnrollment(false)}
        className={className}
      />
    );
  }

  return (
    <div className={`biometric-management ${className}`} style={{ direction: 'rtl' }}>
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">إدارة المصادقة البيومترية</h2>

        {/* Success Message */}
        {showSuccess && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {successMessage}
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <div className="flex justify-between items-center">
              <span>{error}</span>
              <button
                onClick={clearError}
                className="text-red-500 hover:text-red-700"
              >
                ✕
              </button>
            </div>
          </div>
        )}

        {/* Capability Status */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">حالة الدعم</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className={`p-4 rounded-lg border ${isSupported ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
              <div className="flex items-center gap-2">
                <span>{isSupported ? '✅' : '❌'}</span>
                <span className={isSupported ? 'text-green-700' : 'text-red-700'}>
                  {isSupported ? 'مدعوم في المتصفح' : 'غير مدعوم في المتصفح'}
                </span>
              </div>
            </div>
            <div className={`p-4 rounded-lg border ${isAvailable ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'}`}>
              <div className="flex items-center gap-2">
                <span>{isAvailable ? '✅' : '⚠️'}</span>
                <span className={isAvailable ? 'text-green-700' : 'text-yellow-700'}>
                  {isAvailable ? 'متاح على الجهاز' : 'غير متاح على الجهاز'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Current Status */}
        {biometricStatus && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">الحالة الحالية</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">المصادقة البيومترية: </span>
                  <span className={biometricStatus.biometric_enabled ? 'text-green-600' : 'text-red-600'}>
                    {biometricStatus.biometric_enabled ? 'مفعلة' : 'غير مفعلة'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">الطريقة المفضلة: </span>
                  <span className="text-gray-700">
                    {biometricStatus.preferred_auth_method === 'biometric' ? 'بيومترية' :
                     biometricStatus.preferred_auth_method === 'both' ? 'كلاهما' : 'كلمة مرور'}
                  </span>
                </div>
                {biometricStatus.biometric_enrolled_at && (
                  <div className="md:col-span-2">
                    <span className="font-medium">تاريخ التسجيل: </span>
                    <span className="text-gray-700">{formatDate(biometricStatus.biometric_enrolled_at)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Registered Credentials */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-semibold">الأجهزة المسجلة</h3>
            {isSupported && isAvailable && (
              <button
                onClick={() => setShowEnrollment(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                إضافة جهاز جديد
              </button>
            )}
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : credentials.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🔐</div>
              <p>لا توجد أجهزة مسجلة</p>
              {isSupported && isAvailable && (
                <button
                  onClick={() => setShowEnrollment(true)}
                  className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  تسجيل الجهاز الأول
                </button>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {credentials.map((credential) => (
                <div key={credential.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-start">
                    <div className="flex items-start gap-3">
                      <div className="text-2xl">{getDeviceIcon(credential.platform || '')}</div>
                      <div>
                        <h4 className="font-medium text-gray-800">
                          {credential.device_name || credential.authenticator_name}
                        </h4>
                        <div className="text-sm text-gray-600 space-y-1">
                          <div>النوع: {credential.device_type === 'platform' ? 'مصادقة المنصة' : 'مصادقة خارجية'}</div>
                          <div>آخر استخدام: {formatDate(credential.last_used_at)}</div>
                          <div>تاريخ التسجيل: {formatDate(credential.created_at)}</div>
                          {credential.trust_level && (
                            <div>
                              مستوى الثقة: 
                              <span className={`ml-1 ${
                                credential.trust_level === 'trusted' ? 'text-green-600' :
                                credential.trust_level === 'verified' ? 'text-blue-600' :
                                credential.trust_level === 'suspicious' ? 'text-red-600' : 'text-yellow-600'
                              }`}>
                                {credential.trust_level === 'trusted' ? 'موثوق' :
                                 credential.trust_level === 'verified' ? 'محقق' :
                                 credential.trust_level === 'suspicious' ? 'مشبوه' : 'غير محقق'}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => handleRemoveCredential(credential.id)}
                      disabled={isRemoving}
                      className="text-red-600 hover:text-red-800 p-2 rounded transition-colors disabled:opacity-50"
                      title="حذف"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Preferences */}
        {biometricStatus?.biometric_enabled && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">التفضيلات</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  طريقة المصادقة المفضلة
                </label>
                <select
                  value={preferredMethod}
                  onChange={(e) => setPreferredMethod(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="password">كلمة المرور فقط</option>
                  <option value="biometric">المصادقة البيومترية فقط</option>
                  <option value="both">كلاهما (مرونة أكثر)</option>
                </select>
              </div>
              <button
                onClick={handlePreferenceUpdate}
                disabled={isLoading || preferredMethod === biometricStatus.preferred_auth_method}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                حفظ التفضيلات
              </button>
            </div>
          </div>
        )}

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-800 mb-2">معلومات مفيدة</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• بياناتك البيومترية محفوظة بأمان على جهازك فقط</li>
            <li>• يمكنك استخدام أجهزة متعددة للمصادقة البيومترية</li>
            <li>• كلمة المرور ستبقى متاحة دائماً كبديل</li>
            <li>• يمكنك إلغاء المصادقة البيومترية في أي وقت</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default BiometricManagement;
