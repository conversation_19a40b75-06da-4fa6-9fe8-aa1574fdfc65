const { 
  sendBulkWhatsAppNotification, 
  getNotificationRecipients, 
  logNotificationAttempt,
  WHATSAPP_CONFIG 
} = require('./whatsappNotificationService');

const { 
  generateDocumentSignedNotification,
  generateDocumentUploadedNotification,
  validateNotificationContent 
} = require('./notificationContentService');

const { query } = require('../models/database');

/**
 * Send document signed notification
 */
const sendDocumentSignedNotification = async (documentData, userData) => {
  try {
    console.log('📱 Preparing document signed notification...', {
      documentId: documentData.id,
      userId: userData.id,
      enabled: WHATSAPP_CONFIG.enabled
    });

    if (!WHATSAPP_CONFIG.enabled) {
      console.log('WhatsApp notifications disabled - skipping');
      return { success: false, reason: 'disabled' };
    }

    // Check if user has WhatsApp notifications enabled
    const userPrefs = await getUserNotificationPreferences(userData.id);
    if (!userPrefs.whatsapp_enabled || !userPrefs.document_signed) {
      console.log('User has disabled document signed notifications');
      return { success: false, reason: 'user_disabled' };
    }

    // Get notification recipients (user + admins)
    const recipients = await getNotificationRecipients(userData.id);
    
    if (recipients.length === 0) {
      console.log('No valid recipients found for WhatsApp notification');
      return { success: false, reason: 'no_recipients' };
    }

    console.log(`Found ${recipients.length} notification recipients:`, recipients);

    // Generate notification content for user
    const userNotification = generateDocumentSignedNotification(documentData, userData, 'ar', false);
    
    // Validate content
    const validation = validateNotificationContent(userNotification);
    if (!validation.valid) {
      console.error('Invalid notification content:', validation.error);
      return { success: false, reason: 'invalid_content', error: validation.error };
    }

    // Send to user (if they have a phone number)
    const userPhone = await getUserPhoneNumber(userData.id);
    const results = [];

    if (userPhone && recipients.includes(userPhone)) {
      console.log('Sending user notification...');
      const userResult = await sendBulkWhatsAppNotification([userPhone], userNotification.message);
      results.push({ type: 'user', ...userResult });
    }

    // Send admin notifications (if there are admin numbers configured)
    const adminNumbers = recipients.filter(num => num !== userPhone);
    if (adminNumbers.length > 0) {
      console.log('Sending admin notifications...');
      const adminNotification = generateDocumentSignedNotification(documentData, userData, 'ar', true);
      const adminResult = await sendBulkWhatsAppNotification(adminNumbers, adminNotification.message);
      results.push({ type: 'admin', ...adminResult });
    }

    // Calculate overall success
    const totalSent = results.reduce((sum, r) => sum + (r.totalSent || 0), 0);
    const totalFailed = results.reduce((sum, r) => sum + (r.totalFailed || 0), 0);
    const overallSuccess = totalSent > 0;

    // Log the notification attempt
    await logNotificationAttempt(
      userData.id,
      documentData.id,
      recipients,
      userNotification.message,
      {
        success: overallSuccess,
        totalSent,
        totalFailed,
        results
      }
    );

    console.log(`✅ Document signed notification completed:`, {
      success: overallSuccess,
      totalSent,
      totalFailed
    });

    return {
      success: overallSuccess,
      totalSent,
      totalFailed,
      results
    };

  } catch (error) {
    console.error('Error sending document signed notification:', error);
    
    // Log the failed attempt
    try {
      await logNotificationAttempt(
        userData.id,
        documentData.id,
        [],
        '',
        {
          success: false,
          error: error.message,
          stack: error.stack
        }
      );
    } catch (logError) {
      console.error('Failed to log notification error:', logError);
    }

    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Send document uploaded notification (optional)
 */
const sendDocumentUploadedNotification = async (documentData, userData) => {
  try {
    if (!WHATSAPP_CONFIG.enabled) {
      return { success: false, reason: 'disabled' };
    }

    // Check if user has upload notifications enabled
    const userPrefs = await getUserNotificationPreferences(userData.id);
    if (!userPrefs.whatsapp_enabled || !userPrefs.document_uploaded) {
      return { success: false, reason: 'user_disabled' };
    }

    const recipients = await getNotificationRecipients(userData.id);
    if (recipients.length === 0) {
      return { success: false, reason: 'no_recipients' };
    }

    const notification = generateDocumentUploadedNotification(documentData, userData, 'ar');
    const validation = validateNotificationContent(notification);
    
    if (!validation.valid) {
      return { success: false, reason: 'invalid_content', error: validation.error };
    }

    const result = await sendBulkWhatsAppNotification(recipients, notification.message);
    
    await logNotificationAttempt(
      userData.id,
      documentData.id,
      recipients,
      notification.message,
      result
    );

    return result;

  } catch (error) {
    console.error('Error sending document uploaded notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get user notification preferences
 */
const getUserNotificationPreferences = async (userId) => {
  try {
    const result = await query(
      `SELECT whatsapp_notifications_enabled, notification_preferences 
       FROM users WHERE id = $1`,
      [userId]
    );

    if (result.rows.length === 0) {
      return {
        whatsapp_enabled: false,
        document_signed: false,
        document_uploaded: false,
        admin_notifications: false
      };
    }

    const user = result.rows[0];
    const prefs = user.notification_preferences || {};

    return {
      whatsapp_enabled: user.whatsapp_notifications_enabled !== false,
      document_signed: prefs.document_signed !== false,
      document_uploaded: prefs.document_uploaded === true,
      admin_notifications: prefs.admin_notifications !== false
    };

  } catch (error) {
    console.error('Error getting user notification preferences:', error);
    return {
      whatsapp_enabled: false,
      document_signed: false,
      document_uploaded: false,
      admin_notifications: false
    };
  }
};

/**
 * Get user phone number
 */
const getUserPhoneNumber = async (userId) => {
  try {
    const result = await query(
      'SELECT phone_number FROM users WHERE id = $1 AND phone_number IS NOT NULL',
      [userId]
    );
    
    return result.rows.length > 0 ? result.rows[0].phone_number : null;
  } catch (error) {
    console.error('Error getting user phone number:', error);
    return null;
  }
};

/**
 * Test notification system
 */
const testNotificationSystem = async (userId, testMessage = null) => {
  try {
    const recipients = await getNotificationRecipients(userId);
    
    if (recipients.length === 0) {
      return { success: false, reason: 'no_recipients' };
    }

    const message = testMessage || `🧪 اختبار نظام الإشعارات

هذه رسالة اختبار للتأكد من عمل نظام إشعارات WhatsApp بشكل صحيح.

التاريخ: ${new Date().toLocaleDateString('en-US')}
الوقت: ${new Date().toLocaleTimeString('en-US')}

نظام التوقيع الإلكتروني العربي 🇸🇦`;

    const result = await sendBulkWhatsAppNotification(recipients, message);
    
    await logNotificationAttempt(
      userId,
      null, // No document for test
      recipients,
      message,
      { ...result, test: true }
    );

    return result;

  } catch (error) {
    console.error('Error testing notification system:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  sendDocumentSignedNotification,
  sendDocumentUploadedNotification,
  getUserNotificationPreferences,
  getUserPhoneNumber,
  testNotificationSystem
};
