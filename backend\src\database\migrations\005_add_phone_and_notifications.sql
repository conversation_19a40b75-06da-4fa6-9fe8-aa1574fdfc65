-- Migration: Add phone number and notification preferences to users table
-- Description: Add WhatsApp notification support with phone number storage and preferences

-- Add phone number and notification columns to users table
ALTER TABLE users 
ADD COLUMN phone_number VARCHAR(20),
ADD COLUMN full_name VARCHAR(255),
ADD COLUMN whatsapp_notifications_enabled BOOLEAN DEFAULT true,
ADD COLUMN notification_preferences JSONB DEFAULT '{"document_signed": true, "document_uploaded": false, "admin_notifications": true}'::jsonb;

-- Create index for phone number lookups
CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users(phone_number) WHERE phone_number IS NOT NULL;

-- Create notification logs table for audit trail
CREATE TABLE IF NOT EXISTS notification_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
    notification_type VARCHAR(50) NOT NULL, -- 'whatsapp', 'email', 'sms'
    recipients JSONB NOT NULL, -- Array of recipient phone numbers/emails
    message_content TEXT NOT NULL,
    success BOOLEAN NOT NULL DEFAULT false,
    result_data JSONB, -- Store API response, error details, etc.
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for notification logs
CREATE INDEX IF NOT EXISTS idx_notification_logs_user_id ON notification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_document_id ON notification_logs(document_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_type ON notification_logs(notification_type);
CREATE INDEX IF NOT EXISTS idx_notification_logs_created_at ON notification_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_notification_logs_success ON notification_logs(success);

-- Add comments for documentation
COMMENT ON COLUMN users.phone_number IS 'International format phone number for WhatsApp notifications (+966501234567)';
COMMENT ON COLUMN users.whatsapp_notifications_enabled IS 'Global toggle for WhatsApp notifications for this user';
COMMENT ON COLUMN users.notification_preferences IS 'JSON object containing notification preferences for different event types';

COMMENT ON TABLE notification_logs IS 'Audit log for all notification attempts (WhatsApp, email, SMS)';
COMMENT ON COLUMN notification_logs.recipients IS 'JSON array of recipient identifiers (phone numbers, emails, etc.)';
COMMENT ON COLUMN notification_logs.result_data IS 'JSON object containing API response data, error details, message IDs, etc.';

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for notification_logs updated_at
CREATE TRIGGER update_notification_logs_updated_at 
    BEFORE UPDATE ON notification_logs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample notification preferences for existing users
UPDATE users 
SET notification_preferences = '{"document_signed": true, "document_uploaded": false, "admin_notifications": true}'::jsonb
WHERE notification_preferences IS NULL;

-- Create view for notification statistics
CREATE OR REPLACE VIEW notification_stats AS
SELECT 
    u.id as user_id,
    u.email,
    u.phone_number,
    u.whatsapp_notifications_enabled,
    COUNT(nl.id) as total_notifications,
    COUNT(CASE WHEN nl.success = true THEN 1 END) as successful_notifications,
    COUNT(CASE WHEN nl.success = false THEN 1 END) as failed_notifications,
    COUNT(CASE WHEN nl.notification_type = 'whatsapp' THEN 1 END) as whatsapp_notifications,
    MAX(nl.created_at) as last_notification_at
FROM users u
LEFT JOIN notification_logs nl ON u.id = nl.user_id
GROUP BY u.id, u.email, u.phone_number, u.whatsapp_notifications_enabled;

COMMENT ON VIEW notification_stats IS 'Summary view of notification statistics per user';
