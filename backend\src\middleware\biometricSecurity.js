const crypto = require('crypto');
const { query } = require('../models/database');
const webauthnService = require('../services/webauthnService');

/**
 * Biometric Security Middleware
 * Provides security functions for biometric authentication
 */

/**
 * Validate WebAuthn challenge
 * Ensures challenge is valid and not expired
 */
const validateChallenge = async (req, res, next) => {
  try {
    const { challengeId } = req.body;

    if (!challengeId) {
      return res.status(400).json({
        success: false,
        message: 'معرف التحدي مطلوب'
      });
    }

    // Check if challenge exists and is not expired
    const challengeResult = await query(
      `SELECT user_id, timestamp FROM biometric_auth_logs 
       WHERE challenge = $1 AND event_type = 'challenge_generated' 
       AND timestamp > NOW() - INTERVAL '5 minutes'
       ORDER BY timestamp DESC LIMIT 1`,
      [challengeId]
    );

    if (challengeResult.rows.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'التحدي غير صالح أو منتهي الصلاحية'
      });
    }

    // Add challenge info to request
    req.challengeInfo = challengeResult.rows[0];
    next();

  } catch (error) {
    console.error('خطأ في التحقق من التحدي:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في التحقق من التحدي'
    });
  }
};

/**
 * Validate device information
 * Ensures device info is properly formatted and secure
 */
const validateDeviceInfo = (req, res, next) => {
  try {
    const { deviceInfo } = req.body;

    if (!deviceInfo) {
      req.body.deviceInfo = {};
      return next();
    }

    // Sanitize device info
    const sanitizedDeviceInfo = {
      platform: sanitizeString(deviceInfo.platform),
      browser: sanitizeString(deviceInfo.browser),
      browserVersion: sanitizeString(deviceInfo.browserVersion),
      osVersion: sanitizeString(deviceInfo.osVersion),
      deviceModel: sanitizeString(deviceInfo.deviceModel),
      deviceName: sanitizeString(deviceInfo.deviceName),
      authenticatorName: sanitizeString(deviceInfo.authenticatorName),
      capabilities: typeof deviceInfo.capabilities === 'object' ? deviceInfo.capabilities : {},
      timestamp: new Date().toISOString()
    };

    // Add device fingerprint
    sanitizedDeviceInfo.deviceId = generateDeviceFingerprint(sanitizedDeviceInfo);

    req.body.deviceInfo = sanitizedDeviceInfo;
    next();

  } catch (error) {
    console.error('خطأ في التحقق من معلومات الجهاز:', error);
    res.status(400).json({
      success: false,
      message: 'معلومات الجهاز غير صالحة'
    });
  }
};

/**
 * Check user biometric status
 * Validates user can perform biometric operations
 */
const checkBiometricStatus = async (req, res, next) => {
  try {
    const { userId } = req.user;

    const userResult = await query(
      `SELECT biometric_enabled, biometric_locked_until, failed_biometric_attempts,
              biometric_consent_given
       FROM users WHERE id = $1`,
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = userResult.rows[0];

    // Check if biometric is locked
    if (user.biometric_locked_until && new Date() < user.biometric_locked_until) {
      return res.status(423).json({
        success: false,
        message: 'المصادقة البيومترية مؤقتة مؤقتاً. حاول مرة أخرى لاحقاً',
        lockedUntil: user.biometric_locked_until
      });
    }

    // Check consent for registration operations
    if (req.path.includes('/register') && !user.biometric_consent_given) {
      return res.status(403).json({
        success: false,
        message: 'يجب الموافقة على استخدام المصادقة البيومترية أولاً'
      });
    }

    req.userBiometricStatus = user;
    next();

  } catch (error) {
    console.error('خطأ في التحقق من حالة المصادقة البيومترية:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في التحقق من حالة المصادقة البيومترية'
    });
  }
};

/**
 * Rate limiting for biometric operations per user
 */
const biometricUserRateLimit = async (req, res, next) => {
  try {
    const userId = req.user?.userId || req.challengeInfo?.user_id;
    const ipAddress = req.ip;
    const timeWindow = 15 * 60 * 1000; // 15 minutes
    const maxAttempts = 5;

    if (!userId) {
      return next();
    }

    // Count recent attempts for this user
    const attemptsResult = await query(
      `SELECT COUNT(*) as count FROM biometric_auth_logs 
       WHERE user_id = $1 AND timestamp > NOW() - INTERVAL '15 minutes'
       AND event_type IN ('authentication', 'registration')`,
      [userId]
    );

    const attempts = parseInt(attemptsResult.rows[0].count);

    if (attempts >= maxAttempts) {
      // Log rate limit violation
      await query(
        `INSERT INTO biometric_auth_logs (user_id, event_type, event_status, ip_address, error_code, error_message)
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [userId, 'failure', 'error', ipAddress, 'RATE_LIMIT', 'Too many biometric attempts']
      );

      return res.status(429).json({
        success: false,
        message: 'تم تجاوز الحد المسموح من المحاولات. حاول مرة أخرى لاحقاً'
      });
    }

    next();

  } catch (error) {
    console.error('خطأ في فحص معدل المحاولات:', error);
    next(); // Continue on error to avoid blocking legitimate requests
  }
};

/**
 * Validate WebAuthn origin
 */
const validateOrigin = (req, res, next) => {
  try {
    const origin = req.get('Origin') || req.get('Referer');
    const allowedOrigins = [
      process.env.FRONTEND_URL || 'http://localhost:3000',
      process.env.WEBAUTHN_ORIGIN || 'http://localhost:3000',
      'https://localhost:3000' // For HTTPS development
    ];

    if (!origin || !allowedOrigins.some(allowed => origin.startsWith(allowed))) {
      return res.status(403).json({
        success: false,
        message: 'المصدر غير مصرح به'
      });
    }

    req.validatedOrigin = origin;
    next();

  } catch (error) {
    console.error('خطأ في التحقق من المصدر:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في التحقق من المصدر'
    });
  }
};

/**
 * Log biometric security events
 */
const logSecurityEvent = (eventType, details = {}) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.userId || req.challengeInfo?.user_id;
      
      await query(
        `INSERT INTO biometric_auth_logs (user_id, event_type, event_status, ip_address, user_agent, device_info, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          userId,
          eventType,
          'success',
          req.ip,
          req.get('User-Agent'),
          { ...req.body.deviceInfo, ...details },
          new Date()
        ]
      );

      next();

    } catch (error) {
      console.error('خطأ في تسجيل الحدث الأمني:', error);
      next(); // Continue on error
    }
  };
};

/**
 * Sanitize string input
 */
function sanitizeString(input) {
  if (typeof input !== 'string') return '';
  return input.trim().replace(/[<>]/g, '').substring(0, 100);
}

/**
 * Generate device fingerprint
 */
function generateDeviceFingerprint(deviceInfo) {
  const fingerprint = [
    deviceInfo.platform,
    deviceInfo.browser,
    deviceInfo.browserVersion,
    deviceInfo.osVersion,
    deviceInfo.deviceModel
  ].filter(Boolean).join('|');

  return crypto.createHash('sha256').update(fingerprint).digest('hex').substring(0, 16);
}

/**
 * Verify HTTPS requirement for biometric operations
 */
const requireHTTPS = (req, res, next) => {
  // Skip HTTPS check in development
  if (process.env.NODE_ENV === 'development') {
    return next();
  }

  if (!req.secure && req.get('X-Forwarded-Proto') !== 'https') {
    return res.status(400).json({
      success: false,
      message: 'المصادقة البيومترية تتطلب اتصال آمن (HTTPS)'
    });
  }

  next();
};

module.exports = {
  validateChallenge,
  validateDeviceInfo,
  checkBiometricStatus,
  biometricUserRateLimit,
  validateOrigin,
  logSecurityEvent,
  requireHTTPS
};
