const { query, pool } = require('../models/database');
const DatabaseOptimizationService = require('./databaseOptimization');

/**
 * Advanced database optimization service with automated maintenance
 */
class AdvancedDatabaseOptimization {
  
  constructor() {
    this.maintenanceSchedule = {
      vacuum: '0 2 * * 0', // Weekly at 2 AM Sunday
      analyze: '0 3 * * *', // Daily at 3 AM
      reindex: '0 4 * * 0', // Weekly at 4 AM Sunday
      cleanup: '0 1 * * *'  // Daily at 1 AM
    };
    this.isMaintenanceRunning = false;
  }

  /**
   * Comprehensive database health assessment
   */
  async performHealthAssessment() {
    const assessment = {
      timestamp: new Date().toISOString(),
      connectionPool: await this.assessConnectionPool(),
      tableHealth: await this.assessTableHealth(),
      indexEfficiency: await this.assessIndexEfficiency(),
      queryPerformance: await this.assessQueryPerformance(),
      diskUsage: await this.assessDiskUsage(),
      recommendations: []
    };

    // Generate recommendations based on assessment
    assessment.recommendations = this.generateRecommendations(assessment);
    
    return assessment;
  }

  /**
   * Assess connection pool health
   */
  async assessConnectionPool() {
    const poolStats = {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount
    };

    const connectionMetrics = await query(`
      SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections,
        max(now() - query_start) as longest_query_duration,
        avg(now() - query_start) as avg_query_duration
      FROM pg_stat_activity 
      WHERE datname = current_database()
    `);

    return {
      pool: poolStats,
      database: connectionMetrics.rows[0],
      efficiency: poolStats.idleCount / poolStats.totalCount,
      utilization: (poolStats.totalCount - poolStats.idleCount) / poolStats.totalCount
    };
  }

  /**
   * Assess table health and bloat
   */
  async assessTableHealth() {
    const tableStats = await query(`
      SELECT 
        schemaname,
        tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        n_live_tup as live_tuples,
        n_dead_tup as dead_tuples,
        CASE 
          WHEN n_live_tup > 0 
          THEN round((n_dead_tup::float / n_live_tup::float) * 100, 2)
          ELSE 0 
        END as dead_tuple_ratio,
        last_vacuum,
        last_autovacuum,
        last_analyze,
        last_autoanalyze
      FROM pg_stat_user_tables
      ORDER BY dead_tuple_ratio DESC
    `);

    const tableSizes = await query(`
      SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    `);

    return {
      statistics: tableStats.rows,
      sizes: tableSizes.rows,
      bloatedTables: tableStats.rows.filter(t => t.dead_tuple_ratio > 20),
      needsVacuum: tableStats.rows.filter(t => t.dead_tuple_ratio > 10),
      needsAnalyze: tableStats.rows.filter(t => {
        const lastAnalyze = t.last_analyze || t.last_autoanalyze;
        if (!lastAnalyze) return true;
        const daysSinceAnalyze = (Date.now() - new Date(lastAnalyze).getTime()) / (1000 * 60 * 60 * 24);
        return daysSinceAnalyze > 7;
      })
    };
  }

  /**
   * Assess index efficiency
   */
  async assessIndexEfficiency() {
    const indexUsage = await query(`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_tup_read,
        idx_tup_fetch,
        CASE 
          WHEN idx_tup_read > 0 
          THEN round((idx_tup_fetch::float / idx_tup_read::float) * 100, 2)
          ELSE 0 
        END as efficiency_ratio
      FROM pg_stat_user_indexes
      ORDER BY idx_tup_read DESC
    `);

    const unusedIndexes = await query(`
      SELECT 
        schemaname,
        tablename,
        indexname,
        pg_size_pretty(pg_relation_size(indexrelid)) as size
      FROM pg_stat_user_indexes
      WHERE idx_scan = 0
        AND idx_tup_read = 0
        AND idx_tup_fetch = 0
    `);

    const duplicateIndexes = await query(`
      SELECT 
        t.tablename,
        array_agg(t.indexname) as duplicate_indexes,
        t.column_names
      FROM (
        SELECT 
          tablename,
          indexname,
          array_to_string(array_agg(column_name ORDER BY ordinal_position), ',') as column_names
        FROM information_schema.statistics 
        WHERE table_schema = 'public'
        GROUP BY tablename, indexname
      ) t
      GROUP BY t.tablename, t.column_names
      HAVING count(*) > 1
    `);

    return {
      usage: indexUsage.rows,
      unused: unusedIndexes.rows,
      duplicates: duplicateIndexes.rows,
      lowEfficiency: indexUsage.rows.filter(i => i.efficiency_ratio < 50 && i.idx_tup_read > 1000)
    };
  }

  /**
   * Assess query performance
   */
  async assessQueryPerformance() {
    try {
      // This requires pg_stat_statements extension
      const slowQueries = await query(`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows,
          100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
        FROM pg_stat_statements 
        WHERE mean_time > 100
        ORDER BY mean_time DESC 
        LIMIT 20
      `);

      const queryStats = await query(`
        SELECT 
          count(*) as total_queries,
          avg(mean_time) as avg_execution_time,
          max(mean_time) as max_execution_time,
          sum(calls) as total_calls
        FROM pg_stat_statements
      `);

      return {
        slowQueries: slowQueries.rows,
        statistics: queryStats.rows[0] || {},
        available: true
      };
    } catch (error) {
      return {
        slowQueries: [],
        statistics: {},
        available: false,
        error: 'pg_stat_statements extension not available'
      };
    }
  }

  /**
   * Assess disk usage
   */
  async assessDiskUsage() {
    const databaseSize = await query(`
      SELECT 
        pg_size_pretty(pg_database_size(current_database())) as database_size,
        pg_database_size(current_database()) as database_size_bytes
    `);

    const tablespaceSizes = await query(`
      SELECT 
        spcname as tablespace_name,
        pg_size_pretty(pg_tablespace_size(spcname)) as size
      FROM pg_tablespace
    `);

    return {
      database: databaseSize.rows[0],
      tablespaces: tablespaceSizes.rows
    };
  }

  /**
   * Generate optimization recommendations
   */
  generateRecommendations(assessment) {
    const recommendations = [];

    // Connection pool recommendations
    if (assessment.connectionPool.utilization > 0.8) {
      recommendations.push({
        category: 'connection_pool',
        priority: 'high',
        title: 'زيادة حجم تجمع الاتصالات',
        description: 'معدل استخدام تجمع الاتصالات مرتفع (80%+)',
        action: 'زيادة قيمة DB_POOL_MAX في متغيرات البيئة'
      });
    }

    // Table maintenance recommendations
    if (assessment.tableHealth.bloatedTables.length > 0) {
      recommendations.push({
        category: 'maintenance',
        priority: 'medium',
        title: 'تنظيف الجداول المنتفخة',
        description: `${assessment.tableHealth.bloatedTables.length} جداول تحتاج تنظيف`,
        action: 'تشغيل VACUUM FULL على الجداول المتأثرة'
      });
    }

    // Index recommendations
    if (assessment.indexEfficiency.unused.length > 0) {
      recommendations.push({
        category: 'indexes',
        priority: 'low',
        title: 'حذف الفهارس غير المستخدمة',
        description: `${assessment.indexEfficiency.unused.length} فهارس غير مستخدمة`,
        action: 'مراجعة وحذف الفهارس غير الضرورية'
      });
    }

    // Query performance recommendations
    if (assessment.queryPerformance.available && assessment.queryPerformance.slowQueries.length > 0) {
      recommendations.push({
        category: 'performance',
        priority: 'high',
        title: 'تحسين الاستعلامات البطيئة',
        description: `${assessment.queryPerformance.slowQueries.length} استعلامات بطيئة`,
        action: 'مراجعة وتحسين الاستعلامات البطيئة'
      });
    }

    return recommendations;
  }

  /**
   * Run automated maintenance
   */
  async runAutomatedMaintenance() {
    if (this.isMaintenanceRunning) {
      console.log('⚠️ Maintenance already running, skipping...');
      return { status: 'skipped', reason: 'Already running' };
    }

    this.isMaintenanceRunning = true;
    const results = [];

    try {
      console.log('🔧 Starting automated database maintenance...');

      // 1. Update table statistics
      console.log('📊 Updating table statistics...');
      await query('ANALYZE');
      results.push({ task: 'analyze', status: 'completed', timestamp: new Date() });

      // 2. Vacuum tables that need it
      const tableHealth = await this.assessTableHealth();
      for (const table of tableHealth.needsVacuum.slice(0, 5)) { // Limit to 5 tables per run
        console.log(`🧹 Vacuuming table: ${table.tablename}`);
        await query(`VACUUM ${table.tablename}`);
        results.push({ 
          task: 'vacuum', 
          table: table.tablename, 
          status: 'completed', 
          timestamp: new Date() 
        });
      }

      // 3. Reindex if needed (weekly)
      const now = new Date();
      if (now.getDay() === 0) { // Sunday
        console.log('🔄 Reindexing database...');
        await query('REINDEX DATABASE CONCURRENTLY');
        results.push({ task: 'reindex', status: 'completed', timestamp: new Date() });
      }

      // 4. Clean up old data
      console.log('🗑️ Cleaning up old data...');
      
      // Clean old logs (older than 30 days)
      const logCleanup = await query(`
        DELETE FROM logs 
        WHERE timestamp < NOW() - INTERVAL '30 days'
      `);
      
      // Clean old analytics events (older than 90 days)
      const analyticsCleanup = await query(`
        DELETE FROM analytics_events 
        WHERE timestamp < NOW() - INTERVAL '90 days'
      `);

      results.push({ 
        task: 'cleanup', 
        status: 'completed', 
        logsDeleted: logCleanup.rowCount,
        analyticsDeleted: analyticsCleanup.rowCount,
        timestamp: new Date() 
      });

      console.log('✅ Automated maintenance completed successfully');
      
      return {
        status: 'completed',
        results,
        duration: Date.now() - now.getTime()
      };

    } catch (error) {
      console.error('❌ Automated maintenance failed:', error);
      results.push({ 
        task: 'maintenance', 
        status: 'failed', 
        error: error.message, 
        timestamp: new Date() 
      });
      
      return {
        status: 'failed',
        error: error.message,
        results
      };
    } finally {
      this.isMaintenanceRunning = false;
    }
  }

  /**
   * Get maintenance status
   */
  getMaintenanceStatus() {
    return {
      isRunning: this.isMaintenanceRunning,
      schedule: this.maintenanceSchedule,
      lastRun: this.lastMaintenanceRun || null
    };
  }

  /**
   * Schedule automated maintenance
   */
  scheduleAutomatedMaintenance() {
    // Run maintenance every 6 hours
    setInterval(async () => {
      await this.runAutomatedMaintenance();
    }, 6 * 60 * 60 * 1000);

    console.log('📅 Automated maintenance scheduled every 6 hours');
  }
}

module.exports = new AdvancedDatabaseOptimization();
