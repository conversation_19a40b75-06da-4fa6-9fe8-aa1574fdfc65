const os = require('os');
const fs = require('fs').promises;

// Performance metrics storage
const metrics = {
  uploads: {
    total: 0,
    successful: 0,
    failed: 0,
    totalSize: 0,
    averageSpeed: 0,
    largestFile: 0,
    averageTime: 0
  },
  system: {
    peakMemoryUsage: 0,
    averageMemoryUsage: 0,
    cpuUsage: [],
    diskUsage: 0
  },
  errors: {
    timeouts: 0,
    memoryErrors: 0,
    diskErrors: 0,
    networkErrors: 0,
    validationErrors: 0
  }
};

// Performance monitoring middleware
const performanceMonitor = (req, res, next) => {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();
  const startCpu = process.cpuUsage();

  // Track request start
  req.performanceStart = {
    time: startTime,
    memory: startMemory,
    cpu: startCpu
  };

  // Override response methods to capture metrics
  const originalSend = res.send;
  const originalJson = res.json;

  res.send = function(data) {
    captureMetrics(req, res, startTime, startMemory, startCpu);
    originalSend.call(this, data);
  };

  res.json = function(data) {
    captureMetrics(req, res, startTime, startMemory, startCpu);
    originalJson.call(this, data);
  };

  next();
};

// Capture performance metrics
const captureMetrics = (req, res, startTime, startMemory, startCpu) => {
  const endTime = Date.now();
  const endMemory = process.memoryUsage();
  const endCpu = process.cpuUsage(startCpu);
  
  const duration = endTime - startTime;
  const memoryDiff = endMemory.heapUsed - startMemory.heapUsed;
  const cpuUsage = (endCpu.user + endCpu.system) / 1000; // Convert to milliseconds

  // Update system metrics
  metrics.system.peakMemoryUsage = Math.max(metrics.system.peakMemoryUsage, endMemory.heapUsed);
  metrics.system.cpuUsage.push(cpuUsage);
  
  // Keep only last 100 CPU measurements
  if (metrics.system.cpuUsage.length > 100) {
    metrics.system.cpuUsage.shift();
  }

  // Track upload-specific metrics
  if (req.file && req.path.includes('upload') || req.path.includes('sign')) {
    metrics.uploads.total++;
    
    if (res.statusCode >= 200 && res.statusCode < 300) {
      metrics.uploads.successful++;
      
      if (req.file) {
        const fileSize = req.file.size;
        const speed = fileSize / (duration / 1000); // bytes per second
        
        metrics.uploads.totalSize += fileSize;
        metrics.uploads.largestFile = Math.max(metrics.uploads.largestFile, fileSize);
        
        // Update average speed (simple moving average)
        const totalSuccessful = metrics.uploads.successful;
        metrics.uploads.averageSpeed = (metrics.uploads.averageSpeed * (totalSuccessful - 1) + speed) / totalSuccessful;
        metrics.uploads.averageTime = (metrics.uploads.averageTime * (totalSuccessful - 1) + duration) / totalSuccessful;
      }
    } else {
      metrics.uploads.failed++;
      
      // Track error types
      if (res.statusCode === 408) {
        metrics.errors.timeouts++;
      } else if (res.statusCode === 507) {
        metrics.errors.memoryErrors++;
      } else if (res.statusCode === 413) {
        metrics.errors.diskErrors++;
      } else if (res.statusCode >= 500) {
        metrics.errors.networkErrors++;
      } else if (res.statusCode >= 400) {
        metrics.errors.validationErrors++;
      }
    }
  }

  // Log performance data for large files or slow requests
  if ((req.file && req.file.size > 50 * 1024 * 1024) || duration > 30000) {
    console.log('📊 Performance Alert:', {
      path: req.path,
      method: req.method,
      duration: `${duration}ms`,
      fileSize: req.file ? `${(req.file.size / 1024 / 1024).toFixed(2)}MB` : 'N/A',
      memoryUsed: `${(memoryDiff / 1024 / 1024).toFixed(2)}MB`,
      cpuTime: `${cpuUsage.toFixed(2)}ms`,
      statusCode: res.statusCode
    });
  }
};

// Get system health metrics
const getSystemHealth = async () => {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  const uptime = process.uptime();
  
  // Get disk usage
  let diskUsage = 0;
  try {
    const stats = await fs.stat('./uploads');
    diskUsage = stats.size;
  } catch (error) {
    // Directory might not exist yet
  }

  return {
    memory: {
      used: Math.round(memUsage.heapUsed / 1024 / 1024),
      total: Math.round(memUsage.heapTotal / 1024 / 1024),
      rss: Math.round(memUsage.rss / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
      percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
    },
    cpu: {
      user: Math.round(cpuUsage.user / 1000),
      system: Math.round(cpuUsage.system / 1000),
      usage: os.loadavg()[0] // 1-minute load average
    },
    system: {
      uptime: Math.round(uptime),
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      totalMemory: Math.round(os.totalmem() / 1024 / 1024),
      freeMemory: Math.round(os.freemem() / 1024 / 1024)
    },
    disk: {
      uploadsSize: Math.round(diskUsage / 1024 / 1024)
    }
  };
};

// Get performance metrics
const getPerformanceMetrics = () => {
  const avgCpu = metrics.system.cpuUsage.length > 0 
    ? metrics.system.cpuUsage.reduce((a, b) => a + b, 0) / metrics.system.cpuUsage.length 
    : 0;

  return {
    uploads: {
      ...metrics.uploads,
      successRate: metrics.uploads.total > 0 
        ? Math.round((metrics.uploads.successful / metrics.uploads.total) * 100) 
        : 0,
      averageFileSize: metrics.uploads.successful > 0 
        ? Math.round(metrics.uploads.totalSize / metrics.uploads.successful / 1024 / 1024) 
        : 0,
      averageSpeedMBps: Math.round(metrics.uploads.averageSpeed / 1024 / 1024 * 100) / 100,
      largestFileMB: Math.round(metrics.uploads.largestFile / 1024 / 1024 * 100) / 100,
      averageTimeSeconds: Math.round(metrics.uploads.averageTime / 1000 * 100) / 100
    },
    system: {
      peakMemoryMB: Math.round(metrics.system.peakMemoryUsage / 1024 / 1024),
      averageCpuMs: Math.round(avgCpu * 100) / 100
    },
    errors: metrics.errors
  };
};

// Reset metrics
const resetMetrics = () => {
  metrics.uploads = {
    total: 0,
    successful: 0,
    failed: 0,
    totalSize: 0,
    averageSpeed: 0,
    largestFile: 0,
    averageTime: 0
  };
  metrics.system = {
    peakMemoryUsage: 0,
    averageMemoryUsage: 0,
    cpuUsage: [],
    diskUsage: 0
  };
  metrics.errors = {
    timeouts: 0,
    memoryErrors: 0,
    diskErrors: 0,
    networkErrors: 0,
    validationErrors: 0
  };
};

// Health check endpoint handler
const healthCheck = async (req, res) => {
  try {
    const systemHealth = await getSystemHealth();
    const performanceMetrics = getPerformanceMetrics();
    
    // Determine health status
    let status = 'healthy';
    const warnings = [];
    
    if (systemHealth.memory.percentage > 80) {
      status = 'warning';
      warnings.push('High memory usage');
    }
    
    if (systemHealth.cpu.usage > 2) {
      status = 'warning';
      warnings.push('High CPU load');
    }
    
    if (performanceMetrics.uploads.successRate < 90 && performanceMetrics.uploads.total > 10) {
      status = 'warning';
      warnings.push('Low upload success rate');
    }
    
    if (systemHealth.system.freeMemory < 100) { // Less than 100MB free
      status = 'critical';
      warnings.push('Very low free memory');
    }

    res.json({
      status,
      timestamp: new Date().toISOString(),
      warnings,
      system: systemHealth,
      performance: performanceMetrics,
      arabic: {
        status: status === 'healthy' ? 'سليم' : status === 'warning' ? 'تحذير' : 'حرج',
        message: status === 'healthy' 
          ? 'النظام يعمل بشكل طبيعي' 
          : warnings.length > 0 
            ? `تحذيرات: ${warnings.join(', ')}` 
            : 'حالة النظام غير مستقرة'
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'error',
      error: 'Failed to get system health',
      arabic: {
        status: 'خطأ',
        message: 'فشل في فحص حالة النظام'
      }
    });
  }
};

// Periodic cleanup and optimization
const performMaintenance = () => {
  // Trigger garbage collection if available
  if (global.gc) {
    const memBefore = process.memoryUsage().heapUsed;
    global.gc();
    const memAfter = process.memoryUsage().heapUsed;
    const freed = memBefore - memAfter;
    
    if (freed > 10 * 1024 * 1024) { // More than 10MB freed
      console.log(`🗑️ Garbage collection freed ${Math.round(freed / 1024 / 1024)}MB`);
    }
  }
  
  // Clean up old metrics
  if (metrics.system.cpuUsage.length > 1000) {
    metrics.system.cpuUsage = metrics.system.cpuUsage.slice(-100);
  }
};

// Start periodic maintenance (every 5 minutes)
setInterval(performMaintenance, 5 * 60 * 1000);

module.exports = {
  performanceMonitor,
  getSystemHealth,
  getPerformanceMetrics,
  resetMetrics,
  healthCheck
};
