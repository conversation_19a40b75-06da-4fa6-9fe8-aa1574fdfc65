const fs = require('fs');
const path = require('path');
const { query } = require('../src/models/database');

async function runMigration() {
  try {
    console.log('Running user roles migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../../database/migrations/003_add_user_roles.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          console.log(`Executing statement ${i + 1}/${statements.length}...`);
          await query(statement);
          console.log(`✓ Statement ${i + 1} executed successfully`);
        } catch (error) {
          if (error.message.includes('already exists') || error.message.includes('column "role" of relation "users" already exists')) {
            console.log(`⚠ Statement ${i + 1} skipped (already exists)`);
          } else {
            console.error(`✗ Error executing statement ${i + 1}:`, error.message);
            throw error;
          }
        }
      }
    }
    
    console.log('✅ Migration completed successfully!');
    
    // Verify the migration
    const result = await query('SELECT COUNT(*) as count FROM users WHERE role IS NOT NULL');
    console.log(`📊 Users with roles: ${result.rows[0].count}`);
    
    const adminCount = await query('SELECT COUNT(*) as count FROM users WHERE role = \'admin\'');
    console.log(`👑 Admin users: ${adminCount.rows[0].count}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration();
