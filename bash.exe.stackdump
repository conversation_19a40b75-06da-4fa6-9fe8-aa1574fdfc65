Stack trace:
Frame         Function      Args
0007FFFFBBC0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFBBC0, 0007FFFFAAC0) msys-2.0.dll+0x1FEBA
0007FFFFBBC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE98) msys-2.0.dll+0x67F9
0007FFFFBBC0  000210046832 (000210285FF9, 0007FFFFBA78, 0007FFFFBBC0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBC0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBC0  0002100690B4 (0007FFFFBBD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBEA0  00021006A49D (0007FFFFBBD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF81C200000 ntdll.dll
7FF81B5D0000 KERNEL32.DLL
7FF819800000 KERNELBASE.dll
7FF81AFF0000 USER32.dll
000210040000 msys-2.0.dll
7FF819BF0000 win32u.dll
7FF81AAD0000 GDI32.dll
7FF819C20000 gdi32full.dll
7FF8194D0000 msvcp_win.dll
7FF819610000 ucrtbase.dll
7FF81AE00000 advapi32.dll
7FF81BDA0000 msvcrt.dll
7FF81AB20000 sechost.dll
7FF81AED0000 RPCRT4.dll
7FF818860000 CRYPTBASE.DLL
7FF819760000 bcryptPrimitives.dll
7FF81AD70000 IMM32.DLL
