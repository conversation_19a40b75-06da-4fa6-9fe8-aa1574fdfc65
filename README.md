# E-Signature System

A secure electronic signature system built with Node.js, React, and PostgreSQL.

## Features

- User authentication with JWT
- Secure signature upload and storage
- PDF document signing with embedded signatures
- Document history and management
- Encrypted signature storage
- Serial number generation for document integrity

## Tech Stack

### Backend
- Node.js with Express
- PostgreSQL database
- JWT authentication
- pdf-lib for PDF processing
- bcrypt for password hashing

### Frontend
- React with TypeScript
- Tailwind CSS for styling
- Axios for API communication
- React Router for navigation

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- PostgreSQL database
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd esign
```

2. Install backend dependencies
```bash
cd backend
npm install
```

3. Install frontend dependencies
```bash
cd ../frontend
npm install
```

4. Set up environment variables
```bash
cp backend/.env.example backend/.env
# Edit .env with your database credentials
```

5. Run database migrations
```bash
# Connect to PostgreSQL and run the migration files in database/migrations/
```

6. Start the development servers
```bash
# Backend (from backend directory)
npm run dev

# Frontend (from frontend directory)
npm start
```

## Project Structure

```
esign/
├── backend/           # Node.js backend
│   ├── src/
│   │   ├── controllers/
│   │   ├── middleware/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   └── utils/
│   └── tests/
├── frontend/          # React frontend
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   └── utils/
│   └── public/
└── database/          # Database migrations
    └── migrations/
```

## API Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/signatures/upload` - Upload signature
- `POST /api/documents/upload` - Upload and sign document
- `GET /api/documents/history/:userId` - Get document history

## License

MIT License
