const multer = require('multer');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { Readable } = require('stream');

// Create a custom storage engine for streaming large files
class StreamingStorage {
  constructor(options = {}) {
    this.destination = options.destination || './uploads/temp';
    this.maxFileSize = options.maxFileSize || Infinity;
    this.allowedMimeTypes = options.allowedMimeTypes || [];
  }

  async _handleFile(req, file, cb) {
    try {
      // Ensure upload directory exists
      await fs.mkdir(this.destination, { recursive: true });

      // Generate unique filename
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const filename = file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname);
      const filepath = path.join(this.destination, filename);

      // Create write stream
      const writeStream = require('fs').createWriteStream(filepath);
      let uploadedBytes = 0;
      let hasError = false;

      // Handle stream events
      writeStream.on('error', (error) => {
        hasError = true;
        console.error('Write stream error:', error);
        cb(error);
      });

      file.stream.on('data', (chunk) => {
        uploadedBytes += chunk.length;
        
        // Check file size limit if specified
        if (this.maxFileSize !== Infinity && uploadedBytes > this.maxFileSize) {
          hasError = true;
          writeStream.destroy();
          fs.unlink(filepath).catch(() => {}); // Clean up partial file
          return cb(new Error('File too large'));
        }

        // Update progress (could be used for progress tracking)
        req.uploadProgress = {
          filename: file.originalname,
          uploadedBytes,
          timestamp: Date.now()
        };
      });

      file.stream.on('end', () => {
        if (!hasError) {
          writeStream.end();
        }
      });

      writeStream.on('finish', () => {
        if (!hasError) {
          cb(null, {
            destination: this.destination,
            filename: filename,
            path: filepath,
            size: uploadedBytes
          });
        }
      });

      // Pipe the file stream to write stream
      file.stream.pipe(writeStream);

    } catch (error) {
      console.error('Streaming storage error:', error);
      cb(error);
    }
  }

  _removeFile(req, file, cb) {
    fs.unlink(file.path)
      .then(() => cb())
      .catch(cb);
  }
}

// Create streaming multer configuration
const createStreamingUpload = (options = {}) => {
  const storage = new StreamingStorage(options);
  
  return multer({
    storage: storage,
    limits: {
      files: options.maxFiles || 1,
      // Remove fileSize limit to handle in streaming storage
    },
    fileFilter: (req, file, cb) => {
      // Basic file type validation
      if (options.allowedMimeTypes && options.allowedMimeTypes.length > 0) {
        if (!options.allowedMimeTypes.includes(file.mimetype)) {
          const allowedExtensions = options.allowedExtensions || [];
          const fileExtension = path.extname(file.originalname).toLowerCase();
          
          if (!allowedExtensions.includes(fileExtension)) {
            return cb(new Error(`File type not allowed: ${file.mimetype}`), false);
          }
        }
      }
      
      cb(null, true);
    }
  });
};

// Memory-efficient multer configuration for large files
const createMemoryEfficientUpload = (options = {}) => {
  return multer({
    storage: multer.memoryStorage(),
    limits: {
      files: options.maxFiles || 1,
      fieldSize: options.fieldSize || 50 * 1024 * 1024, // 50MB for form fields
    },
    fileFilter: (req, file, cb) => {
      console.log('Processing file:', {
        originalname: file.originalname,
        mimetype: file.mimetype,
        fieldname: file.fieldname
      });

      // Validate file type
      if (options.allowedMimeTypes && options.allowedMimeTypes.length > 0) {
        const isValidMimeType = options.allowedMimeTypes.includes(file.mimetype);
        const allowedExtensions = options.allowedExtensions || [];
        const fileExtension = path.extname(file.originalname).toLowerCase();
        const isValidExtension = allowedExtensions.includes(fileExtension);

        if (!isValidMimeType && !isValidExtension) {
          return cb(new Error(`نوع الملف غير مدعوم: ${file.mimetype}. الأنواع المدعومة: ${options.allowedMimeTypes.join(', ')}`), false);
        }
      }

      cb(null, true);
    }
  });
};

// Progress tracking middleware
const trackUploadProgress = (req, res, next) => {
  const originalSend = res.send;
  const originalJson = res.json;

  // Track upload start time
  req.uploadStartTime = Date.now();

  // Override response methods to include timing
  res.send = function(data) {
    if (req.uploadStartTime) {
      const uploadDuration = Date.now() - req.uploadStartTime;
      console.log(`Upload completed in ${uploadDuration}ms`);
      
      if (req.file) {
        console.log(`File: ${req.file.originalname}, Size: ${req.file.size} bytes, Speed: ${(req.file.size / uploadDuration * 1000 / 1024 / 1024).toFixed(2)} MB/s`);
      }
    }
    originalSend.call(this, data);
  };

  res.json = function(data) {
    if (req.uploadStartTime) {
      const uploadDuration = Date.now() - req.uploadStartTime;
      console.log(`Upload completed in ${uploadDuration}ms`);
      
      if (req.file) {
        console.log(`File: ${req.file.originalname}, Size: ${req.file.size} bytes, Speed: ${(req.file.size / uploadDuration * 1000 / 1024 / 1024).toFixed(2)} MB/s`);
      }
    }
    originalJson.call(this, data);
  };

  next();
};

// Memory monitoring middleware
const monitorMemoryUsage = (req, res, next) => {
  const memBefore = process.memoryUsage();
  
  res.on('finish', () => {
    const memAfter = process.memoryUsage();
    const memDiff = {
      rss: memAfter.rss - memBefore.rss,
      heapUsed: memAfter.heapUsed - memBefore.heapUsed,
      heapTotal: memAfter.heapTotal - memBefore.heapTotal,
      external: memAfter.external - memBefore.external
    };
    
    console.log('Memory usage change:', {
      rss: `${(memDiff.rss / 1024 / 1024).toFixed(2)} MB`,
      heapUsed: `${(memDiff.heapUsed / 1024 / 1024).toFixed(2)} MB`,
      heapTotal: `${(memDiff.heapTotal / 1024 / 1024).toFixed(2)} MB`,
      external: `${(memDiff.external / 1024 / 1024).toFixed(2)} MB`
    });

    // Trigger garbage collection if memory usage is high
    if (memAfter.heapUsed > 500 * 1024 * 1024) { // 500MB threshold
      if (global.gc) {
        console.log('Triggering garbage collection due to high memory usage');
        global.gc();
      }
    }
  });

  next();
};

module.exports = {
  createStreamingUpload,
  createMemoryEfficientUpload,
  trackUploadProgress,
  monitorMemoryUsage,
  StreamingStorage
};
