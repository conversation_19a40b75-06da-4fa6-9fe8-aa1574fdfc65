# 🚀 WhatsApp Notification System - Setup Instructions

## Current Status
✅ **Code Implementation**: Complete  
✅ **Database Schema**: Ready  
✅ **Environment Template**: Created  
⏳ **Twilio Configuration**: Needs your credentials  
⏳ **Database Setup**: Needs to be run  
⏳ **Testing**: Ready after configuration  

## Step-by-Step Setup Guide

### 1. 📱 Set up Twilio WhatsApp

#### A. Create Twilio Account
1. Go to [https://www.twilio.com](https://www.twilio.com)
2. Click "Sign up" and create a free account
3. Verify your phone number
4. Complete the account setup

#### B. Get Your Credentials
1. Go to [Twi<PERSON> Console](https://console.twilio.com)
2. On the dashboard, you'll see:
   - **Account SID** (starts with AC...)
   - **Auth Token** (click to reveal)
3. Copy both values

#### C. Set up WhatsApp Sandbox (for testing)
1. In Twi<PERSON> Console, go to **Messaging** → **Try it out** → **Send a WhatsApp message**
2. You'll see instructions like:
   ```
   Send "join <sandbox-code>" to ****** 523 8886
   ```
3. Send this message from your WhatsApp to join the sandbox
4. Note the sandbox number (usually `+***********`)

### 2. ⚙️ Configure Environment Variables

Open your `backend/.env` file and replace the placeholder values:

```bash
# Replace these with your actual Twilio credentials:
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_WHATSAPP_FROM=whatsapp:+***********

# Update admin numbers with your actual phone numbers:
WHATSAPP_ADMIN_NUMBERS=+************,+************
```

**Important**: 
- Use international format for phone numbers: `+************`
- Include the country code
- No spaces or special characters except `+`

### 3. 🗄️ Set up Database

#### Option A: Using the API (Recommended)
1. Start your server:
   ```bash
   cd backend
   npm run dev
   ```

2. In another terminal, run database setup:
   ```bash
   curl -X POST http://localhost:3001/api/setup/database
   ```

#### Option B: Manual Database Setup
If you prefer to run SQL directly:

1. Connect to your PostgreSQL database
2. Run the migration file:
   ```bash
   psql -d esign -f src/database/migrations/005_add_phone_and_notifications.sql
   ```

### 4. 🧪 Test the Setup

#### A. Initialize and Test
```bash
cd backend
node src/scripts/initializeNotifications.js
```

This will check:
- ✅ Environment variables
- ✅ Twilio connection
- ✅ Database tables
- ✅ System health

#### B. Start the Server
```bash
npm run dev
```

#### C. Test Notification System
1. **Register/Login** to get a JWT token
2. **Update your profile** with a phone number:
   ```bash
   curl -X PUT http://localhost:3001/api/auth/profile \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "phoneNumber": "+************",
       "whatsappNotificationsEnabled": true
     }'
   ```

3. **Test notifications**:
   ```bash
   curl -X POST http://localhost:3001/api/notifications/test \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json"
   ```

4. **Sign a document** to trigger automatic notifications

### 5. 📊 Monitor the System

#### Check System Health
```bash
curl http://localhost:3001/api/notifications/health
```

#### View Notification Statistics
```bash
curl http://localhost:3001/api/notifications/admin/stats
```

#### Check Your Notification History
```bash
curl http://localhost:3001/api/notifications/history \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🔧 Configuration Options

### Environment Variables Reference

| Variable | Description | Example |
|----------|-------------|---------|
| `TWILIO_ACCOUNT_SID` | Your Twilio Account SID | `ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` |
| `TWILIO_AUTH_TOKEN` | Your Twilio Auth Token | `your_auth_token_here` |
| `TWILIO_WHATSAPP_FROM` | WhatsApp sender number | `whatsapp:+***********` |
| `WHATSAPP_NOTIFICATIONS_ENABLED` | Enable/disable system | `true` |
| `WHATSAPP_ADMIN_NUMBERS` | Admin notification numbers | `+************,+************` |
| `WHATSAPP_RETRY_ATTEMPTS` | Number of retry attempts | `3` |
| `WHATSAPP_RETRY_DELAY` | Delay between retries (ms) | `5000` |

### User Notification Preferences

Users can configure their preferences via the profile API:

```json
{
  "whatsappNotificationsEnabled": true,
  "notificationPreferences": {
    "document_signed": true,      // Notify when document is signed
    "document_uploaded": false,   // Notify when document is uploaded  
    "admin_notifications": true   // Include in admin notifications
  }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **"Missing required environment variables"**
   - Check your `.env` file has the Twilio credentials
   - Restart the server after updating `.env`

2. **"Twilio connection test failed"**
   - Verify your Account SID and Auth Token
   - Check your Twilio account status
   - Ensure you have WhatsApp enabled in Twilio

3. **"notification_logs table not found"**
   - Run the database setup: `POST /api/setup/database`
   - Or manually run the migration SQL file

4. **"No valid recipients found"**
   - Add a phone number to your user profile
   - Ensure phone number is in international format
   - Check admin numbers in environment variables

5. **Messages not delivered**
   - For sandbox: ensure recipient joined the sandbox
   - Check Twilio console for delivery status
   - Verify phone numbers are correct

### Debug Commands

```bash
# Check if server is running
curl http://localhost:3001/health

# Check notification system health
curl http://localhost:3001/api/notifications/health

# Validate phone number format
curl -X POST http://localhost:3001/api/notifications/validate-phone \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber": "+************"}'
```

## 🎯 Next Steps After Setup

1. **Production Setup**: Apply for WhatsApp Business API approval
2. **User Onboarding**: Add phone numbers to user profiles
3. **Monitoring**: Set up health check monitoring
4. **Customization**: Modify notification templates if needed
5. **Scaling**: Consider message queuing for high volume

## 📞 Support

- Check the server logs for detailed error messages
- Use the health check endpoint to verify system status
- Test with the notification test endpoint before production
- Review `WHATSAPP_NOTIFICATION_SETUP.md` for detailed technical documentation

---

**Ready to proceed?** Follow the steps above, and you'll have WhatsApp notifications working with your Arabic e-signature system! 🇸🇦
