const express = require('express');
const { query } = require('../models/database');
const { authenticateToken } = require('../middleware/auth');
const { requirePermission } = require('../middleware/roleAuth');

const router = express.Router();

/**
 * Store analytics events
 */
router.post('/events', async (req, res) => {
  try {
    const { sessionId, userId, events, journey } = req.body;

    if (!sessionId || !events || !Array.isArray(events)) {
      return res.status(400).json({
        success: false,
        message: 'بيانات التحليلات غير صحيحة'
      });
    }

    // Store each event
    for (const event of events) {
      await query(`
        INSERT INTO analytics_events (
          session_id, user_id, event_type, timestamp, page, data, 
          user_agent, viewport_width, viewport_height
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [
        sessionId,
        userId || null,
        event.type,
        new Date(event.timestamp),
        event.page,
        JSON.stringify(event.data),
        event.userAgent,
        event.viewport.width,
        event.viewport.height
      ]);
    }

    // Update or insert session data
    await query(`
      INSERT INTO analytics_sessions (
        session_id, user_id, start_time, current_page, referrer, 
        user_agent, language, timezone, journey_data
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      ON CONFLICT (session_id) DO UPDATE SET
        current_page = EXCLUDED.current_page,
        journey_data = EXCLUDED.journey_data,
        updated_at = NOW()
    `, [
      sessionId,
      userId || null,
      new Date(journey.startTime),
      journey.currentPage,
      journey.referrer,
      journey.userAgent,
      journey.language,
      journey.timezone,
      JSON.stringify(journey)
    ]);

    res.json({
      success: true,
      message: 'تم حفظ بيانات التحليلات بنجاح'
    });

  } catch (error) {
    console.error('Analytics storage error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في حفظ بيانات التحليلات'
    });
  }
});

/**
 * Get analytics dashboard data (admin only)
 */
router.get('/dashboard', authenticateToken, requirePermission('manage_users'), async (req, res) => {
  try {
    const { timeframe = '24h' } = req.query;
    
    let timeCondition = "timestamp >= NOW() - INTERVAL '24 hours'";
    if (timeframe === '7d') timeCondition = "timestamp >= NOW() - INTERVAL '7 days'";
    if (timeframe === '30d') timeCondition = "timestamp >= NOW() - INTERVAL '30 days'";

    // Page views
    const pageViews = await query(`
      SELECT page, COUNT(*) as views
      FROM analytics_events 
      WHERE event_type = 'page_view' AND ${timeCondition}
      GROUP BY page
      ORDER BY views DESC
      LIMIT 10
    `);

    // User actions
    const userActions = await query(`
      SELECT data->>'action' as action, COUNT(*) as count
      FROM analytics_events 
      WHERE event_type = 'user_action' AND ${timeCondition}
      GROUP BY data->>'action'
      ORDER BY count DESC
      LIMIT 10
    `);

    // Errors
    const errors = await query(`
      SELECT data->>'message' as error_message, COUNT(*) as count
      FROM analytics_events 
      WHERE event_type = 'error' AND ${timeCondition}
      GROUP BY data->>'message'
      ORDER BY count DESC
      LIMIT 10
    `);

    // Performance metrics
    const performance = await query(`
      SELECT
        AVG(CAST(data->'metrics'->>'pageLoadTime' AS numeric)) as avg_page_load,
        AVG(CAST(data->'metrics'->>'domContentLoaded' AS numeric)) as avg_dom_loaded,
        AVG(CAST(data->'metrics'->>'firstContentfulPaint' AS numeric)) as avg_fcp
      FROM analytics_events
      WHERE event_type = 'performance' AND ${timeCondition}
        AND data->'metrics'->>'pageLoadTime' IS NOT NULL
    `);

    // Session statistics
    const sessions = await query(`
      SELECT 
        COUNT(DISTINCT session_id) as total_sessions,
        COUNT(DISTINCT user_id) as unique_users,
        AVG(EXTRACT(EPOCH FROM (updated_at - start_time))) as avg_session_duration
      FROM analytics_sessions 
      WHERE start_time >= NOW() - INTERVAL '${timeframe === '24h' ? '24 hours' : timeframe === '7d' ? '7 days' : '30 days'}'
    `);

    // Browser statistics
    const browsers = await query(`
      SELECT 
        CASE 
          WHEN user_agent LIKE '%Chrome%' AND user_agent NOT LIKE '%Edg%' THEN 'Chrome'
          WHEN user_agent LIKE '%Firefox%' THEN 'Firefox'
          WHEN user_agent LIKE '%Safari%' AND user_agent NOT LIKE '%Chrome%' THEN 'Safari'
          WHEN user_agent LIKE '%Edg%' THEN 'Edge'
          ELSE 'Other'
        END as browser,
        COUNT(DISTINCT session_id) as sessions
      FROM analytics_sessions 
      WHERE start_time >= NOW() - INTERVAL '${timeframe === '24h' ? '24 hours' : timeframe === '7d' ? '7 days' : '30 days'}'
      GROUP BY browser
      ORDER BY sessions DESC
    `);

    res.json({
      success: true,
      data: {
        timeframe,
        pageViews: pageViews.rows,
        userActions: userActions.rows,
        errors: errors.rows,
        performance: performance.rows[0] || {},
        sessions: sessions.rows[0] || {},
        browsers: browsers.rows
      }
    });

  } catch (error) {
    console.error('Analytics dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب بيانات التحليلات'
    });
  }
});

/**
 * Get user journey analysis (admin only)
 */
router.get('/journey/:sessionId', authenticateToken, requirePermission('manage_users'), async (req, res) => {
  try {
    const { sessionId } = req.params;

    // Get session info
    const sessionInfo = await query(`
      SELECT * FROM analytics_sessions WHERE session_id = $1
    `, [sessionId]);

    if (sessionInfo.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'الجلسة غير موجودة'
      });
    }

    // Get all events for this session
    const events = await query(`
      SELECT * FROM analytics_events 
      WHERE session_id = $1 
      ORDER BY timestamp ASC
    `, [sessionId]);

    res.json({
      success: true,
      data: {
        session: sessionInfo.rows[0],
        events: events.rows
      }
    });

  } catch (error) {
    console.error('Journey analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحليل رحلة المستخدم'
    });
  }
});

/**
 * Get real-time analytics (admin only)
 */
router.get('/realtime', authenticateToken, requirePermission('manage_users'), async (req, res) => {
  try {
    // Active sessions (last 30 minutes)
    const activeSessions = await query(`
      SELECT COUNT(DISTINCT session_id) as count
      FROM analytics_events 
      WHERE timestamp >= NOW() - INTERVAL '30 minutes'
    `);

    // Current page views (last 5 minutes)
    const currentPageViews = await query(`
      SELECT page, COUNT(*) as views
      FROM analytics_events 
      WHERE event_type = 'page_view' 
        AND timestamp >= NOW() - INTERVAL '5 minutes'
      GROUP BY page
      ORDER BY views DESC
      LIMIT 5
    `);

    // Recent errors (last hour)
    const recentErrors = await query(`
      SELECT 
        data->>'message' as error_message,
        page,
        timestamp,
        user_id
      FROM analytics_events 
      WHERE event_type = 'error' 
        AND timestamp >= NOW() - INTERVAL '1 hour'
      ORDER BY timestamp DESC
      LIMIT 10
    `);

    // Performance alerts (slow page loads in last 10 minutes)
    const performanceAlerts = await query(`
      SELECT
        page,
        CAST(data->'metrics'->>'pageLoadTime' AS numeric) as load_time,
        timestamp
      FROM analytics_events
      WHERE event_type = 'performance'
        AND timestamp >= NOW() - INTERVAL '10 minutes'
        AND CAST(data->'metrics'->>'pageLoadTime' AS numeric) > 5000
      ORDER BY load_time DESC
      LIMIT 5
    `);

    res.json({
      success: true,
      data: {
        activeSessions: activeSessions.rows[0]?.count || 0,
        currentPageViews: currentPageViews.rows,
        recentErrors: recentErrors.rows,
        performanceAlerts: performanceAlerts.rows,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Real-time analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب التحليلات في الوقت الفعلي'
    });
  }
});

module.exports = router;
