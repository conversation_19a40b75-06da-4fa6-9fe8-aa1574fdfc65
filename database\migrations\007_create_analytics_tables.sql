-- Migration: Create analytics tables
-- Description: Create tables for user experience analytics and tracking

-- Analytics sessions table
CREATE TABLE IF NOT EXISTS analytics_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    start_time TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW(),
    current_page VARCHAR(500),
    referrer VARCHAR(1000),
    user_agent TEXT,
    language VARCHAR(10),
    timezone VARCHAR(50),
    journey_data JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Analytics events table
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    page VARCHAR(500),
    data JSONB,
    user_agent TEXT,
    viewport_width INTEGER,
    viewport_height INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_analytics_sessions_session_id ON analytics_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_sessions_user_id ON analytics_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_sessions_start_time ON analytics_sessions(start_time);

CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_events_page ON analytics_events(page);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_analytics_events_type_timestamp ON analytics_events(event_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session_timestamp ON analytics_events(session_id, timestamp);

-- Add comments for documentation
COMMENT ON TABLE analytics_sessions IS 'User sessions for analytics tracking';
COMMENT ON TABLE analytics_events IS 'Individual user events for analytics';

COMMENT ON COLUMN analytics_sessions.session_id IS 'Unique session identifier';
COMMENT ON COLUMN analytics_sessions.journey_data IS 'Complete user journey data in JSON format';
COMMENT ON COLUMN analytics_events.event_type IS 'Type of event: page_view, click, form_submit, error, performance, user_action';
COMMENT ON COLUMN analytics_events.data IS 'Event-specific data in JSON format';

-- Create a function to clean up old analytics data
CREATE OR REPLACE FUNCTION cleanup_old_analytics_data()
RETURNS void AS $$
BEGIN
    -- Delete events older than 90 days
    DELETE FROM analytics_events 
    WHERE timestamp < NOW() - INTERVAL '90 days';
    
    -- Delete sessions older than 90 days that have no recent events
    DELETE FROM analytics_sessions 
    WHERE start_time < NOW() - INTERVAL '90 days'
    AND session_id NOT IN (
        SELECT DISTINCT session_id 
        FROM analytics_events 
        WHERE timestamp >= NOW() - INTERVAL '90 days'
    );
    
    -- Log cleanup
    INSERT INTO logs (action, details) 
    VALUES ('ANALYTICS_CLEANUP', jsonb_build_object(
        'timestamp', NOW(),
        'description', 'Cleaned up analytics data older than 90 days'
    ));
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run cleanup (if pg_cron is available)
-- This would typically be set up separately in production
-- SELECT cron.schedule('analytics-cleanup', '0 2 * * 0', 'SELECT cleanup_old_analytics_data();');

-- Create a view for common analytics queries
CREATE OR REPLACE VIEW analytics_summary AS
SELECT 
    DATE_TRUNC('day', ae.timestamp) as date,
    ae.event_type,
    ae.page,
    COUNT(*) as event_count,
    COUNT(DISTINCT ae.session_id) as unique_sessions,
    COUNT(DISTINCT ae.user_id) as unique_users
FROM analytics_events ae
WHERE ae.timestamp >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', ae.timestamp), ae.event_type, ae.page
ORDER BY date DESC, event_count DESC;

COMMENT ON VIEW analytics_summary IS 'Daily summary of analytics events for the last 30 days';

-- Create a view for performance metrics
CREATE OR REPLACE VIEW performance_metrics AS
SELECT
    DATE_TRUNC('hour', timestamp) as hour,
    page,
    AVG(CAST(data->'metrics'->>'pageLoadTime' AS numeric)) as avg_page_load_time,
    AVG(CAST(data->'metrics'->>'domContentLoaded' AS numeric)) as avg_dom_content_loaded,
    AVG(CAST(data->'metrics'->>'firstContentfulPaint' AS numeric)) as avg_first_contentful_paint,
    COUNT(*) as sample_count
FROM analytics_events
WHERE event_type = 'performance'
    AND timestamp >= NOW() - INTERVAL '7 days'
    AND data->'metrics'->>'pageLoadTime' IS NOT NULL
GROUP BY DATE_TRUNC('hour', timestamp), page
ORDER BY hour DESC;

COMMENT ON VIEW performance_metrics IS 'Hourly performance metrics for the last 7 days';

-- Create a view for error tracking
CREATE OR REPLACE VIEW error_summary AS
SELECT
    DATE_TRUNC('hour', timestamp) as hour,
    page,
    data->>'message' as error_message,
    data->>'filename' as filename,
    COUNT(*) as error_count,
    COUNT(DISTINCT session_id) as affected_sessions,
    COUNT(DISTINCT user_id) as affected_users
FROM analytics_events
WHERE event_type = 'error'
    AND timestamp >= NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', timestamp), page, data->>'message', data->>'filename'
ORDER BY hour DESC, error_count DESC;

COMMENT ON VIEW error_summary IS 'Hourly error summary for the last 24 hours';
