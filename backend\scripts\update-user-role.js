const { query } = require('../src/models/database');

async function updateUserRole() {
  try {
    console.log('Updating user role to admin...');
    
    const email = '<EMAIL>';
    const newRole = 'admin';
    
    // Check if user exists
    const userCheck = await query('SELECT id, email, role FROM users WHERE email = $1', [email]);
    
    if (userCheck.rows.length === 0) {
      console.log(`❌ User with email "${email}" not found`);
      process.exit(1);
    }
    
    const user = userCheck.rows[0];
    console.log(`📧 Found user: ${user.email}`);
    console.log(`🔐 Current role: ${user.role}`);
    
    if (user.role === newRole) {
      console.log(`✅ User already has ${newRole} role`);
      process.exit(0);
    }
    
    // Update user role
    const updateResult = await query(
      'UPDATE users SET role = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2 RETURNING id, email, role, updated_at',
      [newRole, email]
    );
    
    if (updateResult.rows.length > 0) {
      const updatedUser = updateResult.rows[0];
      console.log(`✅ Successfully updated user role!`);
      console.log(`📧 Email: ${updatedUser.email}`);
      console.log(`🔐 New role: ${updatedUser.role}`);
      console.log(`⏰ Updated at: ${updatedUser.updated_at}`);
    } else {
      console.log(`❌ Failed to update user role`);
      process.exit(1);
    }
    
    // Verify the change
    console.log('\n📊 Verification:');
    const verification = await query('SELECT id, email, role, created_at, updated_at FROM users WHERE email = $1', [email]);
    const verifiedUser = verification.rows[0];
    
    console.log(`✓ User ID: ${verifiedUser.id}`);
    console.log(`✓ Email: ${verifiedUser.email}`);
    console.log(`✓ Role: ${verifiedUser.role}`);
    console.log(`✓ Created: ${verifiedUser.created_at.toISOString().split('T')[0]}`);
    console.log(`✓ Updated: ${verifiedUser.updated_at.toISOString().split('T')[0]}`);
    
    // Show all admin users
    console.log('\n👑 All Admin Users:');
    const adminUsers = await query("SELECT email, role, created_at FROM users WHERE role = 'admin' ORDER BY created_at ASC");
    adminUsers.rows.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.email} - ${admin.role} (${admin.created_at.toISOString().split('T')[0]})`);
    });
    
    console.log('\n🎉 Role update completed successfully!');
    console.log('📝 The user now has full admin access including:');
    console.log('   ✅ Upload signatures');
    console.log('   ✅ Verify documents');
    console.log('   ✅ Sign documents');
    console.log('   ✅ View history');
    console.log('   ✅ Manage users');
    console.log('   ✅ Access all features');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error updating user role:', error);
    process.exit(1);
  }
}

// Run the role update
updateUserRole();
