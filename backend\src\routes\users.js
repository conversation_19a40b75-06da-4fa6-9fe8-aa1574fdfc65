const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { documentRateLimit } = require('../middleware/documentAuth');
const {
  getUserDocuments,
  getUserDocumentStats,
  getUserAuditHistory,
  getUserRecentActivity
} = require('../controllers/userController');

// Get user's documents with pagination and filtering
router.get('/documents', 
  authenticateToken, 
  documentRateLimit(100), 
  getUserDocuments
);

// Get user's document statistics
router.get('/documents/stats', 
  authenticateToken, 
  getUserDocumentStats
);

// Get user's audit history
router.get('/audit-history', 
  authenticateToken, 
  documentRateLimit(50), 
  getUserAuditHistory
);

// Get user's recent activity summary
router.get('/recent-activity', 
  authenticateToken, 
  getUserRecentActivity
);

module.exports = router;
