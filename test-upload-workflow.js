const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testUploadWorkflow() {
  try {
    console.log('🧪 Testing complete upload workflow...\n');

    // 1. Login as regular user
    console.log('1. Logging in as regular user...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (!loginResponse.data.token) {
      throw new Error('No token received from login');
    }

    const userToken = loginResponse.data.token;
    console.log('✅ Regular user login successful');

    // 2. Upload document for review
    console.log('\n2. Uploading document for review...');
    const formData = new FormData();
    
    // Create a simple PDF buffer for testing
    const pdfContent = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF');
    
    formData.append('document', pdfContent, {
      filename: 'test-workflow-document.pdf',
      contentType: 'application/pdf'
    });
    formData.append('notes', 'Test document for workflow verification');

    const uploadResponse = await axios.post(
      'http://localhost:3001/api/documents/upload-for-review',
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${userToken}`
        }
      }
    );

    console.log('✅ Document uploaded successfully');
    console.log('📄 Document ID:', uploadResponse.data.document.id);
    const documentId = uploadResponse.data.document.id;

    // 3. Login as admin user
    console.log('\n3. Logging in as admin user...');
    const adminLoginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (!adminLoginResponse.data.token) {
      throw new Error('No admin token received from login');
    }

    const adminToken = adminLoginResponse.data.token;
    console.log('✅ Admin login successful');

    // 4. Get pending documents
    console.log('\n4. Fetching pending documents...');
    const pendingResponse = await axios.get(
      'http://localhost:3001/api/documents/pending',
      {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      }
    );

    console.log('✅ Pending documents retrieved');
    console.log('📊 Number of pending documents:', pendingResponse.data.documents.length);
    
    // Find our uploaded document
    const ourDocument = pendingResponse.data.documents.find(doc => doc.id === documentId);
    if (ourDocument) {
      console.log('✅ Our uploaded document found in pending list');
      console.log('📝 Document details:');
      console.log('   - Filename:', ourDocument.original_filename);
      console.log('   - Status:', ourDocument.status);
      console.log('   - Uploader:', ourDocument.uploader_email);
      console.log('   - Notes:', ourDocument.notes);
    } else {
      console.log('❌ Our uploaded document not found in pending list');
    }

    console.log('\n🎉 Upload workflow test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Regular user can upload documents for review');
    console.log('✅ Documents are stored in pending_documents table');
    console.log('✅ Admin can retrieve pending documents');
    console.log('✅ File upload and database integration working correctly');

  } catch (error) {
    console.error('\n❌ Workflow test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testUploadWorkflow();
