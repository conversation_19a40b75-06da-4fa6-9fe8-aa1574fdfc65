#!/usr/bin/env node

/**
 * Comprehensive PDF Viewer Debug Script
 * Following the inspection plan from pdf_ins.md
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const API_BASE_URL = 'http://localhost:3001/api';
const FRONTEND_URL = 'http://localhost:3000';

// Test credentials and document ID (update these with actual values)
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIwZjFlYmIzZS03NWY2LTQ0ZGQtOGFiYS1iZjJlM2U4NTdhMDQiLCJpYXQiOjE3NTI2ODQ4MjcsImV4cCI6MTc1Mjc3MTIyN30.fHhH70Nf1L11XiXq6EvHzlOg71x2B-qFkmQL9PpZVS4';
const TEST_DOCUMENT_ID = '5dfdd344-43cc-4060-911c-2579571972d1';

console.log('🔍 PDF Viewer Comprehensive Debug Analysis');
console.log('==========================================\n');

// Phase 1: Frontend Component Analysis
async function phase1_frontendAnalysis() {
  console.log('📋 PHASE 1: Frontend Component Analysis');
  console.log('----------------------------------------');
  
  // Check if DocumentViewer component exists
  const documentViewerPath = path.join(__dirname, 'frontend/src/components/DocumentViewer.tsx');
  const historyPagePath = path.join(__dirname, 'frontend/src/pages/History.tsx');
  const pdfTestViewerPath = path.join(__dirname, 'frontend/src/components/PDFTestViewer.tsx');
  
  console.log('1.1 Component Files Check:');
  console.log(`   DocumentViewer.tsx: ${fs.existsSync(documentViewerPath) ? '✅ EXISTS' : '❌ MISSING'}`);
  console.log(`   History.tsx: ${fs.existsSync(historyPagePath) ? '✅ EXISTS' : '❌ MISSING'}`);
  console.log(`   PDFTestViewer.tsx: ${fs.existsSync(pdfTestViewerPath) ? '✅ EXISTS' : '❌ MISSING'}`);
  
  // Check package.json for react-pdf dependency
  const packageJsonPath = path.join(__dirname, 'frontend/package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const reactPdfVersion = packageJson.dependencies?.['react-pdf'];
    console.log(`   react-pdf version: ${reactPdfVersion || '❌ NOT FOUND'}`);
    
    const pdfJsVersion = packageJson.dependencies?.['pdfjs-dist'];
    console.log(`   pdfjs-dist version: ${pdfJsVersion || '❌ NOT FOUND'}`);
  }
  
  console.log('\n1.2 CSS Import Check:');
  if (fs.existsSync(documentViewerPath)) {
    const content = fs.readFileSync(documentViewerPath, 'utf8');
    const hasAnnotationCSS = content.includes('AnnotationLayer.css');
    const hasTextLayerCSS = content.includes('TextLayer.css');
    console.log(`   AnnotationLayer.css imported: ${hasAnnotationCSS ? '✅' : '❌'}`);
    console.log(`   TextLayer.css imported: ${hasTextLayerCSS ? '✅' : '❌'}`);
  }
  
  console.log('\n');
}

// Phase 2: Network and API Communication
async function phase2_networkAnalysis() {
  console.log('🌐 PHASE 2: Network and API Communication');
  console.log('------------------------------------------');
  
  console.log('2.1 Backend Health Check:');
  try {
    const healthResponse = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`, { timeout: 5000 });
    console.log(`   Backend health: ✅ ${healthResponse.status} - ${healthResponse.data?.status || 'OK'}`);
  } catch (error) {
    console.log(`   Backend health: ❌ ${error.message}`);
  }
  
  console.log('\n2.2 Authentication Test:');
  try {
    const authHeaders = { Authorization: `Bearer ${TEST_TOKEN}` };
    const documentsResponse = await axios.get(`${API_BASE_URL}/documents`, { 
      headers: authHeaders,
      timeout: 10000 
    });
    console.log(`   Documents API: ✅ ${documentsResponse.status} - ${documentsResponse.data.documents?.length || 0} documents`);
    
    // Find a signed document for testing
    const signedDocs = documentsResponse.data.documents?.filter(doc => doc.status === 'signed') || [];
    console.log(`   Signed documents available: ${signedDocs.length}`);
    
    if (signedDocs.length > 0) {
      const testDoc = signedDocs[0];
      console.log(`   Test document: ${testDoc.id} - ${testDoc.original_filename}`);
      
      // Test document view endpoint
      console.log('\n2.3 Document View Endpoint Test:');
      const viewUrl = `${API_BASE_URL}/documents/${testDoc.id}/view?token=${encodeURIComponent(TEST_TOKEN)}`;
      
      try {
        const viewResponse = await axios.get(viewUrl, {
          responseType: 'arraybuffer',
          timeout: 30000,
          headers: authHeaders
        });
        
        console.log(`   View endpoint: ✅ ${viewResponse.status}`);
        console.log(`   Content-Type: ${viewResponse.headers['content-type']}`);
        console.log(`   Content-Length: ${viewResponse.headers['content-length']} bytes`);
        
        // Verify PDF format
        const buffer = Buffer.from(viewResponse.data);
        const pdfHeader = buffer.slice(0, 4).toString();
        console.log(`   PDF Header: ${pdfHeader === '%PDF' ? '✅' : '❌'} "${pdfHeader}"`);
        
        // Test alternative authentication methods
        console.log('\n2.4 Alternative Auth Methods:');
        
        // Method 1: Authorization header
        try {
          const headerAuthResponse = await axios.get(`${API_BASE_URL}/documents/${testDoc.id}/view`, {
            headers: authHeaders,
            responseType: 'arraybuffer',
            timeout: 10000
          });
          console.log(`   Header auth: ✅ ${headerAuthResponse.status}`);
        } catch (error) {
          console.log(`   Header auth: ❌ ${error.response?.status || error.message}`);
        }
        
        // Method 2: Query parameter
        try {
          const queryAuthResponse = await axios.get(viewUrl, {
            responseType: 'arraybuffer',
            timeout: 10000
          });
          console.log(`   Query auth: ✅ ${queryAuthResponse.status}`);
        } catch (error) {
          console.log(`   Query auth: ❌ ${error.response?.status || error.message}`);
        }
        
      } catch (error) {
        console.log(`   View endpoint: ❌ ${error.response?.status || error.message}`);
        if (error.response?.data) {
          const errorText = Buffer.from(error.response.data).toString();
          console.log(`   Error details: ${errorText.substring(0, 200)}`);
        }
      }
    }
    
  } catch (error) {
    console.log(`   Documents API: ❌ ${error.response?.status || error.message}`);
  }
  
  console.log('\n');
}

// Phase 3: PDF.js Library Integration
async function phase3_pdfjsAnalysis() {
  console.log('📚 PHASE 3: PDF.js Library Integration');
  console.log('--------------------------------------');
  
  console.log('3.1 PDF.js CDN Accessibility:');
  
  // Test PDF.js worker URL
  const workerUrls = [
    'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js',
    'https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js',
    'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js'
  ];
  
  for (const url of workerUrls) {
    try {
      const response = await axios.head(url, { timeout: 5000 });
      console.log(`   ${url}: ✅ ${response.status}`);
    } catch (error) {
      console.log(`   ${url}: ❌ ${error.message}`);
    }
  }
  
  console.log('\n3.2 CMap and Standard Fonts:');
  const cmapUrls = [
    'https://unpkg.com/pdfjs-dist@3.11.174/cmaps/',
    'https://unpkg.com/pdfjs-dist@3.11.174/standard_fonts/'
  ];
  
  for (const url of cmapUrls) {
    try {
      const response = await axios.head(url, { timeout: 5000 });
      console.log(`   ${url}: ✅ ${response.status}`);
    } catch (error) {
      console.log(`   ${url}: ❌ ${error.message}`);
    }
  }
  
  console.log('\n');
}

// Phase 4: Browser Environment Analysis
async function phase4_browserAnalysis() {
  console.log('🌐 PHASE 4: Browser Environment Analysis');
  console.log('----------------------------------------');
  
  console.log('4.1 Frontend Accessibility:');
  try {
    const frontendResponse = await axios.get(FRONTEND_URL, { timeout: 5000 });
    console.log(`   Frontend server: ✅ ${frontendResponse.status}`);
  } catch (error) {
    console.log(`   Frontend server: ❌ ${error.message}`);
  }
  
  try {
    const historyResponse = await axios.get(`${FRONTEND_URL}/history`, { timeout: 5000 });
    console.log(`   History page: ✅ ${historyResponse.status}`);
  } catch (error) {
    console.log(`   History page: ❌ ${error.message}`);
  }
  
  console.log('\n4.2 CORS Configuration Check:');
  console.log('   Note: CORS issues can only be detected in browser console');
  console.log('   Check browser DevTools Network tab for CORS errors');
  
  console.log('\n');
}

// Phase 5: Authentication and Security
async function phase5_authAnalysis() {
  console.log('🔐 PHASE 5: Authentication and Security');
  console.log('---------------------------------------');
  
  console.log('5.1 Token Validation:');
  console.log(`   Token length: ${TEST_TOKEN.length} characters`);
  console.log(`   Token format: ${TEST_TOKEN.startsWith('eyJ') ? '✅ JWT format' : '❌ Invalid format'}`);
  
  // Decode JWT payload (without verification)
  try {
    const payload = JSON.parse(Buffer.from(TEST_TOKEN.split('.')[1], 'base64').toString());
    console.log(`   Token user ID: ${payload.userId}`);
    console.log(`   Token issued: ${new Date(payload.iat * 1000).toISOString()}`);
    console.log(`   Token expires: ${new Date(payload.exp * 1000).toISOString()}`);
    console.log(`   Token valid: ${Date.now() < payload.exp * 1000 ? '✅' : '❌ EXPIRED'}`);
  } catch (error) {
    console.log(`   Token decode: ❌ ${error.message}`);
  }
  
  console.log('\n');
}

// Main execution
async function runComprehensiveDebug() {
  try {
    await phase1_frontendAnalysis();
    await phase2_networkAnalysis();
    await phase3_pdfjsAnalysis();
    await phase4_browserAnalysis();
    await phase5_authAnalysis();
    
    console.log('🎯 SUMMARY AND RECOMMENDATIONS');
    console.log('==============================');
    console.log('1. Check browser console for JavaScript errors');
    console.log('2. Verify PDF.js worker loading in Network tab');
    console.log('3. Check for CORS errors in browser DevTools');
    console.log('4. Ensure token is valid and not expired');
    console.log('5. Test with different browsers');
    console.log('6. Compare DocumentViewer vs PDFTestViewer behavior');
    console.log('\nFor detailed browser debugging:');
    console.log('- Open browser DevTools (F12)');
    console.log('- Go to History page and try viewing a document');
    console.log('- Check Console tab for errors');
    console.log('- Check Network tab for failed requests');
    console.log('- Check Application tab for localStorage token');
    
  } catch (error) {
    console.error('Debug script error:', error);
  }
}

// Run the debug analysis
runComprehensiveDebug();
