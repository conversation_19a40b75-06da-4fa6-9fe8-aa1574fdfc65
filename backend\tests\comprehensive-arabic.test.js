const request = require('supertest');
const app = require('../src/app');
const { query } = require('../src/config/database');
const fs = require('fs').promises;
const path = require('path');

describe('Comprehensive Arabic Language Support Tests', () => {
  let authToken;
  let userId;
  let signatureId;

  beforeAll(async () => {
    // Clean up any existing test data
    await query('DELETE FROM users WHERE email LIKE $1', ['test-arabic%']);
    
    // Register a test user with Arabic email
    const registerResponse = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'testpassword123'
      });

    expect(registerResponse.status).toBe(201);
    authToken = registerResponse.body.token;
    userId = registerResponse.body.user.id;
  });

  afterAll(async () => {
    // Clean up test data
    await query('DELETE FROM users WHERE email LIKE $1', ['test-arabic%']);
  });

  describe('Arabic Authentication Flow', () => {
    test('should register user and return Arabic success message', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'testpassword123'
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('تم إنشاء الحساب بنجاح');
      expect(response.body.user.language).toBe('ar');
      expect(response.body.user.textDirection).toBe('rtl');
    });

    test('should login user and return Arabic success message', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'testpassword123'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('تم تسجيل الدخول بنجاح');
      expect(response.body.user.language).toBe('ar');
      expect(response.body.user.textDirection).toBe('rtl');
    });

    test('should return Arabic error message for invalid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('بيانات الدخول غير صحيحة');
    });
  });

  describe('Arabic Signature Upload', () => {
    test('should upload signature with Arabic filename', async () => {
      // Create a simple test image buffer
      const testImageBuffer = Buffer.from('fake-image-data');
      
      const response = await request(app)
        .post('/api/signatures/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('signature', testImageBuffer, 'توقيع-عربي.png')
        .field('filename', 'توقيع-عربي.png');

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('تم رفع التوقيع بنجاح');
      expect(response.body.signature.filename).toBe('توقيع-عربي.png');
      
      signatureId = response.body.signature.id;
    });

    test('should retrieve signatures with Arabic filenames', async () => {
      const response = await request(app)
        .get('/api/signatures')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.signatures).toHaveLength(1);
      expect(response.body.signatures[0].filename).toBe('توقيع-عربي.png');
    });

    test('should return Arabic error for invalid file type', async () => {
      const testTextBuffer = Buffer.from('not-an-image');
      
      const response = await request(app)
        .post('/api/signatures/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('signature', testTextBuffer, 'ملف-نصي.txt');

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('نوع الملف غير مدعوم');
    });
  });

  describe('Arabic Document Signing', () => {
    test('should sign PDF document with Arabic filename', async () => {
      // Create a minimal PDF buffer for testing
      const testPdfBuffer = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF');
      
      const response = await request(app)
        .post('/api/documents/sign')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('document', testPdfBuffer, 'مستند-عربي.pdf')
        .field('signatureId', signatureId)
        .field('coordinates', JSON.stringify({ x: 100, y: 100 }));

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('تم توقيع المستند بنجاح');
      expect(response.body.document.originalFilename).toBe('مستند-عربي.pdf');
      expect(response.body.document.serialNumber).toMatch(/^وثيقة-[A-F0-9]{16}$/);
      expect(response.body.document.metadata.language).toBe('ar');
      expect(response.body.document.metadata.textDirection).toBe('rtl');
    });

    test('should return Arabic error for missing signature', async () => {
      const testPdfBuffer = Buffer.from('fake-pdf-data');
      
      const response = await request(app)
        .post('/api/documents/sign')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('document', testPdfBuffer, 'مستند-بدون-توقيع.pdf');

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('معرف التوقيع مطلوب');
    });

    test('should return Arabic error for invalid PDF', async () => {
      const invalidBuffer = Buffer.from('not-a-pdf-file');
      
      const response = await request(app)
        .post('/api/documents/sign')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('document', invalidBuffer, 'ملف-غير-صحيح.pdf')
        .field('signatureId', signatureId);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('ملف PDF غير صحيح');
    });
  });

  describe('Arabic Document History', () => {
    test('should retrieve document history with Arabic metadata', async () => {
      const response = await request(app)
        .get('/api/documents')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.documents).toHaveLength(1);
      
      const document = response.body.documents[0];
      expect(document.original_filename).toBe('مستند-عربي.pdf');
      expect(document.serial_number).toMatch(/^وثيقة-[A-F0-9]{16}$/);
    });

    test('should download signed document with Arabic filename', async () => {
      // First get the document ID
      const documentsResponse = await request(app)
        .get('/api/documents')
        .set('Authorization', `Bearer ${authToken}`);

      const documentId = documentsResponse.body.documents[0].id;

      const response = await request(app)
        .get(`/api/documents/${documentId}/download`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toBe('application/pdf');
      expect(response.body).toBeDefined();
    });
  });

  describe('Arabic Text Processing Services', () => {
    test('should detect Arabic text correctly', async () => {
      const { containsArabic } = require('../src/services/arabicFontService');
      
      expect(containsArabic('مرحبا بك')).toBe(true);
      expect(containsArabic('Hello World')).toBe(true); // Arabic-only system
      expect(containsArabic('مرحبا Hello')).toBe(true);
      expect(containsArabic('')).toBe(true); // Arabic-only system
    });

    test('should detect RTL text direction', async () => {
      const { detectTextDirection } = require('../src/services/arabicFontService');
      
      expect(detectTextDirection('مرحبا بك')).toBe('rtl');
      expect(detectTextDirection('Hello World')).toBe('rtl'); // Arabic-only system
      expect(detectTextDirection('مرحبا Hello')).toBe('rtl');
    });

    test('should process bidirectional text', async () => {
      const { processBidiText } = require('../src/services/arabicFontService');
      
      const arabicText = 'مرحبا بك في النظام';
      const processedText = processBidiText(arabicText);
      
      expect(processedText).toBeDefined();
      expect(typeof processedText).toBe('string');
    });

    test('should generate Arabic serial numbers', async () => {
      const { generateSerialNumberText } = require('../src/services/multilingualTextService');
      
      const serialText = generateSerialNumberText('ESG-123456');
      expect(serialText).toContain('الرقم التسلسلي للوثيقة');
      expect(serialText).toContain('ESG-١٢٣٤٥٦');
    });

    test('should generate Arabic timestamps', async () => {
      const { generateTimestampText } = require('../src/services/multilingualTextService');
      
      const timestampText = generateTimestampText(new Date('2024-01-15T14:30:00'));
      expect(timestampText).toContain('تاريخ التوقيع');
      expect(timestampText).toMatch(/\d{4}\/\d{2}\/\d{2}/);
    });

    test('should format Arabic signature blocks', async () => {
      const { formatSignatureBlock } = require('../src/services/multilingualTextService');
      
      const signatureBlock = formatSignatureBlock('وثيقة-ABC123', new Date());
      
      expect(signatureBlock.direction).toBe('rtl');
      expect(signatureBlock.language).toBe('ar');
      expect(signatureBlock.texts).toContain('موقع رقمياً');
      expect(signatureBlock.texts.some(text => text.includes('الرقم التسلسلي للوثيقة'))).toBe(true);
    });
  });

  describe('Arabic Error Handling', () => {
    test('should return Arabic error for unauthorized access', async () => {
      const response = await request(app)
        .get('/api/signatures')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
      expect(response.body.error).toContain('غير مخول');
    });

    test('should return Arabic error for missing required fields', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>'
          // Missing password
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('مطلوب');
    });

    test('should return Arabic error for duplicate email', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>', // Already exists
          password: 'testpassword123'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('البريد الإلكتروني مستخدم بالفعل');
    });
  });

  describe('Arabic Database Integration', () => {
    test('should store and retrieve Arabic text in database', async () => {
      const arabicText = 'نص عربي للاختبار';
      
      // Insert test data
      const insertResult = await query(
        'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3) RETURNING id, action, details',
        [userId, 'اختبار_عربي', JSON.stringify({ message: arabicText })]
      );

      expect(insertResult.rows[0].action).toBe('اختبار_عربي');
      expect(insertResult.rows[0].details.message).toBe(arabicText);

      // Retrieve test data
      const selectResult = await query(
        'SELECT action, details FROM logs WHERE id = $1',
        [insertResult.rows[0].id]
      );

      expect(selectResult.rows[0].action).toBe('اختبار_عربي');
      expect(selectResult.rows[0].details.message).toBe(arabicText);
    });
  });
});
