const { query } = require('./backend/src/models/database');

async function checkAllUserPermissions() {
  try {
    console.log('🔍 Comprehensive User Permission Check...\n');
    
    // 1. Check all users and their roles
    console.log('1. All Users and Roles:');
    console.log('=' .repeat(80));
    const allUsers = await query('SELECT id, email, role, created_at FROM users ORDER BY email');
    
    allUsers.rows.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   ID: ${user.id}`);
      console.log('-'.repeat(40));
    });
    
    // 2. Check if there's a user_permissions table (database-level permissions)
    console.log('\n2. Checking for Database-Level Permission Tables:');
    try {
      const permissionTables = await query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name LIKE '%permission%'
      `);
      
      if (permissionTables.rows.length > 0) {
        console.log('📊 Permission-related tables found:');
        permissionTables.rows.forEach(table => {
          console.log(`   - ${table.table_name}`);
        });
        
        // Check user_permissions table if it exists
        try {
          const userPermissions = await query('SELECT * FROM user_permissions');
          console.log('\n📋 User-specific permissions:');
          if (userPermissions.rows.length > 0) {
            userPermissions.rows.forEach(perm => {
              console.log(`   User ${perm.user_id}: ${perm.permission_name}`);
            });
          } else {
            console.log('   No user-specific permissions found');
          }
        } catch (error) {
          console.log('   user_permissions table exists but is empty or inaccessible');
        }
      } else {
        console.log('✅ No permission tables found - using role-based permissions only');
      }
    } catch (error) {
      console.log('⚠️ Error checking permission tables:', error.message);
    }
    
    // 3. Test permission logic for ALL users
    console.log('\n3. Testing Permission Logic for ALL Users:');
    console.log('=' .repeat(80));
    
    const testPermission = (userRole, permission) => {
      const permissions = {
        admin: [
          'upload_signatures',
          'verify_documents', 
          'sign_documents',
          'view_history',
          'manage_users',
          'view_dashboard',
          'change_password'
        ],
        user: [
          'view_history', 
          'view_dashboard',
          'change_password'
        ]
      };
      
      return permissions[userRole]?.includes(permission) || false;
    };
    
    const criticalPermissions = ['sign_documents', 'verify_documents', 'manage_users'];
    
    allUsers.rows.forEach(user => {
      console.log(`\n👤 ${user.email} (Role: ${user.role}):`);
      criticalPermissions.forEach(permission => {
        const hasPermission = testPermission(user.role, permission);
        const status = hasPermission ? '✅ YES' : '❌ NO';
        const expected = user.role === 'admin' ? '✅ YES' : '❌ NO';
        const correct = (hasPermission && user.role === 'admin') || (!hasPermission && user.role === 'user');
        
        console.log(`   ${permission}: ${status} ${correct ? '✓' : '❌ WRONG!'}`);
        
        if (!correct) {
          console.log(`     ⚠️ SECURITY ISSUE: Expected ${expected} for role '${user.role}'`);
        }
      });
    });
    
    // 4. Check for any hardcoded user IDs or emails in the codebase
    console.log('\n4. Checking for Hardcoded User Overrides:');
    console.log('=' .repeat(80));
    
    // This would need to be done manually, but let's check common patterns
    const suspiciousPatterns = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    console.log('🔍 Common email patterns to check in code:');
    suspiciousPatterns.forEach(email => {
      console.log(`   - ${email}`);
    });
    
    // 5. Test actual API endpoints with different users
    console.log('\n5. API Endpoint Testing Summary:');
    console.log('=' .repeat(80));
    
    console.log('📝 Endpoints that should be ADMIN-ONLY:');
    console.log('   - GET /api/documents/pending');
    console.log('   - POST /api/documents/:id/sign-pending');
    console.log('   - POST /api/documents/:id/reject');
    console.log('   - GET /api/users (if exists)');
    
    console.log('\n📝 Endpoints that should be USER-ACCESSIBLE:');
    console.log('   - GET /api/documents (own documents)');
    console.log('   - POST /api/documents/upload');
    console.log('   - GET /api/auth/profile');
    console.log('   - POST /api/auth/change-password');
    
    // 6. Summary and recommendations
    console.log('\n6. Summary and Recommendations:');
    console.log('=' .repeat(80));
    
    const userRoleUsers = allUsers.rows.filter(u => u.role === 'user');
    const adminRoleUsers = allUsers.rows.filter(u => u.role === 'admin');
    
    console.log(`📊 User Distribution:`);
    console.log(`   - Regular users (role='user'): ${userRoleUsers.length}`);
    console.log(`   - Admin users (role='admin'): ${adminRoleUsers.length}`);
    
    console.log(`\n🔒 Security Status:`);
    
    // Check if any regular users have admin permissions
    let securityIssues = 0;
    userRoleUsers.forEach(user => {
      criticalPermissions.forEach(permission => {
        if (testPermission(user.role, permission)) {
          console.log(`❌ SECURITY ISSUE: ${user.email} (user) has '${permission}' permission`);
          securityIssues++;
        }
      });
    });
    
    if (securityIssues === 0) {
      console.log('✅ No security issues found in permission logic');
    } else {
      console.log(`❌ Found ${securityIssues} security issues that need fixing`);
    }
    
    console.log('\n🎉 Comprehensive Permission Check Completed!');
    
  } catch (error) {
    console.error('❌ Error during permission check:', error);
  }
  
  process.exit(0);
}

checkAllUserPermissions();
