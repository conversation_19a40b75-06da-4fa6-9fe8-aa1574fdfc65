# Large File Upload System - Implementation Guide

## Overview

This document describes the implementation of a robust large file upload system for the e-signature application that supports documents of any practical size without artificial restrictions while maintaining system stability and good user experience.

## Key Features Implemented

### 1. Removed Artificial Size Limits ✅
- **Backend**: Removed hardcoded file size limits from multer configurations
- **Frontend**: Updated validation to remove size restrictions
- **PDF Validation**: Only checks for minimum file size (100 bytes) to detect empty/corrupted files
- **Signature Uploads**: No size limits for signature files

### 2. Enhanced Server Configuration ✅
- **Express Body Parser**: Increased limits to 500MB for large documents
- **Server Timeouts**: Configured for large file operations (10-15 minutes)
- **Memory Management**: Optimized for handling large files efficiently
- **Rate Limiting**: Adjusted to allow more requests for file uploads

### 3. Streaming Upload Middleware ✅
- **Memory-Efficient Processing**: Uses streaming to handle large files without loading entire file into memory
- **Progress Tracking**: Real-time upload progress monitoring
- **Memory Monitoring**: Tracks memory usage and triggers garbage collection when needed
- **Automatic Cleanup**: Cleans up temporary files and manages memory

### 4. Chunked Upload Support ✅
- **Large File Handling**: Splits very large files into manageable chunks (10MB default)
- **Resume Capability**: Supports resuming interrupted uploads
- **Reliability**: Improved upload success rate for large files
- **Assembly**: Automatically assembles chunks into complete files

### 5. Upload Progress Tracking ✅
- **Real-time Progress**: Shows upload percentage, speed, and time remaining
- **User Feedback**: Clear progress indicators in Arabic
- **Error Handling**: Comprehensive error messages with retry options
- **Cancel Support**: Ability to cancel ongoing uploads

### 6. Enhanced Error Handling ✅
- **Comprehensive Error Types**: Handles memory, timeout, network, and validation errors
- **Arabic Error Messages**: User-friendly error messages in Arabic
- **Specific Error Codes**: Detailed error codes for debugging
- **Graceful Degradation**: System continues to function even with errors

### 7. Performance Monitoring ✅
- **System Health**: Real-time monitoring of memory, CPU, and disk usage
- **Upload Metrics**: Tracks upload success rates, speeds, and file sizes
- **Performance Alerts**: Alerts for large files or slow operations
- **Health Endpoints**: `/health` and `/status` endpoints for monitoring

## Technical Implementation

### Backend Components

#### 1. Streaming Upload Middleware (`/backend/src/middleware/streamingUpload.js`)
```javascript
// Memory-efficient upload configuration
const upload = createMemoryEfficientUpload({
  maxFiles: 1,
  allowedMimeTypes: ['application/pdf'],
  allowedExtensions: ['.pdf'],
  fieldSize: 100 * 1024 * 1024 // 100MB for form fields
});
```

#### 2. Chunked Upload System (`/backend/src/middleware/chunkedUpload.js`)
- Handles files in 10MB chunks
- Assembles chunks automatically
- Supports resume functionality
- Cleans up temporary files

#### 3. Enhanced Error Handling (`/backend/src/middleware/errorHandler.js`)
- Comprehensive error detection
- Arabic error messages
- Memory and timeout handling
- User-friendly responses

#### 4. Performance Monitoring (`/backend/src/middleware/performanceMonitor.js`)
- Real-time system metrics
- Upload performance tracking
- Health status reporting
- Automatic maintenance

### Frontend Components

#### 1. Upload Progress Hook (`/frontend/src/hooks/useUploadProgress.ts`)
```typescript
const { progress, uploadFile, resetProgress } = useUploadProgress();
```

#### 2. Progress Indicator (`/frontend/src/components/UploadProgress.tsx`)
- Real-time progress display
- Speed and time remaining
- Error handling
- Cancel functionality

#### 3. Chunked Upload Support
```typescript
const { uploadFileInChunks } = useChunkedUpload();
```

## Configuration

### Environment Variables
```bash
# Server timeouts (milliseconds)
UPLOAD_TIMEOUT=900000  # 15 minutes
CHUNK_TIMEOUT=300000   # 5 minutes

# Memory limits
MAX_MEMORY_USAGE=500   # MB
GC_THRESHOLD=400       # MB

# Upload settings
CHUNK_SIZE=10485760    # 10MB
MAX_CHUNKS=1000        # Maximum chunks per file
```

### Server Configuration
```javascript
// Server timeouts
server.timeout = 10 * 60 * 1000; // 10 minutes
server.keepAliveTimeout = 5 * 60 * 1000; // 5 minutes
server.headersTimeout = 6 * 60 * 1000; // 6 minutes

// Body parser limits
app.use(express.json({ limit: '500mb' }));
app.use(express.urlencoded({ extended: true, limit: '500mb' }));
```

## Usage Examples

### 1. Regular Upload (Recommended for files < 50MB)
```javascript
const response = await uploadFile(file, '/api/documents/sign', {
  signatureId: selectedSignature,
  coordinates: JSON.stringify(coordinates)
});
```

### 2. Chunked Upload (For very large files)
```javascript
const response = await uploadFileInChunks(
  file, 
  '/api/documents/sign',
  10 * 1024 * 1024, // 10MB chunks
  { signatureId, coordinates }
);
```

### 3. Progress Monitoring
```jsx
<UploadProgress 
  progress={progress} 
  fileName={file?.name}
  onCancel={() => {
    resetProgress();
    setSigning(false);
  }}
/>
```

## Performance Characteristics

### Memory Usage
- **Small Files (< 10MB)**: Standard memory usage
- **Medium Files (10-50MB)**: Streaming processing, minimal memory impact
- **Large Files (50MB+)**: Chunked processing, constant memory usage
- **Very Large Files (500MB+)**: Automatic chunking with progress tracking

### Upload Speeds
- **Local Network**: Up to disk write speed
- **Internet**: Limited by network bandwidth
- **Progress Tracking**: Real-time speed calculation
- **Optimization**: Automatic chunk size adjustment

### System Resources
- **CPU**: Minimal impact with streaming
- **Memory**: Constant usage regardless of file size
- **Disk**: Temporary storage for chunks
- **Network**: Optimized for reliability

## Monitoring and Health Checks

### Health Endpoint (`/health`)
```json
{
  "status": "healthy",
  "system": {
    "memory": { "used": 150, "percentage": 45 },
    "cpu": { "usage": 0.8 },
    "uptime": 3600
  },
  "performance": {
    "uploads": {
      "successRate": 98,
      "averageSpeed": 15.5,
      "largestFile": 250
    }
  },
  "arabic": {
    "status": "سليم",
    "message": "النظام يعمل بشكل طبيعي"
  }
}
```

### Performance Metrics
- Upload success rate
- Average upload speed
- Memory usage patterns
- Error frequency and types
- System health indicators

## Error Handling

### Common Error Scenarios
1. **File Too Large for Memory**: Automatic chunking
2. **Network Timeout**: Retry with smaller chunks
3. **Disk Space Full**: Clear error message
4. **Memory Exhaustion**: Garbage collection and streaming
5. **Network Interruption**: Resume capability

### Error Messages (Arabic)
- `الملف كبير جداً للمعالجة` - File too large for processing
- `انتهت مهلة الرفع` - Upload timeout
- `مساحة التخزين ممتلئة` - Storage full
- `انقطع الاتصال أثناء الرفع` - Connection lost during upload

## Best Practices

### For Developers
1. Always use the streaming upload middleware for document uploads
2. Implement progress tracking for better user experience
3. Handle errors gracefully with Arabic messages
4. Monitor system performance regularly
5. Use chunked uploads for files > 100MB

### For Users
1. Use stable internet connection for large files
2. Don't close browser during upload
3. Check file integrity before upload
4. Use PDF optimization tools for very large documents

## Troubleshooting

### Common Issues
1. **Upload Fails**: Check network connection and file integrity
2. **Slow Upload**: Consider chunked upload for large files
3. **Memory Errors**: System will automatically handle with streaming
4. **Timeout**: Increase timeout settings or use chunked upload

### Debugging
- Check `/health` endpoint for system status
- Monitor server logs for detailed error information
- Use browser developer tools for network issues
- Check disk space and memory usage

## Future Enhancements

### Planned Features
1. **Resume Uploads**: Complete implementation of resume functionality
2. **Compression**: Automatic PDF compression for large files
3. **Cloud Storage**: Integration with cloud storage providers
4. **Parallel Uploads**: Multiple chunk uploads simultaneously
5. **Background Processing**: Queue system for very large files

This implementation provides a robust, scalable solution for handling documents of any practical size while maintaining excellent user experience and system stability.
