import React, { useState, useEffect } from 'react';

interface BrowserInfo {
  name: string;
  version: string;
  engine: string;
  platform: string;
  mobile: boolean;
}

interface CompatibilityTest {
  name: string;
  description: string;
  test: () => boolean | Promise<boolean>;
  required: boolean;
  status: 'pending' | 'passed' | 'failed' | 'warning';
  message?: string;
}

interface BrowserCompatibilityTestProps {
  className?: string;
  onTestComplete?: (results: CompatibilityTest[]) => void;
}

const BrowserCompatibilityTest: React.FC<BrowserCompatibilityTestProps> = ({
  className = '',
  onTestComplete
}) => {
  const [browserInfo, setBrowserInfo] = useState<BrowserInfo | null>(null);
  const [tests, setTests] = useState<CompatibilityTest[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [overallScore, setOverallScore] = useState<number>(0);

  // Detect browser information
  const detectBrowser = (): BrowserInfo => {
    const ua = navigator.userAgent;
    const platform = navigator.platform;
    const mobile = /Mobile|Android|iPhone|iPad/.test(ua);

    let name = 'Unknown';
    let version = 'Unknown';
    let engine = 'Unknown';

    // Chrome
    if (ua.includes('Chrome') && !ua.includes('Edg')) {
      name = 'Chrome';
      const match = ua.match(/Chrome\/(\d+)/);
      version = match ? match[1] : 'Unknown';
      engine = 'Blink';
    }
    // Firefox
    else if (ua.includes('Firefox')) {
      name = 'Firefox';
      const match = ua.match(/Firefox\/(\d+)/);
      version = match ? match[1] : 'Unknown';
      engine = 'Gecko';
    }
    // Safari
    else if (ua.includes('Safari') && !ua.includes('Chrome')) {
      name = 'Safari';
      const match = ua.match(/Version\/(\d+)/);
      version = match ? match[1] : 'Unknown';
      engine = 'WebKit';
    }
    // Edge
    else if (ua.includes('Edg')) {
      name = 'Edge';
      const match = ua.match(/Edg\/(\d+)/);
      version = match ? match[1] : 'Unknown';
      engine = 'Blink';
    }
    // Internet Explorer
    else if (ua.includes('MSIE') || ua.includes('Trident')) {
      name = 'Internet Explorer';
      const match = ua.match(/(?:MSIE |rv:)(\d+)/);
      version = match ? match[1] : 'Unknown';
      engine = 'Trident';
    }

    return { name, version, engine, platform, mobile };
  };

  // Initialize compatibility tests
  const initializeTests = (): CompatibilityTest[] => [
    {
      name: 'ES6 Support',
      description: 'دعم JavaScript ES6',
      required: true,
      status: 'pending',
      test: () => {
        try {
          // Test arrow functions, const/let, template literals
          const testArrow = () => 'test';
          const testTemplate = `template ${testArrow()}`;
          return testTemplate === 'template test';
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Fetch API',
      description: 'دعم Fetch API للطلبات',
      required: true,
      status: 'pending',
      test: () => typeof fetch !== 'undefined'
    },
    {
      name: 'Promise Support',
      description: 'دعم JavaScript Promises',
      required: true,
      status: 'pending',
      test: () => typeof Promise !== 'undefined'
    },
    {
      name: 'Local Storage',
      description: 'دعم Local Storage',
      required: true,
      status: 'pending',
      test: () => {
        try {
          localStorage.setItem('test', 'test');
          localStorage.removeItem('test');
          return true;
        } catch {
          return false;
        }
      }
    },
    {
      name: 'PDF Support',
      description: 'دعم عرض ملفات PDF',
      required: false,
      status: 'pending',
      test: () => {
        // Check if browser can display PDF natively
        const mimeTypes = navigator.mimeTypes;
        return Array.from(mimeTypes).some(mt => mt.type === 'application/pdf');
      }
    },
    {
      name: 'WebWorkers',
      description: 'دعم Web Workers',
      required: false,
      status: 'pending',
      test: () => typeof Worker !== 'undefined'
    },
    {
      name: 'File API',
      description: 'دعم File API لرفع الملفات',
      required: true,
      status: 'pending',
      test: () => typeof File !== 'undefined' && typeof FileReader !== 'undefined'
    },
    {
      name: 'Canvas Support',
      description: 'دعم HTML5 Canvas',
      required: false,
      status: 'pending',
      test: () => {
        const canvas = document.createElement('canvas');
        return !!(canvas.getContext && canvas.getContext('2d'));
      }
    },
    {
      name: 'WebGL Support',
      description: 'دعم WebGL للرسوميات',
      required: false,
      status: 'pending',
      test: () => {
        const canvas = document.createElement('canvas');
        try {
          return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Intersection Observer',
      description: 'دعم Intersection Observer API',
      required: false,
      status: 'pending',
      test: () => typeof IntersectionObserver !== 'undefined'
    },
    {
      name: 'CSS Grid',
      description: 'دعم CSS Grid Layout',
      required: false,
      status: 'pending',
      test: () => {
        const div = document.createElement('div');
        div.style.display = 'grid';
        return div.style.display === 'grid';
      }
    },
    {
      name: 'CSS Flexbox',
      description: 'دعم CSS Flexbox',
      required: true,
      status: 'pending',
      test: () => {
        const div = document.createElement('div');
        div.style.display = 'flex';
        return div.style.display === 'flex';
      }
    }
  ];

  // Run all compatibility tests
  const runTests = async () => {
    setIsRunning(true);
    const testResults = [...tests];

    for (let i = 0; i < testResults.length; i++) {
      const test = testResults[i];
      
      try {
        const result = await test.test();
        testResults[i] = {
          ...test,
          status: result ? 'passed' : 'failed',
          message: result ? 'مدعوم' : 'غير مدعوم'
        };
      } catch (error) {
        testResults[i] = {
          ...test,
          status: 'failed',
          message: 'خطأ في الاختبار'
        };
      }

      // Update state incrementally for visual feedback
      setTests([...testResults]);
      
      // Small delay between tests for better UX
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Calculate overall score
    const passedTests = testResults.filter(t => t.status === 'passed').length;
    const score = Math.round((passedTests / testResults.length) * 100);
    setOverallScore(score);

    setIsRunning(false);
    onTestComplete?.(testResults);
  };

  // Get compatibility level based on score
  const getCompatibilityLevel = (score: number) => {
    if (score >= 90) return { level: 'ممتاز', color: 'text-green-600', bgColor: 'bg-green-100' };
    if (score >= 75) return { level: 'جيد', color: 'text-blue-600', bgColor: 'bg-blue-100' };
    if (score >= 60) return { level: 'مقبول', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    return { level: 'ضعيف', color: 'text-red-600', bgColor: 'bg-red-100' };
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>;
      case 'failed':
        return <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>;
      case 'warning':
        return <div className="w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>;
      default:
        return <div className="w-4 h-4 bg-gray-300 rounded-full animate-pulse"></div>;
    }
  };

  useEffect(() => {
    setBrowserInfo(detectBrowser());
    setTests(initializeTests());
  }, []);

  const compatibility = getCompatibilityLevel(overallScore);

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">اختبار توافق المتصفح</h2>
        <button
          onClick={runTests}
          disabled={isRunning}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? 'جاري الاختبار...' : 'تشغيل الاختبار'}
        </button>
      </div>

      {/* Browser Information */}
      {browserInfo && (
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">معلومات المتصفح</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div><span className="font-medium">المتصفح:</span> {browserInfo.name} {browserInfo.version}</div>
            <div><span className="font-medium">المحرك:</span> {browserInfo.engine}</div>
            <div><span className="font-medium">النظام:</span> {browserInfo.platform}</div>
            <div><span className="font-medium">النوع:</span> {browserInfo.mobile ? 'محمول' : 'سطح المكتب'}</div>
          </div>
        </div>
      )}

      {/* Overall Score */}
      {overallScore > 0 && (
        <div className={`${compatibility.bgColor} rounded-lg p-4 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-800">مستوى التوافق</h3>
              <p className={`text-2xl font-bold ${compatibility.color}`}>
                {overallScore}% - {compatibility.level}
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">
                {tests.filter(t => t.status === 'passed').length} من {tests.length} اختبار نجح
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Test Results */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-gray-800">نتائج الاختبارات</h3>
        {tests.map((test, index) => (
          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center space-x-3 space-x-reverse">
              {getStatusIcon(test.status)}
              <div>
                <div className="font-medium">{test.name}</div>
                <div className="text-sm text-gray-600">{test.description}</div>
              </div>
              {test.required && (
                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">مطلوب</span>
              )}
            </div>
            <div className="text-sm text-gray-600">
              {test.message || (test.status === 'pending' ? 'في الانتظار' : '')}
            </div>
          </div>
        ))}
      </div>

      {/* Recommendations */}
      {overallScore > 0 && overallScore < 75 && (
        <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-semibold text-yellow-800 mb-2">توصيات للتحسين</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• قم بتحديث متصفحك إلى أحدث إصدار</li>
            <li>• استخدم متصفحات حديثة مثل Chrome أو Firefox أو Safari</li>
            <li>• تأكد من تفعيل JavaScript</li>
            <li>• امسح ذاكرة التخزين المؤقت للمتصفح</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default BrowserCompatibilityTest;
