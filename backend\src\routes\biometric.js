const express = require('express');
const {
  generateRegistrationChallenge,
  completeRegistration,
  generateAuthenticationChallenge,
  completeAuthentication,
  getBiometricCredentials,
  removeBiometricCredential,
  updateBiometricPreferences,
  getBiometricLogs
} = require('../controllers/biometricController');
const { authenticateToken } = require('../middleware/auth');
const {
  validateChallenge,
  validateDeviceInfo,
  checkBiometricStatus,
  biometricUserRateLimit,
  validateOrigin,
  logSecurityEvent,
  requireHTTPS
} = require('../middleware/biometricSecurity');
const rateLimit = require('express-rate-limit');

const router = express.Router();

// Apply HTTPS requirement to all biometric routes
router.use(requireHTTPS);

// Rate limiting for biometric operations
const biometricRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 requests per windowMs
  message: {
    success: false,
    message: 'تم تجاوز الحد المسموح من المحاولات. حاول مرة أخرى لاحقاً'
  },
  standardHeaders: true,
  legacyHeaders: false
});

const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 authentication attempts per windowMs
  message: {
    success: false,
    message: 'تم تجاوز الحد المسموح من محاولات المصادقة. حاول مرة أخرى لاحقاً'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Public routes (no authentication required)
// Generate authentication challenge for login
router.post('/auth/challenge',
  authRateLimit,
  validateOrigin,
  validateDeviceInfo,
  generateAuthenticationChallenge
);

// Complete authentication for login
router.post('/auth/complete',
  authRateLimit,
  validateOrigin,
  validateDeviceInfo,
  validateChallenge,
  biometricUserRateLimit,
  completeAuthentication
);

// Protected routes (authentication required)
// Generate registration challenge (user must be logged in)
router.post('/register/challenge',
  authenticateToken,
  biometricRateLimit,
  validateOrigin,
  checkBiometricStatus,
  validateDeviceInfo,
  biometricUserRateLimit,
  logSecurityEvent('registration_challenge_requested'),
  generateRegistrationChallenge
);

// Complete biometric registration
router.post('/register/complete',
  authenticateToken,
  biometricRateLimit,
  validateOrigin,
  checkBiometricStatus,
  validateDeviceInfo,
  validateChallenge,
  biometricUserRateLimit,
  completeRegistration
);

// Get user's biometric credentials
router.get('/credentials', authenticateToken, getBiometricCredentials);

// Remove a biometric credential
router.delete('/credentials/:credentialId', authenticateToken, removeBiometricCredential);

// Update biometric preferences
router.put('/preferences', authenticateToken, updateBiometricPreferences);

// Get biometric authentication logs
router.get('/logs', authenticateToken, getBiometricLogs);

// Health check endpoint for biometric service
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'خدمة المصادقة البيومترية تعمل بشكل طبيعي',
    timestamp: new Date().toISOString(),
    webauthn: {
      supported: true,
      version: '1.0.0'
    }
  });
});

module.exports = router;
