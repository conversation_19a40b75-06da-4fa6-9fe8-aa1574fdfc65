const multer = require('multer');

// Enhanced error handler for large file uploads
const uploadErrorHandler = (err, req, res, next) => {
  console.error('Upload error occurred:', {
    error: err.message,
    code: err.code,
    type: err.type,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    file: req.file ? {
      originalname: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    } : 'No file info'
  });

  // Handle multer-specific errors
  if (err instanceof multer.MulterError) {
    switch (err.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(413).json({
          success: false,
          error: 'الملف كبير جداً. يرجى استخدام ملف أصغر أو تجربة الرفع المجزأ.',
          code: 'FILE_TOO_LARGE',
          arabicMessage: 'حجم الملف يتجاوز الحد المسموح'
        });

      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          error: 'عدد كبير من الملفات. يُسمح بملف واحد فقط في كل مرة.',
          code: 'TOO_MANY_FILES',
          arabicMessage: 'عدد الملفات يتجاوز المسموح'
        });

      case 'LIMIT_FIELD_KEY':
        return res.status(400).json({
          success: false,
          error: 'اسم الحقل طويل جداً.',
          code: 'FIELD_NAME_TOO_LONG',
          arabicMessage: 'اسم الحقل غير صالح'
        });

      case 'LIMIT_FIELD_VALUE':
        return res.status(400).json({
          success: false,
          error: 'قيمة الحقل كبيرة جداً.',
          code: 'FIELD_VALUE_TOO_LONG',
          arabicMessage: 'قيمة الحقل تتجاوز الحد المسموح'
        });

      case 'LIMIT_FIELD_COUNT':
        return res.status(400).json({
          success: false,
          error: 'عدد كبير من الحقول.',
          code: 'TOO_MANY_FIELDS',
          arabicMessage: 'عدد الحقول يتجاوز المسموح'
        });

      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          error: 'ملف غير متوقع. يرجى التحقق من نوع الملف المرفوع.',
          code: 'UNEXPECTED_FILE',
          arabicMessage: 'نوع الملف غير مدعوم'
        });

      default:
        return res.status(400).json({
          success: false,
          error: 'خطأ في رفع الملف. يرجى المحاولة مرة أخرى.',
          code: 'UPLOAD_ERROR',
          arabicMessage: 'فشل في رفع الملف'
        });
    }
  }

  // Handle payload too large errors
  if (err.type === 'entity.too.large') {
    return res.status(413).json({
      success: false,
      error: 'حجم البيانات كبير جداً. يرجى تقسيم الملف أو استخدام ملف أصغر.',
      code: 'PAYLOAD_TOO_LARGE',
      arabicMessage: 'حجم البيانات يتجاوز الحد المسموح'
    });
  }

  // Handle timeout errors
  if (err.code === 'ETIMEDOUT' || err.timeout || err.message.includes('timeout')) {
    return res.status(408).json({
      success: false,
      error: 'انتهت مهلة الرفع. يرجى المحاولة مرة أخرى أو استخدام الرفع المجزأ للملفات الكبيرة.',
      code: 'UPLOAD_TIMEOUT',
      arabicMessage: 'انتهت مهلة العملية'
    });
  }

  // Handle memory errors
  if (err.message && (
    err.message.includes('out of memory') ||
    err.message.includes('Cannot allocate memory') ||
    err.message.includes('ENOMEM')
  )) {
    return res.status(507).json({
      success: false,
      error: 'الملف كبير جداً للمعالجة. يرجى استخدام ملف أصغر أو تجربة الرفع المجزأ.',
      code: 'INSUFFICIENT_MEMORY',
      arabicMessage: 'الذاكرة غير كافية لمعالجة الملف'
    });
  }

  // Handle disk space errors
  if (err.code === 'ENOSPC' || err.message.includes('no space left')) {
    return res.status(507).json({
      success: false,
      error: 'مساحة التخزين ممتلئة. يرجى المحاولة لاحقاً.',
      code: 'INSUFFICIENT_STORAGE',
      arabicMessage: 'مساحة التخزين غير كافية'
    });
  }

  // Handle file system errors
  if (err.code === 'ENOENT') {
    return res.status(404).json({
      success: false,
      error: 'الملف غير موجود.',
      code: 'FILE_NOT_FOUND',
      arabicMessage: 'الملف المطلوب غير موجود'
    });
  }

  if (err.code === 'EACCES' || err.code === 'EPERM') {
    return res.status(403).json({
      success: false,
      error: 'ليس لديك صلاحية للوصول إلى هذا الملف.',
      code: 'ACCESS_DENIED',
      arabicMessage: 'ليس لديك صلاحية للوصول'
    });
  }

  // Handle network errors
  if (err.code === 'ECONNRESET' || err.code === 'ECONNABORTED') {
    return res.status(503).json({
      success: false,
      error: 'انقطع الاتصال أثناء الرفع. يرجى المحاولة مرة أخرى.',
      code: 'CONNECTION_LOST',
      arabicMessage: 'انقطع الاتصال'
    });
  }

  // Handle validation errors
  if (err.message.includes('Only PDF files are allowed') || 
      err.message.includes('File type not allowed')) {
    return res.status(400).json({
      success: false,
      error: 'نوع الملف غير مدعوم. يُسمح فقط بملفات PDF.',
      code: 'INVALID_FILE_TYPE',
      arabicMessage: 'نوع الملف غير صالح'
    });
  }

  // Handle encryption/decryption errors
  if (err.message.includes('Encryption') || err.message.includes('Decryption')) {
    return res.status(500).json({
      success: false,
      error: 'خطأ في تشفير/فك تشفير الملف. يرجى المحاولة مرة أخرى.',
      code: 'ENCRYPTION_ERROR',
      arabicMessage: 'خطأ في التشفير'
    });
  }

  // Handle PDF processing errors
  if (err.message.includes('PDF') || err.message.includes('pdf-lib')) {
    return res.status(400).json({
      success: false,
      error: 'ملف PDF تالف أو غير صالح. يرجى التحقق من الملف والمحاولة مرة أخرى.',
      code: 'INVALID_PDF',
      arabicMessage: 'ملف PDF غير صالح'
    });
  }

  // Handle database errors
  if (err.code === '23505') { // PostgreSQL unique violation
    return res.status(409).json({
      success: false,
      error: 'هذا الملف موجود بالفعل.',
      code: 'DUPLICATE_FILE',
      arabicMessage: 'الملف موجود مسبقاً'
    });
  }

  if (err.code && err.code.startsWith('23')) { // Other PostgreSQL constraint violations
    return res.status(400).json({
      success: false,
      error: 'خطأ في قاعدة البيانات. يرجى التحقق من البيانات المدخلة.',
      code: 'DATABASE_CONSTRAINT_ERROR',
      arabicMessage: 'خطأ في قاعدة البيانات'
    });
  }

  // Default error response
  console.error('Unhandled error:', err);
  return res.status(500).json({
    success: false,
    error: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.',
    code: 'INTERNAL_ERROR',
    arabicMessage: 'خطأ داخلي في الخادم',
    ...(process.env.NODE_ENV === 'development' && { details: err.message })
  });
};

// Memory monitoring middleware
const memoryMonitor = (req, res, next) => {
  const memUsage = process.memoryUsage();
  const memUsageMB = {
    rss: Math.round(memUsage.rss / 1024 / 1024),
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
    external: Math.round(memUsage.external / 1024 / 1024)
  };

  console.log('Memory usage before request:', memUsageMB);

  // Set memory warning threshold (500MB)
  if (memUsage.heapUsed > 500 * 1024 * 1024) {
    console.warn('⚠️ High memory usage detected:', memUsageMB);
    
    // Trigger garbage collection if available
    if (global.gc) {
      console.log('🗑️ Triggering garbage collection');
      global.gc();
    }
  }

  // Monitor memory after response
  res.on('finish', () => {
    const memUsageAfter = process.memoryUsage();
    const memUsageAfterMB = {
      rss: Math.round(memUsageAfter.rss / 1024 / 1024),
      heapUsed: Math.round(memUsageAfter.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsageAfter.heapTotal / 1024 / 1024),
      external: Math.round(memUsageAfter.external / 1024 / 1024)
    };

    const memDiff = {
      rss: memUsageAfterMB.rss - memUsageMB.rss,
      heapUsed: memUsageAfterMB.heapUsed - memUsageMB.heapUsed,
      heapTotal: memUsageAfterMB.heapTotal - memUsageMB.heapTotal,
      external: memUsageAfterMB.external - memUsageMB.external
    };

    console.log('Memory usage after request:', memUsageAfterMB);
    console.log('Memory difference:', memDiff);
  });

  next();
};

// Request timeout middleware for large uploads
const uploadTimeout = (timeoutMs = 10 * 60 * 1000) => { // 10 minutes default
  return (req, res, next) => {
    req.setTimeout(timeoutMs, () => {
      const error = new Error('Request timeout');
      error.code = 'ETIMEDOUT';
      error.timeout = true;
      next(error);
    });

    res.setTimeout(timeoutMs, () => {
      const error = new Error('Response timeout');
      error.code = 'ETIMEDOUT';
      error.timeout = true;
      next(error);
    });

    next();
  };
};

module.exports = {
  uploadErrorHandler,
  memoryMonitor,
  uploadTimeout
};
