const { query } = require('../src/config/database');

describe('Arabic Text Database Support', () => {
  beforeAll(async () => {
    // Ensure database connection is established
    await query('SELECT 1');
  });

  afterAll(async () => {
    // Clean up test data
    await query('DELETE FROM users WHERE email LIKE $1', ['test-arabic%']);
  });

  test('should store and retrieve Arabic text in user email field', async () => {
    const arabicEmail = 'مستخدم-تجريبي@example.com';
    const passwordHash = 'test-hash';

    // Insert Arabic text
    const insertResult = await query(
      'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING id, email',
      [arabicEmail, passwordHash]
    );

    expect(insertResult.rows).toHaveLength(1);
    expect(insertResult.rows[0].email).toBe(arabicEmail);

    // Retrieve Arabic text
    const selectResult = await query(
      'SELECT email FROM users WHERE id = $1',
      [insertResult.rows[0].id]
    );

    expect(selectResult.rows).toHaveLength(1);
    expect(selectResult.rows[0].email).toBe(arabicEmail);
  });

  test('should store and retrieve Arabic text in document filenames', async () => {
    // First create a test user
    const userResult = await query(
      'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING id',
      ['<EMAIL>', 'test-hash']
    );
    const userId = userResult.rows[0].id;

    const arabicFilename = 'مستند-عربي-للتوقيع.pdf';
    const arabicSignedFilename = 'مستند-عربي-موقع.pdf';
    const serialNumber = 'وثيقة-123456';

    // Insert document with Arabic filenames
    const insertResult = await query(
      `INSERT INTO documents (user_id, original_filename, signed_filename, serial_number, file_path, file_size) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING id, original_filename, signed_filename, serial_number`,
      [userId, arabicFilename, arabicSignedFilename, serialNumber, '/test/path', 1024]
    );

    expect(insertResult.rows).toHaveLength(1);
    expect(insertResult.rows[0].original_filename).toBe(arabicFilename);
    expect(insertResult.rows[0].signed_filename).toBe(arabicSignedFilename);
    expect(insertResult.rows[0].serial_number).toBe(serialNumber);

    // Retrieve document with Arabic filenames
    const selectResult = await query(
      'SELECT original_filename, signed_filename, serial_number FROM documents WHERE id = $1',
      [insertResult.rows[0].id]
    );

    expect(selectResult.rows).toHaveLength(1);
    expect(selectResult.rows[0].original_filename).toBe(arabicFilename);
    expect(selectResult.rows[0].signed_filename).toBe(arabicSignedFilename);
    expect(selectResult.rows[0].serial_number).toBe(serialNumber);
  });

  test('should store and retrieve Arabic text in signature filenames', async () => {
    // First create a test user
    const userResult = await query(
      'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING id',
      ['<EMAIL>', 'test-hash']
    );
    const userId = userResult.rows[0].id;

    const arabicSignatureFilename = 'توقيع-عربي.png';

    // Insert signature with Arabic filename
    const insertResult = await query(
      `INSERT INTO signatures (user_id, filename, file_path, file_size, mime_type) 
       VALUES ($1, $2, $3, $4, $5) 
       RETURNING id, filename`,
      [userId, arabicSignatureFilename, '/test/signature/path', 2048, 'image/png']
    );

    expect(insertResult.rows).toHaveLength(1);
    expect(insertResult.rows[0].filename).toBe(arabicSignatureFilename);

    // Retrieve signature with Arabic filename
    const selectResult = await query(
      'SELECT filename FROM signatures WHERE id = $1',
      [insertResult.rows[0].id]
    );

    expect(selectResult.rows).toHaveLength(1);
    expect(selectResult.rows[0].filename).toBe(arabicSignatureFilename);
  });

  test('should store and retrieve Arabic text in JSONB fields', async () => {
    // First create a test user
    const userResult = await query(
      'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING id',
      ['<EMAIL>', 'test-hash']
    );
    const userId = userResult.rows[0].id;

    const arabicMetadata = {
      description: 'وصف المستند العربي',
      notes: 'ملاحظات إضافية باللغة العربية',
      tags: ['مهم', 'عاجل', 'سري']
    };

    // Insert document with Arabic JSONB data
    const insertResult = await query(
      `INSERT INTO documents (user_id, original_filename, signed_filename, serial_number, file_path, file_size, signature_coordinates) 
       VALUES ($1, $2, $3, $4, $5, $6, $7) 
       RETURNING id, signature_coordinates`,
      [userId, 'test.pdf', 'test-signed.pdf', 'TEST-123', '/test/path', 1024, JSON.stringify(arabicMetadata)]
    );

    expect(insertResult.rows).toHaveLength(1);
    const retrievedMetadata = insertResult.rows[0].signature_coordinates;
    expect(retrievedMetadata.description).toBe(arabicMetadata.description);
    expect(retrievedMetadata.notes).toBe(arabicMetadata.notes);
    expect(retrievedMetadata.tags).toEqual(arabicMetadata.tags);

    // Query using Arabic text in JSONB
    const searchResult = await query(
      `SELECT id FROM documents WHERE signature_coordinates->>'description' = $1`,
      [arabicMetadata.description]
    );

    expect(searchResult.rows).toHaveLength(1);
    expect(searchResult.rows[0].id).toBe(insertResult.rows[0].id);
  });

  test('should handle Arabic text in log entries', async () => {
    // First create a test user
    const userResult = await query(
      'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING id',
      ['<EMAIL>', 'test-hash']
    );
    const userId = userResult.rows[0].id;

    const arabicAction = 'توقيع_مستند';
    const arabicDetails = {
      message: 'تم توقيع المستند بنجاح',
      filename: 'مستند-مهم.pdf',
      timestamp: new Date().toISOString()
    };

    // Insert log with Arabic text
    const insertResult = await query(
      `INSERT INTO logs (user_id, action, resource_type, details) 
       VALUES ($1, $2, $3, $4) 
       RETURNING id, action, details`,
      [userId, arabicAction, 'document', JSON.stringify(arabicDetails)]
    );

    expect(insertResult.rows).toHaveLength(1);
    expect(insertResult.rows[0].action).toBe(arabicAction);
    expect(insertResult.rows[0].details.message).toBe(arabicDetails.message);
    expect(insertResult.rows[0].details.filename).toBe(arabicDetails.filename);

    // Search logs by Arabic action
    const searchResult = await query(
      'SELECT id, action FROM logs WHERE action = $1',
      [arabicAction]
    );

    expect(searchResult.rows).toHaveLength(1);
    expect(searchResult.rows[0].action).toBe(arabicAction);
  });

  test('should handle mixed Arabic and English text', async () => {
    const mixedEmail = 'user-مستخدم@example.com';
    const mixedFilename = 'Document-مستند-2024.pdf';

    // Insert mixed language text
    const userResult = await query(
      'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING id, email',
      [mixedEmail, 'test-hash']
    );

    expect(userResult.rows[0].email).toBe(mixedEmail);

    const documentResult = await query(
      `INSERT INTO documents (user_id, original_filename, signed_filename, serial_number, file_path, file_size) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING original_filename`,
      [userResult.rows[0].id, mixedFilename, 'signed-' + mixedFilename, 'MIX-123', '/test/path', 1024]
    );

    expect(documentResult.rows[0].original_filename).toBe(mixedFilename);
  });

  test('should handle Arabic text sorting and comparison', async () => {
    // Create test users with Arabic names in different orders
    const arabicEmails = [
      'أحمد@example.com',
      'بسام@example.com', 
      'تامر@example.com',
      'جمال@example.com'
    ];

    // Insert users
    for (const email of arabicEmails) {
      await query(
        'INSERT INTO users (email, password_hash) VALUES ($1, $2)',
        [email, 'test-hash']
      );
    }

    // Retrieve and sort
    const sortedResult = await query(
      'SELECT email FROM users WHERE email LIKE $1 ORDER BY email',
      ['%@example.com']
    );

    // Verify we got all the Arabic emails
    const retrievedEmails = sortedResult.rows.map(row => row.email);
    for (const email of arabicEmails) {
      expect(retrievedEmails).toContain(email);
    }
  });
});
