import React, { useState } from 'react';

interface DocumentUploadProps {
  onFileSelect: (file: File) => void;
  loading?: boolean;
  error?: string;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({ onFileSelect, loading, error }) => {
  const [file, setFile] = useState<File | null>(null);
  const [dragOver, setDragOver] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile && selectedFile.type === 'application/pdf') {
      setFile(selectedFile);
      onFileSelect(selectedFile);
    } else {
      alert('يرجى اختيار ملف PDF صالح');
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragOver(false);
    
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile && droppedFile.type === 'application/pdf') {
      setFile(droppedFile);
      onFileSelect(droppedFile);
    } else {
      alert('يرجى اختيار ملف PDF صالح');
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragOver(false);
  };

  return (
    <div className="upload-container" dir="rtl">
      <h2 className="text-xl font-bold mb-4">رفع المستندات</h2>
      
      <div 
        className={`upload-area ${dragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'} 
                   ${loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          
          <label className="upload-label">
            اختر ملف PDF أو اسحبه هنا
            <input 
              type="file" 
              accept=".pdf" 
              onChange={handleFileChange}
              className="upload-input"
              disabled={loading}
            />
          </label>
          
          <p className="text-sm text-gray-500 mt-2">
            يُسمح فقط بملفات PDF (جميع الأحجام مدعومة)
          </p>
        </div>
      </div>

      {file && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <p className="text-green-700">
            <strong>الملف المحدد:</strong> {file.name}
          </p>
          <p className="text-sm text-green-600">
            الحجم: {(file.size / 1024 / 1024).toFixed(2)} ميجابايت
          </p>
        </div>
      )}

      {error && (
        <div className="error-message mt-4">
          {error}
        </div>
      )}

      {loading && (
        <div className="mt-4 text-center">
          <div className="inline-flex items-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            جاري المعالجة...
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentUpload;
