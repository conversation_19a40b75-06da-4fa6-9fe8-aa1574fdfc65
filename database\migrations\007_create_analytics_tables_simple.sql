-- Migration: Create analytics tables (simplified)
-- Description: Create basic tables for user experience analytics

-- Analytics sessions table
CREATE TABLE IF NOT EXISTS analytics_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    start_time TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW(),
    current_page VARCHAR(500),
    referrer VARCHAR(1000),
    user_agent TEXT,
    language VARCHAR(10),
    timezone VARCHAR(50),
    journey_data JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Analytics events table
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    page VARCHAR(500),
    data JSONB,
    user_agent TEXT,
    viewport_width INTEGER,
    viewport_height INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_analytics_sessions_session_id ON analytics_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_sessions_user_id ON analytics_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_sessions_start_time ON analytics_sessions(start_time);

CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_events_page ON analytics_events(page);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_analytics_events_type_timestamp ON analytics_events(event_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session_timestamp ON analytics_events(session_id, timestamp);

-- Add comments for documentation
COMMENT ON TABLE analytics_sessions IS 'User sessions for analytics tracking';
COMMENT ON TABLE analytics_events IS 'Individual user events for analytics';

COMMENT ON COLUMN analytics_sessions.session_id IS 'Unique session identifier';
COMMENT ON COLUMN analytics_sessions.journey_data IS 'Complete user journey data in JSON format';
COMMENT ON COLUMN analytics_events.event_type IS 'Type of event: page_view, click, form_submit, error, performance, user_action';
COMMENT ON COLUMN analytics_events.data IS 'Event-specific data in JSON format';
