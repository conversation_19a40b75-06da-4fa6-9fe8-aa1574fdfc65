const express = require('express');
const {
  register,
  login,
  getProfile,
  updateProfile,
  getAllUsers,
  changePassword,
  refreshTokenEndpoint
} = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.post('/register', register);
router.post('/login', login);
router.post('/refresh-token', refreshTokenEndpoint);

// Protected routes
router.get('/profile', authenticateToken, getProfile);
router.put('/profile', authenticateToken, updateProfile);
router.post('/change-password', authenticateToken, changePassword);
router.get('/users', authenticateToken, getAllUsers);

module.exports = router;
