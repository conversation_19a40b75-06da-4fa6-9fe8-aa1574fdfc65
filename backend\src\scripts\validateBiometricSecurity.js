#!/usr/bin/env node

/**
 * Biometric Security Validation Script
 * Validates the security implementation of the biometric authentication system
 */

const crypto = require('crypto');
const { query } = require('../models/database');

class BiometricSecurityValidator {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      tests: []
    };
  }

  log(level, test, message, details = null) {
    const result = {
      level,
      test,
      message,
      details,
      timestamp: new Date().toISOString()
    };
    
    this.results.tests.push(result);
    
    const colors = {
      PASS: '\x1b[32m',
      FAIL: '\x1b[31m',
      WARN: '\x1b[33m',
      INFO: '\x1b[36m',
      RESET: '\x1b[0m'
    };
    
    console.log(`${colors[level]}[${level}]${colors.RESET} ${test}: ${message}`);
    if (details) {
      console.log(`  Details: ${JSON.stringify(details, null, 2)}`);
    }
    
    if (level === 'PASS') this.results.passed++;
    else if (level === 'FAIL') this.results.failed++;
    else if (level === 'WARN') this.results.warnings++;
  }

  async validateDatabaseSchema() {
    console.log('\n=== Database Schema Validation ===');
    
    try {
      // Check if biometric tables exist
      const tables = ['biometric_credentials', 'biometric_auth_logs', 'registered_devices'];
      
      for (const table of tables) {
        const result = await query(
          `SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = $1
          )`,
          [table]
        );
        
        if (result.rows[0].exists) {
          this.log('PASS', 'Database Schema', `Table ${table} exists`);
        } else {
          this.log('FAIL', 'Database Schema', `Table ${table} missing`);
        }
      }

      // Check for proper indexes
      const indexChecks = [
        { table: 'biometric_credentials', column: 'user_id' },
        { table: 'biometric_credentials', column: 'credential_id' },
        { table: 'biometric_auth_logs', column: 'user_id' },
        { table: 'biometric_auth_logs', column: 'timestamp' },
        { table: 'registered_devices', column: 'user_id' }
      ];

      for (const check of indexChecks) {
        const result = await query(
          `SELECT EXISTS (
            SELECT FROM pg_indexes 
            WHERE tablename = $1 AND indexdef LIKE $2
          )`,
          [check.table, `%${check.column}%`]
        );
        
        if (result.rows[0].exists) {
          this.log('PASS', 'Database Indexes', `Index on ${check.table}.${check.column} exists`);
        } else {
          this.log('WARN', 'Database Indexes', `Index on ${check.table}.${check.column} missing`);
        }
      }

      // Check for sensitive data columns that shouldn't exist
      const sensitiveColumns = ['biometric_template', 'fingerprint_data', 'face_data', 'biometric_raw'];
      
      for (const table of tables) {
        const result = await query(
          `SELECT column_name FROM information_schema.columns 
           WHERE table_name = $1`,
          [table]
        );
        
        const columns = result.rows.map(row => row.column_name);
        const foundSensitive = columns.filter(col => 
          sensitiveColumns.some(sensitive => col.includes(sensitive))
        );
        
        if (foundSensitive.length === 0) {
          this.log('PASS', 'Data Privacy', `No sensitive biometric data columns in ${table}`);
        } else {
          this.log('FAIL', 'Data Privacy', `Sensitive columns found in ${table}`, foundSensitive);
        }
      }

    } catch (error) {
      this.log('FAIL', 'Database Schema', 'Database connection or query failed', error.message);
    }
  }

  async validateEncryptionStandards() {
    console.log('\n=== Encryption Standards Validation ===');
    
    // Check JWT secret strength
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      this.log('FAIL', 'JWT Security', 'JWT_SECRET environment variable not set');
    } else if (jwtSecret.length < 32) {
      this.log('FAIL', 'JWT Security', 'JWT_SECRET too short (minimum 32 characters)');
    } else if (jwtSecret === 'your-secret-key' || jwtSecret === 'secret') {
      this.log('FAIL', 'JWT Security', 'JWT_SECRET using default/weak value');
    } else {
      this.log('PASS', 'JWT Security', 'JWT_SECRET appears to be properly configured');
    }

    // Check refresh token secret
    const refreshSecret = process.env.JWT_REFRESH_SECRET;
    if (!refreshSecret) {
      this.log('WARN', 'JWT Security', 'JWT_REFRESH_SECRET not set, using JWT_SECRET');
    } else if (refreshSecret === jwtSecret) {
      this.log('WARN', 'JWT Security', 'JWT_REFRESH_SECRET same as JWT_SECRET');
    } else {
      this.log('PASS', 'JWT Security', 'JWT_REFRESH_SECRET properly configured');
    }

    // Test challenge generation entropy
    const challenges = [];
    for (let i = 0; i < 100; i++) {
      const challenge = crypto.randomBytes(32).toString('base64url');
      challenges.push(challenge);
    }

    const uniqueChallenges = new Set(challenges);
    if (uniqueChallenges.size === challenges.length) {
      this.log('PASS', 'Challenge Generation', 'All generated challenges are unique');
    } else {
      this.log('FAIL', 'Challenge Generation', 'Duplicate challenges detected');
    }

    // Check challenge length
    const challengeBuffer = Buffer.from(challenges[0], 'base64url');
    if (challengeBuffer.length >= 32) {
      this.log('PASS', 'Challenge Security', 'Challenge length meets security requirements (≥32 bytes)');
    } else {
      this.log('FAIL', 'Challenge Security', `Challenge too short: ${challengeBuffer.length} bytes`);
    }
  }

  async validateSecurityFunctions() {
    console.log('\n=== Security Functions Validation ===');
    
    try {
      // Check if security functions exist
      const functions = [
        'has_active_biometric_credentials',
        'log_biometric_event',
        'update_credential_counter',
        'deactivate_biometric_credential'
      ];

      for (const func of functions) {
        const result = await query(
          `SELECT EXISTS (
            SELECT FROM pg_proc 
            WHERE proname = $1
          )`,
          [func]
        );
        
        if (result.rows[0].exists) {
          this.log('PASS', 'Security Functions', `Function ${func} exists`);
        } else {
          this.log('FAIL', 'Security Functions', `Function ${func} missing`);
        }
      }

      // Test counter validation function
      const testUserId = crypto.randomUUID();
      const testCredentialId = crypto.randomBytes(32).toString('base64url');
      
      // Create test credential
      await query(
        'INSERT INTO biometric_credentials (user_id, credential_id, public_key, counter) VALUES ($1, $2, $3, $4)',
        [testUserId, testCredentialId, 'test-key', 100]
      );

      // Test counter update with valid increment
      const validResult = await query(
        'SELECT update_credential_counter($1, $2)',
        [testCredentialId, 150]
      );

      if (validResult.rows[0].update_credential_counter) {
        this.log('PASS', 'Counter Validation', 'Counter increment validation works');
      } else {
        this.log('FAIL', 'Counter Validation', 'Counter increment validation failed');
      }

      // Test counter update with invalid decrement (replay attack)
      const invalidResult = await query(
        'SELECT update_credential_counter($1, $2)',
        [testCredentialId, 140]
      );

      if (!invalidResult.rows[0].update_credential_counter) {
        this.log('PASS', 'Replay Protection', 'Counter decrement properly rejected');
      } else {
        this.log('FAIL', 'Replay Protection', 'Counter decrement not rejected - replay attack possible');
      }

      // Cleanup test data
      await query('DELETE FROM biometric_credentials WHERE credential_id = $1', [testCredentialId]);

    } catch (error) {
      this.log('FAIL', 'Security Functions', 'Error testing security functions', error.message);
    }
  }

  async validateAuditLogging() {
    console.log('\n=== Audit Logging Validation ===');
    
    try {
      // Check audit log structure
      const result = await query(
        `SELECT column_name, data_type FROM information_schema.columns 
         WHERE table_name = 'biometric_auth_logs'
         ORDER BY ordinal_position`
      );

      const requiredColumns = [
        'user_id', 'credential_id', 'event_type', 'event_status',
        'ip_address', 'user_agent', 'timestamp', 'device_info'
      ];

      const existingColumns = result.rows.map(row => row.column_name);
      
      for (const required of requiredColumns) {
        if (existingColumns.includes(required)) {
          this.log('PASS', 'Audit Schema', `Required column ${required} exists`);
        } else {
          this.log('FAIL', 'Audit Schema', `Required column ${required} missing`);
        }
      }

      // Check for proper constraints
      const constraintResult = await query(
        `SELECT constraint_name, constraint_type FROM information_schema.table_constraints 
         WHERE table_name = 'biometric_auth_logs'`
      );

      const hasCheckConstraints = constraintResult.rows.some(row => 
        row.constraint_type === 'CHECK'
      );

      if (hasCheckConstraints) {
        this.log('PASS', 'Audit Constraints', 'Check constraints exist for audit logs');
      } else {
        this.log('WARN', 'Audit Constraints', 'No check constraints found for audit logs');
      }

    } catch (error) {
      this.log('FAIL', 'Audit Logging', 'Error validating audit logging', error.message);
    }
  }

  async validateEnvironmentSecurity() {
    console.log('\n=== Environment Security Validation ===');
    
    // Check required environment variables
    const requiredEnvVars = [
      'JWT_SECRET',
      'DATABASE_URL',
      'WEBAUTHN_RP_ID',
      'WEBAUTHN_ORIGIN'
    ];

    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        this.log('PASS', 'Environment', `${envVar} is set`);
      } else {
        this.log('FAIL', 'Environment', `${envVar} is not set`);
      }
    }

    // Check for development/debug settings in production
    if (process.env.NODE_ENV === 'production') {
      const dangerousVars = ['DEBUG', 'VERBOSE_LOGGING'];
      
      for (const dangerousVar of dangerousVars) {
        if (process.env[dangerousVar]) {
          this.log('WARN', 'Production Security', `${dangerousVar} is set in production`);
        } else {
          this.log('PASS', 'Production Security', `${dangerousVar} not set in production`);
        }
      }
    }

    // Check HTTPS configuration
    if (process.env.NODE_ENV === 'production') {
      if (process.env.WEBAUTHN_ORIGIN && process.env.WEBAUTHN_ORIGIN.startsWith('https://')) {
        this.log('PASS', 'HTTPS Security', 'WebAuthn origin uses HTTPS');
      } else {
        this.log('FAIL', 'HTTPS Security', 'WebAuthn origin should use HTTPS in production');
      }
    }
  }

  async validateRateLimiting() {
    console.log('\n=== Rate Limiting Validation ===');
    
    // This would require testing the actual endpoints
    // For now, we'll check if rate limiting middleware is configured
    try {
      const packageJson = require('../../package.json');
      
      if (packageJson.dependencies['express-rate-limit']) {
        this.log('PASS', 'Rate Limiting', 'express-rate-limit dependency found');
      } else {
        this.log('FAIL', 'Rate Limiting', 'express-rate-limit dependency missing');
      }

    } catch (error) {
      this.log('WARN', 'Rate Limiting', 'Could not verify rate limiting configuration');
    }
  }

  async run() {
    console.log('🔒 Biometric Security Validation Starting...\n');
    
    await this.validateDatabaseSchema();
    await this.validateEncryptionStandards();
    await this.validateSecurityFunctions();
    await this.validateAuditLogging();
    await this.validateEnvironmentSecurity();
    await this.validateRateLimiting();
    
    console.log('\n=== Validation Summary ===');
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⚠️  Warnings: ${this.results.warnings}`);
    console.log(`📊 Total Tests: ${this.results.tests.length}`);
    
    if (this.results.failed > 0) {
      console.log('\n❌ Security validation FAILED. Please address the failed tests before deploying.');
      process.exit(1);
    } else if (this.results.warnings > 0) {
      console.log('\n⚠️  Security validation PASSED with warnings. Review warnings before deploying.');
      process.exit(0);
    } else {
      console.log('\n✅ Security validation PASSED. System appears secure for deployment.');
      process.exit(0);
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new BiometricSecurityValidator();
  validator.run().catch(error => {
    console.error('❌ Validation failed with error:', error);
    process.exit(1);
  });
}

module.exports = BiometricSecurityValidator;
