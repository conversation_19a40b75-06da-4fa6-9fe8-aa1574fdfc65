#!/usr/bin/env node

/**
 * Font Verification Script
 * Verifies that all font references have been updated to use Almarai
 */

const fs = require('fs');
const path = require('path');

// Files to check for font references
const filesToCheck = [
  // Frontend files
  'frontend/src/App.tsx',
  'frontend/src/index.css',
  'frontend/tailwind.config.js',
  
  // Backend files
  'backend/src/services/arabicFontService.js',
  'backend/src/services/rtlTextService.js',
  'backend/.env.example',
  'backend/src/server.js'
];

// Old font names that should no longer be present
const oldFontNames = [
  'Noto Sans Arabic',
  'NotoSansArabic',
  'Cairo',
  'Amiri',
  'Tahoma'
];

// Expected Almarai references
const expectedAlmaraiRefs = [
  'Almarai',
  'family=Almarai'
];

console.log('🔍 Verifying Almarai font implementation...\n');

let hasErrors = false;
let totalChecks = 0;
let passedChecks = 0;

// Function to check a file
function checkFile(filePath) {
  console.log(`📄 Checking: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ⚠️  File not found: ${filePath}`);
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  let fileHasErrors = false;
  
  // Check for old font references
  oldFontNames.forEach(oldFont => {
    totalChecks++;
    if (content.includes(oldFont)) {
      console.log(`   ❌ Found old font reference: "${oldFont}"`);
      hasErrors = true;
      fileHasErrors = true;
    } else {
      passedChecks++;
    }
  });
  
  // Check for Almarai references (at least one should exist in relevant files)
  const shouldHaveAlmarai = [
    'frontend/src/App.tsx',
    'frontend/src/index.css',
    'frontend/tailwind.config.js',
    'backend/src/services/arabicFontService.js',
    'backend/src/services/rtlTextService.js',
    'backend/.env.example'
  ];
  
  if (shouldHaveAlmarai.includes(filePath)) {
    totalChecks++;
    const hasAlmarai = expectedAlmaraiRefs.some(ref => content.includes(ref));
    if (hasAlmarai) {
      console.log(`   ✅ Contains Almarai reference`);
      passedChecks++;
    } else {
      console.log(`   ❌ Missing Almarai reference`);
      hasErrors = true;
      fileHasErrors = true;
    }
  }
  
  if (!fileHasErrors) {
    console.log(`   ✅ All checks passed`);
  }
  
  console.log('');
}

// Check all files
filesToCheck.forEach(checkFile);

// Summary
console.log('📊 Summary:');
console.log(`   Total checks: ${totalChecks}`);
console.log(`   Passed: ${passedChecks}`);
console.log(`   Failed: ${totalChecks - passedChecks}`);

if (hasErrors) {
  console.log('\n❌ Font verification failed! Please fix the issues above.');
  process.exit(1);
} else {
  console.log('\n✅ Font verification passed! All references updated to Almarai.');
  
  // Additional verification
  console.log('\n🎯 Additional Verification:');
  
  // Check Google Fonts URL
  const appTsxPath = 'frontend/src/App.tsx';
  if (fs.existsSync(appTsxPath)) {
    const appContent = fs.readFileSync(appTsxPath, 'utf8');
    if (appContent.includes('family=Almarai')) {
      console.log('   ✅ Google Fonts URL updated to Almarai');
    } else {
      console.log('   ❌ Google Fonts URL not updated');
    }
  }
  
  // Check Tailwind config
  const tailwindPath = 'frontend/tailwind.config.js';
  if (fs.existsSync(tailwindPath)) {
    const tailwindContent = fs.readFileSync(tailwindPath, 'utf8');
    if (tailwindContent.includes("'Almarai'")) {
      console.log('   ✅ Tailwind config updated with Almarai');
    } else {
      console.log('   ❌ Tailwind config not updated');
    }
  }
  
  // Check CSS files
  const cssPath = 'frontend/src/index.css';
  if (fs.existsSync(cssPath)) {
    const cssContent = fs.readFileSync(cssPath, 'utf8');
    const almaraiCount = (cssContent.match(/Almarai/g) || []).length;
    console.log(`   ✅ CSS file contains ${almaraiCount} Almarai references`);
  }
  
  console.log('\n🎉 Almarai font implementation is complete and verified!');
}

// Usage instructions
console.log('\n📋 Next Steps:');
console.log('1. Run the application to test font loading');
console.log('2. Check browser developer tools for font loading');
console.log('3. Verify Arabic text renders with Almarai font');
console.log('4. Test PDF generation with Almarai font');
console.log('\n💡 To run this verification again: node verify-almarai-font.js');
