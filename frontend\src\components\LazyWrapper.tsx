import React, { Suspense, ComponentType } from 'react';

interface LazyWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

const DefaultFallback: React.FC = () => (
  <div className="flex justify-center items-center min-h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    <span className="mr-3 text-gray-600">جاري التحميل...</span>
  </div>
);

const LazyWrapper: React.FC<LazyWrapperProps> = ({ 
  children, 
  fallback = <DefaultFallback />, 
  className = '' 
}) => {
  return (
    <div className={className}>
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </div>
  );
};

// Higher-order component for lazy loading
export const withLazyLoading = <P extends object>(
  Component: ComponentType<P>,
  fallback?: React.ReactNode
) => {
  const LazyComponent = React.lazy(() => Promise.resolve({ default: Component }));
  
  return (props: P) => (
    <LazyWrapper fallback={fallback}>
      <LazyComponent {...props} />
    </LazyWrapper>
  );
};

// Lazy loading with error boundary
interface LazyWithErrorBoundaryProps extends LazyWrapperProps {
  onError?: (error: Error) => void;
}

class LazyErrorBoundary extends React.Component<
  LazyWithErrorBoundaryProps,
  { hasError: boolean; error?: Error }
> {
  constructor(props: LazyWithErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy loading error:', error, errorInfo);
    this.props.onError?.(error);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center min-h-64 p-6 bg-red-50 rounded-lg">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">خطأ في التحميل</h3>
          <p className="text-red-600 text-center mb-4">
            حدث خطأ أثناء تحميل هذا المكون. يرجى المحاولة مرة أخرى.
          </p>
          <button
            onClick={() => this.setState({ hasError: false, error: undefined })}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
          >
            إعادة المحاولة
          </button>
        </div>
      );
    }

    return (
      <LazyWrapper {...this.props}>
        {this.props.children}
      </LazyWrapper>
    );
  }
}

export const LazyWithErrorBoundary: React.FC<LazyWithErrorBoundaryProps> = (props) => (
  <LazyErrorBoundary {...props} />
);

// Performance monitoring for lazy components
export const withPerformanceMonitoring = <P extends object>(
  Component: ComponentType<P>,
  componentName: string
) => {
  return React.forwardRef<any, P>((props, ref) => {
    const startTime = React.useRef<number>();
    const [renderTime, setRenderTime] = React.useState<number>();

    React.useEffect(() => {
      startTime.current = performance.now();
    }, []);

    React.useLayoutEffect(() => {
      if (startTime.current) {
        const endTime = performance.now();
        const duration = endTime - startTime.current;
        setRenderTime(duration);
        
        // Log slow renders in development
        if (process.env.NODE_ENV === 'development' && duration > 100) {
          console.warn(`Slow render detected in ${componentName}: ${duration.toFixed(2)}ms`);
        }
      }
    });

    return (
      <>
        <Component {...props} ref={ref} />
        {process.env.NODE_ENV === 'development' && renderTime && (
          <div className="text-xs text-gray-400 mt-1">
            Render time: {renderTime.toFixed(2)}ms
          </div>
        )}
      </>
    );
  });
};

// Intersection Observer for lazy loading on scroll
export const useIntersectionObserver = (
  ref: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = React.useState(false);

  React.useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        ...options
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [ref, options]);

  return isIntersecting;
};

// Lazy component that loads when visible
interface LazyOnScrollProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  rootMargin?: string;
}

export const LazyOnScroll: React.FC<LazyOnScrollProps> = ({
  children,
  fallback = <DefaultFallback />,
  className = '',
  rootMargin = '100px'
}) => {
  const ref = React.useRef<HTMLDivElement>(null);
  const isVisible = useIntersectionObserver(ref, { rootMargin });
  const [hasLoaded, setHasLoaded] = React.useState(false);

  React.useEffect(() => {
    if (isVisible && !hasLoaded) {
      setHasLoaded(true);
    }
  }, [isVisible, hasLoaded]);

  return (
    <div ref={ref} className={className}>
      {hasLoaded ? children : fallback}
    </div>
  );
};

export default LazyWrapper;
