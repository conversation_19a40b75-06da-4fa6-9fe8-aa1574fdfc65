const {
  containsArabic,
  detectTextDirection,
  processBidiText,
  getAppropriateFont,
  initializeArabicFonts
} = require('../src/services/arabicFontService');

const {
  isRTLCharacter,
  analyzeDocumentLayout,
  formatMultilingualText
} = require('../src/services/rtlTextService');

const {
  generateSerialNumberText,
  generateTimestampText,
  formatSignatureBlock,
  convertToArabicNumerals
} = require('../src/services/multilingualTextService');

describe('Arabic Font Service', () => {
  test('should detect Arabic text correctly', () => {
    expect(containsArabic('Hello World')).toBe(false);
    expect(containsArabic('مرحبا بالعالم')).toBe(true);
    expect(containsArabic('Hello مرحبا World')).toBe(true);
    expect(containsArabic('')).toBe(false);
    expect(containsArabic(null)).toBe(false);
  });

  test('should detect text direction correctly', () => {
    expect(detectTextDirection('Hello World')).toBe('ltr');
    expect(detectTextDirection('مرحبا بالعالم')).toBe('rtl');
    expect(detectTextDirection('Hello مرحبا بالعالم World')).toBe('rtl');
    expect(detectTextDirection('123 ABC')).toBe('ltr');
    expect(detectTextDirection('')).toBe('ltr');
  });

  test('should process bidirectional text', () => {
    const arabicText = 'مرحبا بالعالم';
    const mixedText = 'Hello مرحبا World';
    
    expect(processBidiText(arabicText, 'rtl')).toBeDefined();
    expect(processBidiText(mixedText, 'auto')).toBeDefined();
    expect(processBidiText('', 'rtl')).toBe('');
    expect(processBidiText(null, 'rtl')).toBe(null);
  });

  test('should get appropriate font for text', async () => {
    const englishFont = await getAppropriateFont('Hello World');
    expect(englishFont.isArabic).toBe(false);
    expect(englishFont.fontName).toBe('Helvetica');

    const arabicFont = await getAppropriateFont('مرحبا بالعالم');
    expect(arabicFont.isArabic).toBe(true);
    expect(arabicFont.fontName).toBe('NotoSansArabic');
  });
});

describe('RTL Text Service', () => {
  test('should identify RTL characters', () => {
    expect(isRTLCharacter(0x0627)).toBe(true); // Arabic letter Alef
    expect(isRTLCharacter(0x0628)).toBe(true); // Arabic letter Beh
    expect(isRTLCharacter(0x0041)).toBe(false); // Latin A
    expect(isRTLCharacter(0x0061)).toBe(false); // Latin a
  });

  test('should analyze document layout', () => {
    const arabicContent = 'هذا نص عربي للاختبار';
    const englishContent = 'This is English test content';
    
    const arabicAnalysis = analyzeDocumentLayout(arabicContent, 600, 800);
    expect(arabicAnalysis.direction).toBe('rtl');
    expect(arabicAnalysis.suggestedPositions).toHaveLength(3);
    expect(arabicAnalysis.suggestedPositions[0].x).toBe(50); // Left side for RTL

    const englishAnalysis = analyzeDocumentLayout(englishContent, 600, 800);
    expect(englishAnalysis.direction).toBe('ltr');
    expect(englishAnalysis.suggestedPositions[0].x).toBe(400); // Right side for LTR
  });

  test('should format multilingual text correctly', () => {
    const arabicText = 'النص العربي';
    const englishText = 'English Text';
    
    const formatted = formatMultilingualText(arabicText, englishText);
    expect(formatted).toContain(arabicText);
    expect(formatted).toContain(englishText);
    expect(formatted).toContain('|');
  });
});

describe('Multilingual Text Service', () => {
  test('should convert numerals correctly', () => {
    expect(convertToArabicNumerals('123')).toBe('١٢٣');
    expect(convertToArabicNumerals('2024')).toBe('٢٠٢٤');
    expect(convertToArabicNumerals('ABC123')).toBe('ABC١٢٣');
    expect(convertToArabicNumerals('')).toBe('');
  });

  test('should generate serial number text in different languages', () => {
    const serialNumber = 'ESG-1234567890ABCDEF';
    
    const englishSerial = generateSerialNumberText(serialNumber, 'en');
    expect(englishSerial).toContain('Document Serial');
    expect(englishSerial).toContain(serialNumber);

    const arabicSerial = generateSerialNumberText(serialNumber, 'ar');
    expect(arabicSerial).toContain('الرقم التسلسلي للوثيقة');

    const mixedSerial = generateSerialNumberText(serialNumber, 'mixed');
    expect(mixedSerial).toContain('Document Serial');
    expect(mixedSerial).toContain('الرقم التسلسلي');
  });

  test('should generate timestamp text in different languages', () => {
    const testDate = new Date('2024-01-15T10:30:00Z');
    
    const englishTimestamp = generateTimestampText(testDate, 'en');
    expect(englishTimestamp).toContain('Signed');
    expect(englishTimestamp).toContain('2024');

    const arabicTimestamp = generateTimestampText(testDate, 'ar');
    expect(arabicTimestamp).toContain('تم التوقيع');
    expect(arabicTimestamp).toContain('2024'); // English numerals now

    const mixedTimestamp = generateTimestampText(testDate, 'mixed');
    expect(mixedTimestamp).toContain('Signed');
    expect(mixedTimestamp).toContain('تم التوقيع');
  });

  test('should format complete signature block', () => {
    const serialNumber = 'ESG-TEST123';
    const testDate = new Date('2024-01-15T10:30:00Z');
    
    const englishBlock = formatSignatureBlock(serialNumber, testDate, {
      language: 'en',
      documentDirection: 'ltr',
      includeVerification: true
    });
    
    expect(englishBlock.texts).toHaveLength(3); // Verification, Serial, Timestamp
    expect(englishBlock.direction).toBe('ltr');
    expect(englishBlock.spacing).toHaveLength(3);

    const arabicBlock = formatSignatureBlock(serialNumber, testDate, {
      language: 'ar',
      documentDirection: 'rtl',
      includeVerification: true
    });
    
    expect(arabicBlock.texts).toHaveLength(3);
    expect(arabicBlock.direction).toBe('rtl');
    expect(arabicBlock.texts[0]).toContain('موقع رقمياً');
  });
});

describe('Integration Tests', () => {
  test('should handle mixed Arabic-English content', () => {
    const mixedText = 'Document Title: عنوان الوثيقة';
    
    expect(containsArabic(mixedText)).toBe(true);
    expect(detectTextDirection(mixedText)).toBe('rtl');
    
    const processed = processBidiText(mixedText, 'auto');
    expect(processed).toBeDefined();
  });

  test('should provide appropriate positioning for RTL documents', () => {
    const arabicContent = 'هذا مستند باللغة العربية يحتوي على نص طويل للاختبار';
    const analysis = analyzeDocumentLayout(arabicContent, 600, 800);
    
    expect(analysis.direction).toBe('rtl');
    expect(analysis.suggestedPositions[0].x).toBeLessThan(300); // Left side positioning
    expect(analysis.suggestedPositions[0].reason).toContain('RTL');
  });

  test('should handle empty or null inputs gracefully', () => {
    expect(containsArabic(null)).toBe(false);
    expect(containsArabic('')).toBe(false);
    expect(detectTextDirection('')).toBe('ltr');
    expect(processBidiText(null)).toBe(null);
    expect(generateSerialNumberText('', 'ar')).toContain('الرقم التسلسلي');
  });

  test('should maintain performance with large text inputs', () => {
    const largeArabicText = 'مرحبا بالعالم '.repeat(1000);
    const largeEnglishText = 'Hello World '.repeat(1000);
    
    const start = Date.now();
    
    expect(containsArabic(largeArabicText)).toBe(true);
    expect(containsArabic(largeEnglishText)).toBe(false);
    expect(detectTextDirection(largeArabicText)).toBe('rtl');
    expect(detectTextDirection(largeEnglishText)).toBe('ltr');
    
    const end = Date.now();
    expect(end - start).toBeLessThan(1000); // Should complete within 1 second
  });
});

describe('Error Handling', () => {
  test('should handle invalid font paths gracefully', async () => {
    // This test ensures the system doesn't crash when fonts are missing
    const result = await getAppropriateFont('مرحبا بالعالم');
    expect(result).toBeDefined();
    expect(result.fontName).toBeDefined();
  });

  test('should handle malformed coordinates', () => {
    const analysis = analyzeDocumentLayout('Test content', 0, 0);
    expect(analysis.suggestedPositions).toBeDefined();
    expect(analysis.suggestedPositions.length).toBeGreaterThan(0);
  });

  test('should handle invalid language codes', () => {
    const serialText = generateSerialNumberText('TEST123', 'invalid-lang');
    expect(serialText).toBeDefined();
    expect(serialText.length).toBeGreaterThan(0);
  });
});
