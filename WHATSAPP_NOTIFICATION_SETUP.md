# WhatsApp Notification System Setup Guide

## Overview

This guide explains how to set up and configure the WhatsApp notification system for the Arabic E-Signature application. The system automatically sends notifications when documents are successfully signed.

## Features

✅ **Automatic Notifications**: Sends WhatsApp messages when documents are signed  
✅ **Arabic/RTL Support**: Full Arabic language support with proper formatting  
✅ **Multi-recipient**: Supports user notifications + admin notifications  
✅ **Error Handling**: Comprehensive retry logic and error handling  
✅ **Audit Logging**: Complete audit trail of all notification attempts  
✅ **User Preferences**: Configurable notification preferences per user  
✅ **Admin Dashboard**: Monitoring and management endpoints  
✅ **Phone Validation**: International phone number format validation  

## Prerequisites

1. **Twilio Account**: Sign up at [https://www.twilio.com](https://www.twilio.com)
2. **WhatsApp Business API**: Enable WhatsApp messaging in your Twilio console
3. **Phone Numbers**: Valid international phone numbers for testing

## Installation & Configuration

### 1. Environment Variables

Add the following variables to your `.env` file:

```bash
# WhatsApp Notification Configuration
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_FROM=whatsapp:+***********
WHATSAPP_NOTIFICATIONS_ENABLED=true
WHATSAPP_ADMIN_NUMBERS=+************,+************
WHATSAPP_RETRY_ATTEMPTS=3
WHATSAPP_RETRY_DELAY=5000
```

### 2. Twilio Setup

1. **Create Twilio Account**: Go to [Twilio Console](https://console.twilio.com)
2. **Get Credentials**: Copy your Account SID and Auth Token
3. **Enable WhatsApp**: Go to Messaging > Try it out > Send a WhatsApp message
4. **Sandbox Setup**: Follow Twilio's WhatsApp sandbox setup instructions
5. **Production Setup**: For production, apply for WhatsApp Business API approval

### 3. Database Migration

Run the database setup to create notification tables:

```bash
# Navigate to backend directory
cd backend

# Run database setup (includes notification tables)
npm run setup-db
# OR manually run: node -e "require('./src/routes/setup').setupDatabase()"
```

### 4. Install Dependencies

The Twilio package is already included in package.json. If needed:

```bash
npm install twilio
```

## Configuration Options

### Environment Variables Explained

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `TWILIO_ACCOUNT_SID` | Your Twilio Account SID | - | Yes |
| `TWILIO_AUTH_TOKEN` | Your Twilio Auth Token | - | Yes |
| `TWILIO_WHATSAPP_FROM` | Twilio WhatsApp number | `whatsapp:+***********` | Yes |
| `WHATSAPP_NOTIFICATIONS_ENABLED` | Enable/disable notifications | `true` | No |
| `WHATSAPP_ADMIN_NUMBERS` | Comma-separated admin numbers | - | No |
| `WHATSAPP_RETRY_ATTEMPTS` | Number of retry attempts | `3` | No |
| `WHATSAPP_RETRY_DELAY` | Delay between retries (ms) | `5000` | No |

### User Notification Preferences

Users can configure their notification preferences:

```json
{
  "document_signed": true,      // Notify when document is signed
  "document_uploaded": false,   // Notify when document is uploaded
  "admin_notifications": true   // Include in admin notifications
}
```

## API Endpoints

### User Endpoints

```bash
# Test notification system
POST /api/notifications/test
{
  "message": "Custom test message (optional)"
}

# Get notification configuration
GET /api/notifications/config

# Get notification history
GET /api/notifications/history?limit=20

# Validate phone number
POST /api/notifications/validate-phone
{
  "phoneNumber": "+************"
}

# Health check
GET /api/notifications/health
```

### Admin Endpoints

```bash
# Get notification statistics
GET /api/notifications/admin/stats?timeframe=24h

# Retry failed notifications
POST /api/notifications/admin/retry-failed
{
  "maxAge": "1h",
  "maxRetries": 3
}

# Cleanup old logs
POST /api/notifications/admin/cleanup
{
  "retentionDays": 90
}
```

### User Profile Endpoints

```bash
# Update user profile with phone number
PUT /api/auth/profile
{
  "fullName": "أحمد محمد",
  "phoneNumber": "+************",
  "whatsappNotificationsEnabled": true,
  "notificationPreferences": {
    "document_signed": true,
    "document_uploaded": false,
    "admin_notifications": true
  }
}
```

## Testing

### 1. Basic Test

```bash
# Test with curl
curl -X POST http://localhost:3001/api/notifications/test \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "Test notification"}'
```

### 2. Phone Number Validation

```bash
curl -X POST http://localhost:3001/api/notifications/validate-phone \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber": "+************"}'
```

### 3. Document Signing Test

1. Upload and sign a document through the normal workflow
2. Check the server logs for notification attempts
3. Verify WhatsApp message delivery
4. Check notification history via API

## Troubleshooting

### Common Issues

1. **No notifications sent**
   - Check `WHATSAPP_NOTIFICATIONS_ENABLED=true`
   - Verify Twilio credentials
   - Check user has valid phone number
   - Verify user notification preferences

2. **Invalid phone number format**
   - Use international format: `+************`
   - Include country code
   - No spaces or special characters except `+`

3. **Twilio authentication errors**
   - Verify Account SID and Auth Token
   - Check Twilio account status
   - Ensure WhatsApp is enabled in Twilio console

4. **Messages not delivered**
   - Check Twilio delivery status
   - Verify recipient has WhatsApp
   - For sandbox: ensure recipient joined sandbox
   - For production: ensure WhatsApp Business API approval

### Debug Commands

```bash
# Check notification health
curl http://localhost:3001/api/notifications/health

# View recent failures
curl http://localhost:3001/api/notifications/admin/stats

# Check user preferences
curl http://localhost:3001/api/auth/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Log Monitoring

Monitor server logs for notification-related messages:

```bash
# Look for these log patterns:
# ✅ WhatsApp message sent successfully
# ⚠ WhatsApp notification failed
# 🔔 Sending document signed notification
# 📱 Preparing document signed notification
```

## Security Considerations

1. **API Credentials**: Store Twilio credentials securely in environment variables
2. **Phone Numbers**: Validate and sanitize phone numbers
3. **Message Content**: Avoid sending sensitive document content
4. **Rate Limiting**: API endpoints have rate limiting enabled
5. **Audit Logging**: All notification attempts are logged for security audit

## Production Deployment

1. **WhatsApp Business API**: Apply for production WhatsApp Business API
2. **Phone Number Verification**: Implement phone number verification flow
3. **Monitoring**: Set up monitoring for notification health endpoint
4. **Backup**: Configure backup notification methods (email, SMS)
5. **Scaling**: Consider message queuing for high-volume scenarios

## Support

For issues or questions:
1. Check server logs for error messages
2. Use the health check endpoint to verify system status
3. Test with the notification test endpoint
4. Review Twilio console for delivery status
