const axios = require('axios');

async function testPDFViewerComplete() {
  console.log('🔍 Complete PDF Viewer Test\n');

  try {
    // Test 1: Verify backend document endpoint
    console.log('1. Testing backend document endpoint...');
    const documentId = '5dfdd344-43cc-4060-911c-2579571972d1';
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIwZjFlYmIzZS03NWY2LTQ0ZGQtOGFiYS1iZjJlM2U4NTdhMDQiLCJpYXQiOjE3NTI2ODQ4MjcsImV4cCI6MTc1Mjc3MTIyN30.fHhH70Nf1L11XiXq6EvHzlOg71x2B-qFkmQL9PpZVS4';
    
    const documentUrl = `http://localhost:3001/api/documents/${documentId}/view?token=${encodeURIComponent(token)}`;
    
    const docResponse = await axios.get(documentUrl, {
      responseType: 'arraybuffer',
      timeout: 30000
    });
    
    console.log('✅ Backend endpoint working:');
    console.log(`   Status: ${docResponse.status}`);
    console.log(`   Content-Type: ${docResponse.headers['content-type']}`);
    console.log(`   Size: ${docResponse.headers['content-length']} bytes`);

    // Test 2: Verify PDF content
    console.log('\n2. Verifying PDF content...');
    const buffer = Buffer.from(docResponse.data);
    const pdfHeader = buffer.slice(0, 4).toString();
    console.log(`   PDF Header: ${pdfHeader}`);
    
    if (pdfHeader === '%PDF') {
      console.log('✅ Valid PDF file confirmed');
    } else {
      console.log('❌ Invalid PDF file format');
      return;
    }

    // Test 3: Check CORS headers
    console.log('\n3. Checking CORS configuration...');
    const corsHeaders = docResponse.headers;
    console.log(`   Access-Control-Allow-Origin: ${corsHeaders['access-control-allow-origin'] || 'Not set'}`);
    console.log(`   Access-Control-Allow-Methods: ${corsHeaders['access-control-allow-methods'] || 'Not set'}`);
    console.log(`   Access-Control-Allow-Headers: ${corsHeaders['access-control-allow-headers'] || 'Not set'}`);

    // Test 4: Test authentication
    console.log('\n4. Testing authentication...');
    try {
      const noAuthUrl = `http://localhost:3001/api/documents/${documentId}/view`;
      await axios.get(noAuthUrl);
      console.log('❌ Authentication bypass detected - this is a security issue');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Authentication required - security working correctly');
      } else {
        console.log('⚠️ Unexpected authentication error:', error.message);
      }
    }

    // Test 5: Test with invalid token
    console.log('\n5. Testing with invalid token...');
    try {
      const invalidTokenUrl = `http://localhost:3001/api/documents/${documentId}/view?token=invalid`;
      await axios.get(invalidTokenUrl);
      console.log('❌ Invalid token accepted - this is a security issue');
    } catch (error) {
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        console.log('✅ Invalid token rejected - security working correctly');
      } else {
        console.log('⚠️ Unexpected token validation error:', error.message);
      }
    }

    // Test 6: Test PDF.js compatibility
    console.log('\n6. Testing PDF.js compatibility...');
    
    // Simulate what PDF.js would do
    const pdfData = new Uint8Array(docResponse.data);
    
    // Check PDF version
    const versionMatch = buffer.toString('ascii', 0, 20).match(/%PDF-(\d\.\d)/);
    if (versionMatch) {
      console.log(`✅ PDF Version: ${versionMatch[1]}`);
    }
    
    // Check for basic PDF structure
    const hasXref = buffer.includes(Buffer.from('xref'));
    const hasTrailer = buffer.includes(Buffer.from('trailer'));
    const hasStartxref = buffer.includes(Buffer.from('startxref'));
    
    console.log(`   Has xref table: ${hasXref ? '✅' : '❌'}`);
    console.log(`   Has trailer: ${hasTrailer ? '✅' : '❌'}`);
    console.log(`   Has startxref: ${hasStartxref ? '✅' : '❌'}`);

    // Test 7: Frontend accessibility
    console.log('\n7. Testing frontend accessibility...');
    try {
      const frontendResponse = await axios.get('http://localhost:3000');
      console.log('✅ Frontend accessible');
      
      const historyResponse = await axios.get('http://localhost:3000/history');
      console.log('✅ History page accessible');
    } catch (error) {
      console.log('❌ Frontend not accessible:', error.message);
    }

    // Test 8: Test page accessibility
    console.log('\n8. Testing test page...');
    try {
      const testPageResponse = await axios.get('http://localhost:3000/test-pdf-viewer.html');
      console.log('✅ Test PDF viewer page accessible');
    } catch (error) {
      console.log('❌ Test PDF viewer page not accessible:', error.message);
    }

    console.log('\n📋 Test Summary:');
    console.log('✅ Backend PDF endpoint is working correctly');
    console.log('✅ PDF file is valid and properly formatted');
    console.log('✅ Authentication and security are working');
    console.log('✅ CORS headers are configured');
    console.log('✅ Frontend is accessible');

    console.log('\n🔧 Manual Testing Steps:');
    console.log('1. Open http://localhost:3000/test-pdf-viewer.html');
    console.log('2. Click "اختبار تحميل المستند" - should show success');
    console.log('3. Click "تحميل PDF" - should display the PDF');
    console.log('4. Open http://localhost:3000/history');
    console.log('5. Click "عرض" on any document - should open PDF viewer');
    console.log('6. Check browser console for any errors');

    console.log('\n🐛 If PDF viewer still not working, check:');
    console.log('- Browser console for JavaScript errors');
    console.log('- Network tab for failed requests');
    console.log('- PDF.js worker loading issues');
    console.log('- React component rendering errors');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${error.response.data}`);
    }
  }
}

// Run the test
testPDFViewerComplete().catch(console.error);
